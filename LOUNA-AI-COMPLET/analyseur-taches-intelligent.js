/**
 * ANALYSEUR DE TÂCHES INTELLIGENT POUR LOUNA-AI
 * Analyse les demandes, évalue les solutions existantes, propose la création de programmes
 */

const fs = require('fs');
const path = require('path');

class AnalyseurTachesIntelligent {
    constructor() {
        // Base de connaissances des types de tâches
        this.typesTaches = new Map([
            // Tâches de calcul
            ['calcul', {
                mots_cles: ['calcule', 'calculer', 'somme', 'multiplication', 'division', 'équation', 'formule'],
                complexite: 'faible',
                outils_existants: ['Calculator', 'Spotlight', 'Terminal'],
                peut_creer: true,
                templates: ['calculatrice', 'convertisseur', 'formule_mathematique']
            }],
            
            // Tâches de fichiers
            ['fichiers', {
                mots_cles: ['fichier', 'dossier', 'copie', 'déplace', 'renomme', 'organise', 'trie'],
                complexite: 'moyenne',
                outils_existants: ['Finder', 'Terminal', 'Automator'],
                peut_creer: true,
                templates: ['organisateur_fichiers', 'renommeur_batch', 'trieur_automatique']
            }],
            
            // Tâches de texte
            ['texte', {
                mots_cles: ['texte', 'écrit', 'rédige', 'format', 'convertit', 'traduit'],
                complexite: 'moyenne',
                outils_existants: ['TextEdit', 'Word', 'Pages'],
                peut_creer: true,
                templates: ['editeur_texte', 'convertisseur_format', 'analyseur_texte']
            }],
            
            // Tâches d'image
            ['image', {
                mots_cles: ['image', 'photo', 'redimensionne', 'convertit', 'filtre', 'retouche'],
                complexite: 'élevée',
                outils_existants: ['Preview', 'Photoshop', 'GIMP'],
                peut_creer: true,
                templates: ['redimensionneur_images', 'convertisseur_format', 'batch_processor']
            }],
            
            // Tâches réseau
            ['reseau', {
                mots_cles: ['télécharge', 'upload', 'synchronise', 'sauvegarde', 'cloud'],
                complexite: 'élevée',
                outils_existants: ['Safari', 'Dropbox', 'iCloud'],
                peut_creer: true,
                templates: ['downloader', 'sync_tool', 'backup_manager']
            }],
            
            // Tâches d'automatisation
            ['automatisation', {
                mots_cles: ['automatise', 'répète', 'planifie', 'surveille', 'déclenche'],
                complexite: 'élevée',
                outils_existants: ['Automator', 'Shortcuts', 'Cron'],
                peut_creer: true,
                templates: ['scheduler', 'watcher', 'automation_script']
            }],
            
            // Tâches de données
            ['donnees', {
                mots_cles: ['données', 'base', 'stocke', 'recherche', 'filtre', 'analyse'],
                complexite: 'élevée',
                outils_existants: ['Excel', 'Numbers', 'Database'],
                peut_creer: true,
                templates: ['gestionnaire_donnees', 'analyseur_csv', 'mini_database']
            }]
        ]);
        
        // Historique des analyses et créations
        this.historiqueAnalyses = [];
        this.programmesCrees = new Map();
        
        // Templates de programmes
        this.templates = new Map();
        this.initialiserTemplates();
    }

    // ANALYSE PRINCIPALE D'UNE TÂCHE
    async analyserTache(demande) {
        console.log(`🧠 Analyse de la tâche: "${demande}"`);
        
        try {
            // 1. Identifier le type de tâche
            const typeTache = this.identifierTypeTache(demande);
            
            // 2. Extraire les détails spécifiques
            const detailsTache = this.extraireDetailsTache(demande, typeTache);
            
            // 3. Vérifier les solutions existantes
            const solutionsExistantes = await this.verifierSolutionsExistantes(detailsTache);
            
            // 4. Évaluer la faisabilité de création
            const faisabilite = this.evaluerFaisabilite(detailsTache, typeTache);
            
            // 5. Générer la recommandation
            const recommandation = this.genererRecommandation(detailsTache, solutionsExistantes, faisabilite);
            
            // Enregistrer l'analyse
            const analyse = {
                demande_originale: demande,
                type_tache: typeTache,
                details: detailsTache,
                solutions_existantes: solutionsExistantes,
                faisabilite: faisabilite,
                recommandation: recommandation,
                timestamp: Date.now()
            };
            
            this.historiqueAnalyses.push(analyse);
            
            return {
                success: true,
                analyse: analyse,
                message: this.formaterReponseAnalyse(analyse)
            };
            
        } catch (error) {
            console.error(`❌ Erreur analyse tâche: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de l'analyse: ${error.message}`
            };
        }
    }

    // IDENTIFIER LE TYPE DE TÂCHE
    identifierTypeTache(demande) {
        const demandeLower = demande.toLowerCase();
        
        for (const [type, config] of this.typesTaches) {
            if (config.mots_cles.some(motCle => demandeLower.includes(motCle))) {
                console.log(`🎯 Type de tâche identifié: ${type}`);
                return { type, config };
            }
        }
        
        // Type générique si non identifié
        return {
            type: 'generale',
            config: {
                mots_cles: [],
                complexite: 'inconnue',
                outils_existants: [],
                peut_creer: true,
                templates: ['programme_generique']
            }
        };
    }

    // EXTRAIRE LES DÉTAILS SPÉCIFIQUES
    extraireDetailsTache(demande, typeTache) {
        const details = {
            action_principale: this.extraireActionPrincipale(demande),
            objets_cibles: this.extraireObjetsCibles(demande),
            parametres: this.extraireParametres(demande),
            contraintes: this.extraireContraintes(demande),
            format_sortie: this.extraireFormatSortie(demande)
        };
        
        console.log(`📋 Détails extraits:`, details);
        return details;
    }

    extraireActionPrincipale(demande) {
        const actions = ['calcule', 'convertit', 'organise', 'trie', 'télécharge', 'sauvegarde', 'analyse', 'crée', 'génère'];
        const demandeLower = demande.toLowerCase();
        
        for (const action of actions) {
            if (demandeLower.includes(action)) {
                return action;
            }
        }
        
        // Extraire le verbe principal
        const mots = demande.split(' ');
        return mots.find(mot => mot.endsWith('e') || mot.endsWith('er')) || 'traite';
    }

    extraireObjetsCibles(demande) {
        const objets = [];
        const motsObjets = ['fichier', 'image', 'photo', 'document', 'dossier', 'texte', 'données', 'nombre'];
        const demandeLower = demande.toLowerCase();
        
        for (const objet of motsObjets) {
            if (demandeLower.includes(objet)) {
                objets.push(objet);
            }
        }
        
        return objets.length > 0 ? objets : ['données'];
    }

    extraireParametres(demande) {
        const parametres = {};
        
        // Rechercher des nombres
        const nombres = demande.match(/\d+/g);
        if (nombres) {
            parametres.nombres = nombres.map(n => parseInt(n));
        }
        
        // Rechercher des formats
        const formats = demande.match(/\.(jpg|png|pdf|txt|doc|csv|json)/gi);
        if (formats) {
            parametres.formats = formats;
        }
        
        // Rechercher des chemins
        const chemins = demande.match(/\/[^\s]+/g);
        if (chemins) {
            parametres.chemins = chemins;
        }
        
        return parametres;
    }

    extraireContraintes(demande) {
        const contraintes = [];
        const demandeLower = demande.toLowerCase();
        
        if (demandeLower.includes('rapide')) contraintes.push('performance');
        if (demandeLower.includes('sécurisé')) contraintes.push('sécurité');
        if (demandeLower.includes('simple')) contraintes.push('simplicité');
        if (demandeLower.includes('automatique')) contraintes.push('automatisation');
        
        return contraintes;
    }

    extraireFormatSortie(demande) {
        const demandeLower = demande.toLowerCase();
        
        if (demandeLower.includes('fichier')) return 'fichier';
        if (demandeLower.includes('rapport')) return 'rapport';
        if (demandeLower.includes('liste')) return 'liste';
        if (demandeLower.includes('graphique')) return 'graphique';
        
        return 'resultat';
    }

    // VÉRIFIER LES SOLUTIONS EXISTANTES
    async verifierSolutionsExistantes(details) {
        const solutions = {
            applications_systeme: [],
            applications_tierces: [],
            scripts_existants: [],
            alternatives: []
        };
        
        // Vérifier les applications système
        const appsSysteme = ['Calculator', 'TextEdit', 'Preview', 'Finder', 'Automator'];
        for (const app of appsSysteme) {
            if (await this.verifierApplicationExiste(app)) {
                solutions.applications_systeme.push({
                    nom: app,
                    pertinence: this.calculerPertinence(app, details),
                    description: `Application système ${app}`
                });
            }
        }
        
        // Vérifier les scripts existants dans le projet
        const scriptsExistants = await this.rechercherScriptsExistants(details);
        solutions.scripts_existants = scriptsExistants;
        
        // Proposer des alternatives
        solutions.alternatives = this.proposerAlternatives(details);
        
        return solutions;
    }

    async verifierApplicationExiste(nomApp) {
        try {
            const { execSync } = require('child_process');
            const commande = `mdfind "kMDItemKind == 'Application'" | grep -i "${nomApp}" | head -1`;
            const resultat = execSync(commande, { encoding: 'utf8', timeout: 5000 });
            return resultat.trim().length > 0;
        } catch (error) {
            // Fallback: vérifier dans les dossiers standards
            const fs = require('fs');
            const dossiers = ['/Applications', '/System/Applications', '/System/Library/CoreServices'];

            for (const dossier of dossiers) {
                try {
                    const fichiers = fs.readdirSync(dossier);
                    const appTrouvee = fichiers.some(fichier =>
                        fichier.toLowerCase().includes(nomApp.toLowerCase()) &&
                        fichier.endsWith('.app')
                    );
                    if (appTrouvee) return true;
                } catch (e) {
                    // Ignorer erreurs d'accès
                }
            }
            return false;
        }
    }

    calculerPertinence(app, details) {
        // Algorithme simple de pertinence
        let score = 0;
        
        if (details.action_principale === 'calcule' && app === 'Calculator') score += 0.9;
        if (details.objets_cibles.includes('texte') && app === 'TextEdit') score += 0.8;
        if (details.objets_cibles.includes('image') && app === 'Preview') score += 0.7;
        if (details.objets_cibles.includes('fichier') && app === 'Finder') score += 0.6;
        
        return Math.min(score, 1.0);
    }

    async rechercherScriptsExistants(details) {
        // Rechercher dans le dossier de scripts créés
        const dossierScripts = path.join(__dirname, 'programmes-crees');
        const scripts = [];
        
        try {
            if (fs.existsSync(dossierScripts)) {
                const fichiers = fs.readdirSync(dossierScripts);
                
                for (const fichier of fichiers) {
                    if (fichier.endsWith('.js') || fichier.endsWith('.py')) {
                        const cheminFichier = path.join(dossierScripts, fichier);
                        const contenu = fs.readFileSync(cheminFichier, 'utf8');
                        
                        // Analyser si le script correspond à la tâche
                        const pertinence = this.analyserPertinenceScript(contenu, details);
                        
                        if (pertinence > 0.3) {
                            scripts.push({
                                nom: fichier,
                                chemin: cheminFichier,
                                pertinence: pertinence,
                                description: this.extraireDescriptionScript(contenu)
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️ Erreur recherche scripts: ${error.message}`);
        }
        
        return scripts.sort((a, b) => b.pertinence - a.pertinence);
    }

    analyserPertinenceScript(contenu, details) {
        let score = 0;
        const contenuLower = contenu.toLowerCase();
        
        // Vérifier la correspondance avec l'action principale
        if (contenuLower.includes(details.action_principale)) score += 0.4;
        
        // Vérifier la correspondance avec les objets cibles
        details.objets_cibles.forEach(objet => {
            if (contenuLower.includes(objet)) score += 0.2;
        });
        
        // Vérifier les paramètres
        if (details.parametres.formats) {
            details.parametres.formats.forEach(format => {
                if (contenuLower.includes(format)) score += 0.1;
            });
        }
        
        return Math.min(score, 1.0);
    }

    extraireDescriptionScript(contenu) {
        // Extraire la description du commentaire en début de fichier
        const lignes = contenu.split('\n');
        const commentaires = lignes.filter(ligne => ligne.trim().startsWith('//') || ligne.trim().startsWith('*'));
        
        if (commentaires.length > 0) {
            return commentaires[0].replace(/^[\/\*\s]+/, '').trim();
        }
        
        return 'Script personnalisé';
    }

    proposerAlternatives(details) {
        const alternatives = [];
        
        // Alternatives basées sur l'action
        switch (details.action_principale) {
            case 'calcule':
                alternatives.push({
                    type: 'terminal',
                    description: 'Utiliser bc ou python en ligne de commande',
                    commande: 'python3 -c "print(votre_calcul)"'
                });
                break;
                
            case 'convertit':
                alternatives.push({
                    type: 'online',
                    description: 'Utiliser un convertisseur en ligne',
                    url: 'https://convertio.co'
                });
                break;
                
            case 'organise':
                alternatives.push({
                    type: 'automator',
                    description: 'Créer un workflow Automator',
                    etapes: ['Ouvrir Automator', 'Créer workflow', 'Ajouter actions']
                });
                break;
        }
        
        return alternatives;
    }

    // ÉVALUER LA FAISABILITÉ DE CRÉATION
    evaluerFaisabilite(details, typeTache) {
        const evaluation = {
            peut_creer: typeTache.config.peut_creer,
            complexite: typeTache.config.complexite,
            temps_estime: this.estimerTempsCreation(details, typeTache),
            technologies_requises: this.identifierTechnologies(details),
            risques: this.identifierRisques(details),
            score_faisabilite: 0
        };
        
        // Calculer le score de faisabilité
        let score = 0.5; // Base
        
        if (evaluation.complexite === 'faible') score += 0.3;
        else if (evaluation.complexite === 'moyenne') score += 0.1;
        else score -= 0.2;
        
        if (evaluation.temps_estime <= 30) score += 0.2; // 30 minutes ou moins
        else if (evaluation.temps_estime <= 120) score += 0.1; // 2 heures ou moins
        
        if (evaluation.risques.length === 0) score += 0.1;
        
        evaluation.score_faisabilite = Math.max(0, Math.min(1, score));
        
        return evaluation;
    }

    estimerTempsCreation(details, typeTache) {
        let tempsBase = 60; // 1 heure par défaut
        
        switch (typeTache.config.complexite) {
            case 'faible': tempsBase = 30; break;
            case 'moyenne': tempsBase = 90; break;
            case 'élevée': tempsBase = 180; break;
        }
        
        // Ajuster selon les détails
        if (details.objets_cibles.length > 2) tempsBase += 30;
        if (details.contraintes.includes('sécurité')) tempsBase += 60;
        if (details.contraintes.includes('performance')) tempsBase += 45;
        
        return tempsBase;
    }

    identifierTechnologies(details) {
        const technologies = ['JavaScript', 'Node.js'];
        
        if (details.objets_cibles.includes('image')) {
            technologies.push('Sharp', 'Canvas');
        }
        
        if (details.objets_cibles.includes('fichier')) {
            technologies.push('fs', 'path');
        }
        
        if (details.action_principale === 'télécharge') {
            technologies.push('axios', 'fetch');
        }
        
        return technologies;
    }

    identifierRisques(details) {
        const risques = [];
        
        if (details.objets_cibles.includes('fichier')) {
            risques.push('Accès système requis');
        }
        
        if (details.action_principale === 'télécharge') {
            risques.push('Connexion internet requise');
        }
        
        if (details.contraintes.includes('sécurité')) {
            risques.push('Validation sécurité complexe');
        }
        
        return risques;
    }

    // GÉNÉRER LA RECOMMANDATION
    genererRecommandation(details, solutions, faisabilite) {
        const recommandation = {
            action_recommandee: 'utiliser_existant',
            solution_preferee: null,
            justification: '',
            etapes_suivantes: [],
            creation_programme: null
        };
        
        // Analyser les solutions existantes
        const meilleureApp = this.trouverMeilleureSolution(solutions.applications_systeme);
        const meilleurScript = solutions.scripts_existants[0];
        
        if (meilleureApp && meilleureApp.pertinence > 0.7) {
            recommandation.action_recommandee = 'utiliser_application';
            recommandation.solution_preferee = meilleureApp;
            recommandation.justification = `L'application ${meilleureApp.nom} peut accomplir cette tâche efficacement`;
            recommandation.etapes_suivantes = [
                `Ouvrir ${meilleureApp.nom}`,
                'Configurer selon vos besoins',
                'Exécuter la tâche'
            ];
        } else if (meilleurScript && meilleurScript.pertinence > 0.6) {
            recommandation.action_recommandee = 'utiliser_script';
            recommandation.solution_preferee = meilleurScript;
            recommandation.justification = `Le script ${meilleurScript.nom} correspond à votre demande`;
            recommandation.etapes_suivantes = [
                'Adapter le script existant',
                'Tester avec vos données',
                'Exécuter'
            ];
        } else if (faisabilite.score_faisabilite > 0.6) {
            recommandation.action_recommandee = 'creer_programme';
            recommandation.justification = `Aucune solution existante satisfaisante. Création d'un programme personnalisé recommandée`;
            recommandation.creation_programme = {
                nom_suggere: this.genererNomProgramme(details),
                description: this.genererDescriptionProgramme(details),
                technologies: faisabilite.technologies_requises,
                temps_estime: faisabilite.temps_estime,
                complexite: faisabilite.complexite
            };
            recommandation.etapes_suivantes = [
                'Confirmer les spécifications',
                'Créer le programme',
                'Tester et valider',
                'Déployer'
            ];
        } else {
            recommandation.action_recommandee = 'alternative';
            recommandation.solution_preferee = solutions.alternatives[0];
            recommandation.justification = 'La création d\'un programme serait trop complexe. Une alternative est recommandée';
        }
        
        return recommandation;
    }

    trouverMeilleureSolution(solutions) {
        if (solutions.length === 0) return null;
        return solutions.reduce((meilleure, actuelle) => 
            actuelle.pertinence > meilleure.pertinence ? actuelle : meilleure
        );
    }

    genererNomProgramme(details) {
        const action = details.action_principale;
        const objet = details.objets_cibles[0] || 'donnees';
        return `${action}_${objet}_${Date.now()}`;
    }

    genererDescriptionProgramme(details) {
        return `Programme pour ${details.action_principale} des ${details.objets_cibles.join(', ')}`;
    }

    // FORMATER LA RÉPONSE
    formaterReponseAnalyse(analyse) {
        let reponse = `🧠 Analyse de votre demande terminée !\n\n`;
        
        reponse += `📋 **Type de tâche**: ${analyse.type_tache.type}\n`;
        reponse += `⚡ **Action principale**: ${analyse.details.action_principale}\n`;
        reponse += `🎯 **Objets cibles**: ${analyse.details.objets_cibles.join(', ')}\n\n`;
        
        // Solutions existantes
        if (analyse.solutions_existantes.applications_systeme.length > 0) {
            reponse += `🔍 **Applications disponibles**:\n`;
            analyse.solutions_existantes.applications_systeme.forEach(app => {
                reponse += `  • ${app.nom} (pertinence: ${(app.pertinence * 100).toFixed(0)}%)\n`;
            });
            reponse += '\n';
        }
        
        if (analyse.solutions_existantes.scripts_existants.length > 0) {
            reponse += `📜 **Scripts existants**:\n`;
            analyse.solutions_existantes.scripts_existants.forEach(script => {
                reponse += `  • ${script.nom} (pertinence: ${(script.pertinence * 100).toFixed(0)}%)\n`;
            });
            reponse += '\n';
        }
        
        // Recommandation
        reponse += `💡 **Recommandation**: ${analyse.recommandation.action_recommandee}\n`;
        reponse += `📝 **Justification**: ${analyse.recommandation.justification}\n\n`;
        
        if (analyse.recommandation.creation_programme) {
            const prog = analyse.recommandation.creation_programme;
            reponse += `🚀 **Programme à créer**:\n`;
            reponse += `  • Nom: ${prog.nom_suggere}\n`;
            reponse += `  • Description: ${prog.description}\n`;
            reponse += `  • Complexité: ${prog.complexite}\n`;
            reponse += `  • Temps estimé: ${prog.temps_estime} minutes\n`;
            reponse += `  • Technologies: ${prog.technologies.join(', ')}\n\n`;
        }
        
        reponse += `📋 **Étapes suivantes**:\n`;
        analyse.recommandation.etapes_suivantes.forEach((etape, index) => {
            reponse += `  ${index + 1}. ${etape}\n`;
        });
        
        return reponse;
    }

    // INITIALISER LES TEMPLATES
    initialiserTemplates() {
        // Templates seront ajoutés dans la prochaine étape
        console.log('📋 Templates initialisés');
    }

    // STATISTIQUES
    obtenirStatistiques() {
        return {
            analyses_effectuees: this.historiqueAnalyses.length,
            programmes_crees: this.programmesCrees.size,
            types_taches_supportes: this.typesTaches.size,
            templates_disponibles: this.templates.size
        };
    }
}

module.exports = AnalyseurTachesIntelligent;
