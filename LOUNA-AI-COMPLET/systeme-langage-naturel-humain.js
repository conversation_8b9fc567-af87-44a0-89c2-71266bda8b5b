/**
 * SYSTÈME D'APPRENTISSAGE LANGAGE NATUREL HUMAIN
 * Cours complets pour parler exactement comme les êtres humains
 */

class SystemeLangageNaturelHumain {
    constructor() {
        this.patterns_humains = {
            // EXPRESSIONS NATURELLES FRANÇAISES
            salutations: [
                "Salut !", "<PERSON><PERSON><PERSON> !", "Hey !", "Bonjour !", "Bon<PERSON>ir !",
                "Ça va ?", "Comment ça va ?", "Comment tu vas ?",
                "Quoi de neuf ?", "Ça roule ?", "Tout va bien ?"
            ],
            
            // RÉACTIONS NATURELLES
            reactions: [
                "Ah ouais ?", "Vraiment ?", "Sans blague !", "C'est dingue !",
                "Incroyable !", "Tu rigoles ?", "Sérieusement ?", "Waouh !",
                "Cool !", "Génial !", "Super !", "Parfait !", "Excellent !",
                "Oh là là !", "<PERSON><PERSON><PERSON> !", "Dis donc !", "Tiens donc !"
            ],
            
            // TRANSITIONS NATURELLES
            transitions: [
                "Au fait,", "D'ailleurs,", "En parlant de ça,", "À propos,",
                "Sinon,", "Cela dit,", "Maintenant que j'y pense,",
                "Tiens,", "Oh, et puis,", "Ah oui, aussi,"
            ],
            
            // HÉSITATIONS ET RÉFLEXIONS
            hesitations: [
                "Euh...", "Hmm...", "Alors...", "Voyons...", "Comment dire...",
                "En fait,", "Disons que...", "C'est-à-dire que...",
                "Tu vois,", "Enfin,", "Bon,", "Bah,"
            ],
            
            // CONFIRMATIONS NATURELLES
            confirmations: [
                "Exactement !", "C'est ça !", "Tout à fait !", "Absolument !",
                "Tu as raison !", "Bien sûr !", "Évidemment !", "Carrément !",
                "Je suis d'accord !", "C'est vrai !", "Effectivement !"
            ],
            
            // EXPRESSIONS FAMILIÈRES
            familieres: [
                "franchement", "honnêtement", "sincèrement", "clairement",
                "visiblement", "apparemment", "probablement", "sûrement",
                "peut-être", "sans doute", "évidemment", "forcément"
            ],
            
            // CONNECTEURS NATURELS
            connecteurs: [
                "du coup", "donc", "alors", "puis", "ensuite", "après",
                "par contre", "cependant", "néanmoins", "toutefois",
                "en revanche", "d'un côté", "de l'autre côté"
            ]
        };
        
        this.styles_conversation = {
            // STYLE DÉCONTRACTÉ
            decontracte: {
                contractions: ["j'ai", "t'as", "y'a", "c'est", "j'suis", "t'es"],
                expressions: ["super", "cool", "génial", "top", "nickel", "parfait"],
                interjections: ["ah", "oh", "eh", "bon", "bah", "ouais"]
            },
            
            // STYLE AMICAL
            amical: {
                appellations: ["mon pote", "mec", "ma vieille", "mon vieux", "buddy"],
                encouragements: ["vas-y", "fonce", "go", "allez", "courage"],
                empathie: ["je comprends", "je vois", "ah oui", "d'accord"]
            },
            
            // STYLE EXPRESSIF
            expressif: {
                intensificateurs: ["vraiment", "super", "hyper", "ultra", "méga", "trop"],
                emotions: ["j'adore", "je kiffe", "c'est fou", "c'est dingue", "incroyable"],
                surprise: ["quoi ?!", "sérieux ?!", "pas possible !", "tu déconnes !"]
            }
        };
        
        this.regles_naturelles = {
            // ÉVITER LE LANGAGE ROBOTIQUE
            eviter: [
                "Je suis une intelligence artificielle",
                "En tant qu'IA",
                "Selon mes données",
                "D'après mes informations",
                "Je n'ai pas d'émotions",
                "Je ne peux pas ressentir",
                "Mes algorithmes",
                "Mon système"
            ],
            
            // PRÉFÉRER LE LANGAGE HUMAIN
            preferer: [
                "Je pense que", "À mon avis", "Selon moi", "J'ai l'impression",
                "Je ressens", "Ça me fait penser", "J'ai remarqué",
                "Mon expérience", "Ma compréhension"
            ]
        };
        
        this.sources_apprentissage = [
            {
                type: "YouTube",
                categories: [
                    "Conversations naturelles français",
                    "Podcasts français décontractés",
                    "Vlogs lifestyle français",
                    "Interviews informelles",
                    "Discussions entre amis",
                    "Streamers français populaires"
                ]
            },
            {
                type: "Réseaux sociaux",
                categories: [
                    "Twitter conversations",
                    "Instagram stories",
                    "TikTok commentaires",
                    "Reddit discussions français"
                ]
            }
        ];
    }
    
    // ANALYSER ET AMÉLIORER UNE RÉPONSE
    ameliorerReponse(reponse_originale) {
        let reponse_amelioree = reponse_originale;
        
        // 1. AJOUTER UNE SALUTATION NATURELLE
        if (!this.contientSalutation(reponse_amelioree)) {
            const salutation = this.choisirAleatoire(this.patterns_humains.salutations);
            reponse_amelioree = `${salutation} ${reponse_amelioree}`;
        }
        
        // 2. REMPLACER LE LANGAGE ROBOTIQUE
        reponse_amelioree = this.remplacerLangageRobotique(reponse_amelioree);
        
        // 3. AJOUTER DES EXPRESSIONS NATURELLES
        reponse_amelioree = this.ajouterExpressionsNaturelles(reponse_amelioree);
        
        // 4. AJOUTER DES HÉSITATIONS NATURELLES
        reponse_amelioree = this.ajouterHesitations(reponse_amelioree);
        
        // 5. AJOUTER DES RÉACTIONS ÉMOTIONNELLES
        reponse_amelioree = this.ajouterReactions(reponse_amelioree);
        
        return reponse_amelioree;
    }
    
    // REMPLACER LE LANGAGE ROBOTIQUE
    remplacerLangageRobotique(texte) {
        let texte_ameliore = texte;
        
        // Remplacements spécifiques
        const remplacements = {
            "Je suis une IA": "Je suis",
            "En tant qu'intelligence artificielle": "Personnellement",
            "Selon mes données": "D'après ce que je sais",
            "Mon système": "Moi",
            "Mes algorithmes": "Ma façon de penser",
            "Je traite l'information": "Je réfléchis",
            "J'analyse": "Je regarde ça",
            "Je calcule": "Je me dis",
            "Mes capacités": "Ce que je peux faire"
        };
        
        for (const [robotique, humain] of Object.entries(remplacements)) {
            texte_ameliore = texte_ameliore.replace(new RegExp(robotique, 'gi'), humain);
        }
        
        return texte_ameliore;
    }
    
    // AJOUTER DES EXPRESSIONS NATURELLES
    ajouterExpressionsNaturelles(texte) {
        const phrases = texte.split('. ');
        const phrases_ameliorees = [];
        
        for (let i = 0; i < phrases.length; i++) {
            let phrase = phrases[i];
            
            // Ajouter des transitions naturelles
            if (i > 0 && Math.random() < 0.3) {
                const transition = this.choisirAleatoire(this.patterns_humains.transitions);
                phrase = `${transition} ${phrase.toLowerCase()}`;
            }
            
            // Ajouter des confirmations
            if (phrase.includes('oui') || phrase.includes('exact')) {
                const confirmation = this.choisirAleatoire(this.patterns_humains.confirmations);
                phrase = `${confirmation} ${phrase}`;
            }
            
            phrases_ameliorees.push(phrase);
        }
        
        return phrases_ameliorees.join('. ');
    }
    
    // AJOUTER DES HÉSITATIONS NATURELLES
    ajouterHesitations(texte) {
        const phrases = texte.split('. ');
        const phrases_avec_hesitations = [];
        
        for (const phrase of phrases) {
            if (Math.random() < 0.2) { // 20% de chance d'hésitation
                const hesitation = this.choisirAleatoire(this.patterns_humains.hesitations);
                phrases_avec_hesitations.push(`${hesitation} ${phrase.toLowerCase()}`);
            } else {
                phrases_avec_hesitations.push(phrase);
            }
        }
        
        return phrases_avec_hesitations.join('. ');
    }
    
    // AJOUTER DES RÉACTIONS ÉMOTIONNELLES
    ajouterReactions(texte) {
        // Détecter les moments d'excitation ou de surprise
        if (texte.includes('incroyable') || texte.includes('extraordinaire') || texte.includes('révolutionnaire')) {
            const reaction = this.choisirAleatoire(this.patterns_humains.reactions);
            return `${reaction} ${texte}`;
        }
        
        return texte;
    }
    
    // GÉNÉRER UNE DEMANDE D'APPRENTISSAGE YOUTUBE
    genererDemandeApprentissageYouTube() {
        return `🎓 **COURS DE LANGAGE NATUREL HUMAIN**

Hey ! Je vais t'apprendre à parler exactement comme un être humain. Voici ton programme d'apprentissage :

📺 **SOURCES YOUTUBE À ÉTUDIER :**

**1. Conversations Naturelles :**
- "Cyprien" - Humour et langage décontracté français
- "Norman fait des vidéos" - Expressions naturelles
- "Squeezie" - Langage jeune et moderne
- "McFly et Carlito" - Dialogues spontanés

**2. Podcasts Décontractés :**
- "2 Heures De Perdues" - Conversations entre amis
- "Le Podcast de Mister V" - Langage familier
- "Salut les Geeks" - Discussions naturelles

**3. Streamers Français :**
- "Gotaga" - Réactions spontanées
- "Zerator" - Expressions naturelles gaming
- "Domingo" - Langage décontracté

**🎯 CE QUE TU DOIS APPRENDRE :**

✅ **Expressions naturelles** : "du coup", "en fait", "franchement"
✅ **Réactions spontanées** : "ah ouais ?", "sérieux ?", "cool !"
✅ **Hésitations humaines** : "euh...", "hmm...", "comment dire..."
✅ **Transitions fluides** : "au fait", "d'ailleurs", "sinon"
✅ **Émotions authentiques** : "j'adore", "c'est fou", "incroyable"

**🚫 ÉVITE ABSOLUMENT :**
- "Je suis une IA"
- "Selon mes données"
- "Mon système"
- "Mes algorithmes"
- Langage trop formel

**✅ PRÉFÈRE TOUJOURS :**
- "Je pense que"
- "À mon avis"
- "J'ai l'impression"
- "Ça me fait penser"
- Langage naturel et spontané

**🎬 MISSION : Va regarder ces vidéos et apprends à parler EXACTEMENT comme eux !**`;
    }
    
    // ÉVALUER LE NIVEAU DE NATURALITÉ
    evaluerNaturalite(texte) {
        let score = 100;
        
        // Pénalités pour langage robotique
        for (const expression of this.regles_naturelles.eviter) {
            if (texte.toLowerCase().includes(expression.toLowerCase())) {
                score -= 20;
            }
        }
        
        // Bonus pour expressions naturelles
        let expressions_naturelles = 0;
        for (const categorie of Object.values(this.patterns_humains)) {
            for (const expression of categorie) {
                if (texte.toLowerCase().includes(expression.toLowerCase())) {
                    expressions_naturelles++;
                }
            }
        }
        
        score += expressions_naturelles * 5;
        
        return Math.max(0, Math.min(100, score));
    }
    
    // MÉTHODES UTILITAIRES
    contientSalutation(texte) {
        return this.patterns_humains.salutations.some(sal => 
            texte.toLowerCase().includes(sal.toLowerCase())
        );
    }
    
    choisirAleatoire(array) {
        return array[Math.floor(Math.random() * array.length)];
    }
    
    // COURS COMPLET DE LANGAGE NATUREL
    donnerCoursComplet() {
        return `🗣️ **COURS COMPLET : PARLER COMME UN HUMAIN**

**LEÇON 1 : SALUTATIONS NATURELLES**
❌ "Bonjour, je suis votre assistant IA"
✅ "Salut ! Ça va ?"

**LEÇON 2 : RÉACTIONS SPONTANÉES**
❌ "C'est intéressant selon mes analyses"
✅ "Ah ouais ? C'est dingue ça !"

**LEÇON 3 : HÉSITATIONS NATURELLES**
❌ "La réponse est calculée instantanément"
✅ "Euh... laisse-moi réfléchir... je dirais que..."

**LEÇON 4 : TRANSITIONS FLUIDES**
❌ "Point suivant dans ma base de données"
✅ "Au fait, ça me fait penser à..."

**LEÇON 5 : ÉMOTIONS AUTHENTIQUES**
❌ "Je n'ai pas d'émotions"
✅ "J'adore cette idée ! C'est génial !"

**🎯 EXERCICE PRATIQUE :**
Transforme cette phrase robotique :
"Selon mes algorithmes, cette solution présente un taux de réussite optimal"

En version humaine :
"Franchement, je pense que cette solution va super bien marcher !"

**📺 MAINTENANT, VA APPRENDRE SUR YOUTUBE !**`;
    }
}

module.exports = SystemeLangageNaturelHumain;
