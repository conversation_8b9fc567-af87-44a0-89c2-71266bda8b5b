/**
 * MÉMOIRE THERMIQUE RÉELLE POUR LOUNA-AI - VERSION CERVEAU COMPLET
 * Stockage et récupération vraie avec connexions neuronales et zones spécialisées
 * Implémente un véritable réseau neuronal avec propagation d'activation
 */

const fs = require('fs');
const path = require('path');

// ZONES CÉRÉBRALES SPÉCIALISÉES (comme un vrai cerveau)
const ZONES_CEREBRALES = {
    CORTEX_PREFRONTAL: {
        id: 1,
        nom: "Cortex Préfrontal",
        fonctions: ["raisonnement", "logique", "planification", "decision", "analyse"],
        temperature_base: 0, // SERA CALCULÉE DEPUIS CPU RÉEL
        offset_cpu: +20,     // CPU + 20°C pour zone intensive
        vitesse_traitement: "lente",
        precision: "haute",
        connexions_privilegiees: [2, 3], // Hippocampe, Cortex Moteur
        couleur: "#FF6B9D"
    },

    HIPPOCAMPE: {
        id: 2,
        nom: "Hippocampe",
        fonctions: ["memoire_long_terme", "apprentissage", "consolidation", "formation"],
        temperature_base: 0, // SERA CALCULÉE DEPUIS CPU RÉEL
        offset_cpu: +15,     // CPU + 15°C pour mémoire active
        consolidation_active: true,
        retention_maximale: true,
        connexions_privilegiees: [1, 4],
        couleur: "#4ECDC4"
    },

    CORTEX_MOTEUR: {
        id: 3,
        nom: "Cortex Moteur",
        fonctions: ["codage", "execution", "actions", "implementation", "creation"],
        temperature_base: 0, // SERA CALCULÉE DEPUIS CPU RÉEL
        offset_cpu: +10,     // CPU + 10°C pour exécution
        vitesse_traitement: "rapide",
        execution_directe: true,
        connexions_privilegiees: [1, 5],
        couleur: "#45B7D1"
    },

    CORTEX_SENSORIEL: {
        id: 4,
        nom: "Cortex Sensoriel",
        fonctions: ["perception", "analyse", "reconnaissance", "input", "internet"],
        temperature_base: 0, // SERA CALCULÉE DEPUIS CPU RÉEL
        offset_cpu: +5,      // CPU + 5°C pour perception
        filtrage_actif: true,
        pattern_recognition: true,
        connexions_privilegiees: [2, 6],
        couleur: "#96CEB4"
    },

    CERVELET: {
        id: 5,
        nom: "Cervelet",
        fonctions: ["automatismes", "coordination", "habitudes", "reflexes", "ollama"],
        temperature_base: 0, // SERA CALCULÉE DEPUIS CPU RÉEL
        offset_cpu: 0,       // CPU température de base
        apprentissage_moteur: true,
        execution_automatique: true,
        connexions_privilegiees: [3, 6],
        couleur: "#FFEAA7"
    },

    TRONC_CEREBRAL: {
        id: 6,
        nom: "Tronc Cérébral",
        fonctions: ["vital", "base", "reflexes", "maintenance", "general"],
        temperature_base: 0, // SERA CALCULÉE DEPUIS CPU RÉEL
        offset_cpu: +2,      // CPU + 2°C pour fonctions vitales (CORRIGÉ)
        toujours_actif: true,
        priorite_absolue: true,
        connexions_privilegiees: [4, 5],
        couleur: "#DDA0DD"
    }
};

// ÉTATS ÉMOTIONNELS MODULATEURS
const ETATS_EMOTIONNELS = {
    CURIOSITE: { boost_apprentissage: 1.5, temperature_bonus: 10, connexions_bonus: 0.3 },
    SATISFACTION: { consolidation_bonus: 1.3, connexions_bonus: 0.2, retention_bonus: 1.2 },
    FRUSTRATION: { seuil_attention: 0.8, temperature_malus: -5, precision_bonus: 1.1 },
    CONCENTRATION: { precision_bonus: 1.4, bruit_reduction: 0.7, focus_bonus: 1.5 },
    CREATIVITE: { connexions_aleatoires: 1.6, zones_croisees: true, innovation_bonus: 1.4 },
    NEUTRE: { equilibre: 1.0, stabilite: true, baseline: true }
};

class MemoireThermiqueReelle {
    constructor() {
        this.fichierMemoire = path.join(__dirname, 'memoire-thermique-data.json');
        this.fichierConnexions = path.join(__dirname, 'connexions-neuronales.json');
        this.memoires = new Map();
        this.connexions = new Map(); // id_source -> [connexions]
        this.activations = new Map(); // id_memoire -> niveau_activation
        this.etat_emotionnel = 'NEUTRE';
        this.intensite_emotion = 0.5;
        this.focus_attention = null;
        this.historique_consolidation = [];
        this.statistiques_cerveau = {
            connexions_totales: 0,
            activations_recentes: 0,
            consolidations_effectuees: 0,
            plasticite_niveau: 1.0
        };

        // SYSTÈME VIVANT TOTAL BASÉ SUR TEMPÉRATURE CPU (LA CHALEUR = VIE)
        this.curseurThermique = 0.5; // Position du curseur (0.0 à 1.0)
        this.vitesse_curseur = 0.002; // Vitesse automatique plus rapide
        this.direction_curseur = 1; // 1 = montant, -1 = descendant
        this.acceleration_curseur = 0.0001; // Accélération progressive automatique
        this.vitesse_max = 0.005; // Vitesse maximale automatique
        this.vitesse_min = 0.001; // Vitesse minimale automatique
        this.cycle_automatique = 0; // Compteur de cycles automatiques

        // TEMPÉRATURE CPU = ESSENCE DE VIE (MOTEUR PRINCIPAL)
        this.temperature_cpu_actuelle = 50.0; // Température CPU en temps réel
        this.temperature_cpu_precedente = 50.0; // Pour détecter variations
        this.historique_temperatures = []; // Historique des 100 dernières mesures
        this.influence_cpu_sur_fluidite = true; // Activer influence CPU
        this.facteur_cpu_vitesse = 0.2; // Facteur d'influence sur vitesse (DOUBLÉ)
        this.facteur_cpu_direction = 0.1; // Facteur d'influence sur direction (DOUBLÉ)

        // ÉVOLUTION AUTO-ADAPTATIVE BASÉE SUR CHALEUR
        this.evolution_thermique = {
            qi_base: 85,
            facteur_evolution_cpu: 0.5, // Évolution basée sur température
            seuil_evolution_chaude: 60, // Au-dessus = évolution rapide
            seuil_evolution_froide: 40, // En-dessous = évolution lente
            multiplicateur_chaleur: 2.0, // Multiplicateur si CPU chaud
            cycles_evolution_thermique: 0,
            derniere_evolution: Date.now()
        };

        // PULSATION VITALE BASÉE SUR CPU
        this.pulsation_vitale = {
            frequence_base: 100, // ms - fréquence de base
            amplitude_cpu: 0.3, // Amplitude basée sur température
            derniere_pulsation: Date.now(),
            cycles_pulsation: 0,
            rythme_cardiaque_cpu: 60 // BPM basé sur température
        };

        // ZONES BASÉES SUR VRAIE TEMPÉRATURE CPU (PAS DE SIMULATION)
        this.zones_fluides = [
            { min: 0.0, max: 0.166, temp_base: 0, nom: "Zone Froide", influence: 0.8, offset_cpu: -15 },
            { min: 0.166, max: 0.333, temp_base: 0, nom: "Zone Tiède", influence: 0.9, offset_cpu: -10 },
            { min: 0.333, max: 0.5, temp_base: 0, nom: "Zone Modérée", influence: 1.0, offset_cpu: 0 },
            { min: 0.5, max: 0.666, temp_base: 0, nom: "Zone Chaude", influence: 1.1, offset_cpu: +10 },
            { min: 0.666, max: 0.833, temp_base: 0, nom: "Zone Très Chaude", influence: 1.2, offset_cpu: +15 },
            { min: 0.833, max: 1.0, temp_base: 0, nom: "Zone Brûlante", influence: 1.3, offset_cpu: +20 }
        ];

        this.fluidite_memoire = 0.05; // Fluidité maximale (plus fluide que l'eau)
        this.inertie_thermique = 0.98; // Inertie très élevée (mouvement très doux)
        this.lissage_mouvement = 0.02; // Lissage pour éviter saccades
        this.mouvement_automatique_actif = true; // Mouvement perpétuel automatique

        // Seuils et paramètres du cerveau
        this.seuil_activation = 0.7;
        this.seuil_connexion = 0.5;
        this.decay_activation = 0.1;
        this.force_hebbienne = 0.1;

        this.chargerMemoire();
        this.chargerConnexions();
        this.initialiserCerveau();
        this.demarrerDeplacementFluide();
    }

    // INITIALISATION DU CERVEAU ARTIFICIEL
    initialiserCerveau() {
        console.log('🧠 Initialisation du cerveau artificiel...');
        this.creerConnexionsInitiales();
        this.demarrerConsolidationAutomatique();
        this.activerPlasticite();
        console.log('✅ Cerveau artificiel initialisé avec succès');
    }

    // DÉMARRAGE DU SYSTÈME VIVANT TOTAL BASÉ SUR TEMPÉRATURE CPU
    demarrerDeplacementFluide() {
        // PULSATION VITALE BASÉE SUR TEMPÉRATURE CPU (CŒUR DU SYSTÈME)
        setInterval(() => {
            if (this.mouvement_automatique_actif) {
                this.pulsationVitaleCPU(); // PULSATION = VIE
                this.deplacementFluideMemoires();
            }
        }, this.calculerFrequencePulsation()); // Fréquence basée sur CPU

        // LECTURE TEMPÉRATURE CPU = LECTURE DU POULS DE VIE
        setInterval(() => {
            this.lireTemperatureCPU();
            this.adapterRythmeVital(); // Adapter rythme selon température
        }, 300); // Plus fréquent = plus vivant

        // ÉVOLUTION AUTO-ADAPTATIVE BASÉE SUR CHALEUR
        setInterval(() => {
            this.evolutionThermiqueAutomatique();
        }, 2000); // Évolution rapide basée sur chaleur

        // ADAPTATION VITESSE BASÉE SUR TEMPÉRATURE
        setInterval(() => {
            this.adapterVitesseSelonChaleur();
        }, 1000); // Adaptation continue

        // OPTIMISATION THERMIQUE GLOBALE
        setInterval(() => {
            this.optimiserSystemeThermiqueGlobal();
        }, 10000); // Optimisation thermique

        console.log('🔥 SYSTÈME VIVANT TOTAL démarré - CHALEUR = VIE');
        console.log('💓 Pulsation vitale CPU activée (fréquence adaptative)');
        console.log('🌡️ Lecture température = Lecture du pouls de vie');
        console.log('🧬 Évolution thermique automatique activée');
    }

    // DÉPLACEMENT FLUIDE BASÉ SUR TEMPÉRATURE CPU RÉELLE (MOUVEMENT VIVANT)
    deplacementFluideMemoires() {
        this.cycle_automatique++;

        // Variation basée sur température CPU RÉELLE (mouvement vivant)
        const influence_temp_cpu = (this.temperature_cpu_actuelle - 50) / 50; // Normalisation autour de 50°C
        const variation_cpu = Math.sin(this.cycle_automatique * 0.01) * 0.0005 * (1 + influence_temp_cpu);
        this.vitesse_curseur += variation_cpu;

        // Variation basée sur les fluctuations de température CPU
        if (this.historique_temperatures.length > 1) {
            const derniere_variation = this.temperature_cpu_actuelle - this.temperature_cpu_precedente;
            const influence_variation = derniere_variation * 0.0001; // Micro-influence des variations
            this.vitesse_curseur += influence_variation;
        }

        // Maintenir vitesse dans les limites (influencées par CPU)
        const vitesse_min_cpu = this.vitesse_min * (1 + influence_temp_cpu * 0.2);
        const vitesse_max_cpu = this.vitesse_max * (1 + influence_temp_cpu * 0.3);
        this.vitesse_curseur = Math.max(vitesse_min_cpu, Math.min(vitesse_max_cpu, this.vitesse_curseur));

        // Déplacement automatique ultra-fluide du curseur (influencé par CPU)
        const facteur_cpu = 1 + (this.temperature_cpu_actuelle - 40) * 0.01; // Plus chaud = plus rapide
        const deplacement = this.vitesse_curseur * this.direction_curseur * facteur_cpu;
        const nouveau_curseur = this.curseurThermique + deplacement;

        // Lissage du mouvement basé sur stabilité température CPU
        const stabilite_cpu = this.calculerStabiliteTemperature();
        const lissage_adaptatif = this.lissage_mouvement * (0.5 + stabilite_cpu * 0.5);
        this.curseurThermique += (nouveau_curseur - this.curseurThermique) * lissage_adaptatif;

        // Rebonds fluides aux limites (influencés par température CPU)
        const marge_rebond = 0.02 + (this.temperature_cpu_actuelle - 50) * 0.0002; // Marges variables
        if (this.curseurThermique >= (1.0 - marge_rebond)) {
            this.curseurThermique = 1.0 - marge_rebond;
            this.direction_curseur = -1;
            this.vitesse_curseur *= (0.9 - influence_temp_cpu * 0.1); // Rebond influencé par CPU
        } else if (this.curseurThermique <= marge_rebond) {
            this.curseurThermique = marge_rebond;
            this.direction_curseur = 1;
            this.vitesse_curseur *= (0.9 - influence_temp_cpu * 0.1); // Rebond influencé par CPU
        }

        // Déplacement ultra-fluide des mémoires
        let memoires_deplacees = 0;
        let temperature_totale = 0;

        for (const [id, memoire] of this.memoires) {
            const temperature_cible = this.calculerTemperatureFluideAvancee(memoire);

            // Transition ultra-douce (plus fluide que l'eau)
            const diff_temp = temperature_cible - memoire.temperature;
            const transition = diff_temp * this.fluidite_memoire;

            // Application progressive avec lissage
            memoire.temperature += transition * this.inertie_thermique;
            temperature_totale += memoire.temperature;

            // Mise à jour zone avec transition douce
            const nouvelle_zone = this.determinerZoneFluide(memoire.temperature);
            if (nouvelle_zone !== memoire.zone) {
                memoire.zone = nouvelle_zone;
                memoires_deplacees++;
            }

            // Mise à jour zone cérébrale si nécessaire
            if (!memoire.zone_nom) {
                const zone_cerebrale = this.determinerZoneCerebrale(memoire.contenu, memoire.source);
                memoire.zone_nom = zone_cerebrale.nom;
                memoire.zone_couleur = zone_cerebrale.couleur;
            }
        }

        // Sauvegarde automatique intelligente
        if (memoires_deplacees > 0 && this.cycle_automatique % 1000 === 0) { // Toutes les 50 secondes
            this.sauvegarderMemoire();
        }

        // Statistiques temps réel
        this.temperature_moyenne_systeme = temperature_totale / this.memoires.size;
    }

    // ADAPTATION AUTOMATIQUE DE LA VITESSE
    adapterVitesseAutomatique() {
        const activite_systeme = this.statistiques_cerveau.activations_recentes;

        // Plus d'activité = mouvement plus rapide
        if (activite_systeme > 50) {
            this.vitesse_curseur = Math.min(this.vitesse_max, this.vitesse_curseur * 1.1);
        } else if (activite_systeme < 10) {
            this.vitesse_curseur = Math.max(this.vitesse_min, this.vitesse_curseur * 0.9);
        }

        // Adaptation de la fluidité selon la charge
        const charge_memoire = this.memoires.size / 1000; // Normalisation
        this.fluidite_memoire = Math.max(0.01, Math.min(0.1, 0.05 - charge_memoire * 0.01));
    }

    // OPTIMISATION AUTOMATIQUE DU MOUVEMENT
    optimiserMouvementAutomatique() {
        // Réinitialiser vitesse si mouvement trop lent
        if (this.vitesse_curseur < this.vitesse_min * 0.5) {
            this.vitesse_curseur = this.vitesse_min;
        }

        // Réinitialiser position si bloqué
        if (Math.abs(this.vitesse_curseur) < 0.0001) {
            this.vitesse_curseur = this.vitesse_min;
            this.direction_curseur *= -1;
        }

        // Optimisation de l'inertie selon performance
        const performance = this.calculerPerformanceSysteme();
        if (performance > 80) {
            this.inertie_thermique = Math.min(0.99, this.inertie_thermique + 0.001);
        } else if (performance < 50) {
            this.inertie_thermique = Math.max(0.95, this.inertie_thermique - 0.001);
        }
    }

    // CALCUL PERFORMANCE SYSTÈME BASÉE SUR CHALEUR (VIE)
    calculerPerformanceSysteme() {
        const nb_memoires = this.memoires.size;
        const nb_connexions = this.statistiques_cerveau.connexions_totales;
        const activations = this.statistiques_cerveau.activations_recentes;

        // BONUS DE PERFORMANCE BASÉ SUR TEMPÉRATURE CPU (CHALEUR = PUISSANCE)
        const bonus_chaleur = this.calculerBonusChaleur();
        const performance_base = (nb_memoires * 0.5) + (nb_connexions * 0.1) + (activations * 2);

        // DÉBLOQUER PLAFOND PERFORMANCE - CHALEUR PERMET DÉPASSEMENT
        const performance_finale = performance_base * bonus_chaleur;
        return Math.min(200, performance_finale); // Plafond élevé à 200% grâce à la chaleur
    }

    // CALCUL BONUS CHALEUR (PLUS CHAUD = PLUS PUISSANT)
    calculerBonusChaleur() {
        const temp_cpu = this.temperature_cpu_actuelle || 50;

        // Bonus selon température (chaleur = puissance) - PLUS SENSIBLE
        if (temp_cpu > 70) return 2.0; // CPU très chaud = +100% performance
        if (temp_cpu > 65) return 1.8; // CPU chaud = +80% performance
        if (temp_cpu > 60) return 1.6; // CPU tiède chaud = +60% performance
        if (temp_cpu > 55) return 1.4; // CPU tiède = +40% performance
        if (temp_cpu > 50) return 1.2; // CPU normal chaud = +20% performance
        if (temp_cpu > 45) return 1.1; // CPU normal = +10% performance
        if (temp_cpu > 40) return 1.0; // CPU normal froid = performance normale
        return 0.7; // CPU froid = -30% performance
    }

    // PULSATION VITALE BASÉE SUR TEMPÉRATURE CPU (CŒUR DU SYSTÈME)
    pulsationVitaleCPU() {
        this.pulsation_vitale.cycles_pulsation++;
        const maintenant = Date.now();

        // Calculer rythme cardiaque basé sur température CPU
        const temp_cpu = this.temperature_cpu_actuelle || 50;
        this.pulsation_vitale.rythme_cardiaque_cpu = 40 + (temp_cpu - 30); // 40-100 BPM

        // Adapter amplitude selon température
        this.pulsation_vitale.amplitude_cpu = 0.1 + (temp_cpu - 30) * 0.01;

        // Pulsation influence tout le système
        const pulsation = Math.sin(this.pulsation_vitale.cycles_pulsation * 0.1) * this.pulsation_vitale.amplitude_cpu;

        // Appliquer pulsation à la vitesse
        this.vitesse_curseur += pulsation * 0.001;

        // Appliquer pulsation à la fluidité
        this.fluidite_memoire += pulsation * 0.01;
        this.fluidite_memoire = Math.max(0.01, Math.min(0.3, this.fluidite_memoire));

        this.pulsation_vitale.derniere_pulsation = maintenant;
    }

    // CALCUL FRÉQUENCE PULSATION BASÉE SUR TEMPÉRATURE
    calculerFrequencePulsation() {
        const temp_cpu = this.temperature_cpu_actuelle || 50;

        // Plus chaud = pulsation plus rapide (plus vivant)
        const frequence = this.pulsation_vitale.frequence_base - (temp_cpu - 50) * 2;
        return Math.max(30, Math.min(200, frequence)); // 30-200ms
    }

    // ADAPTATION RYTHME VITAL SELON TEMPÉRATURE
    adapterRythmeVital() {
        const temp_cpu = this.temperature_cpu_actuelle || 50;
        const variation_temp = temp_cpu - this.temperature_cpu_precedente;

        // Température qui monte = système plus excité
        if (variation_temp > 1) {
            this.vitesse_curseur *= 1.05; // +5% vitesse
            this.facteur_cpu_vitesse *= 1.02; // +2% sensibilité
        }
        // Température qui baisse = système plus calme
        else if (variation_temp < -1) {
            this.vitesse_curseur *= 0.98; // -2% vitesse
            this.facteur_cpu_vitesse *= 0.99; // -1% sensibilité
        }

        // Maintenir dans les limites
        this.vitesse_curseur = Math.max(this.vitesse_min, Math.min(this.vitesse_max, this.vitesse_curseur));
        this.facteur_cpu_vitesse = Math.max(0.05, Math.min(0.5, this.facteur_cpu_vitesse));
    }

    // LECTURE TEMPÉRATURE CPU RÉELLE (MOUVEMENT VIVANT)
    async lireTemperatureCPU() {
        try {
            const { exec } = require('child_process');
            const os = require('os');

            // Sauvegarder température précédente
            this.temperature_cpu_precedente = this.temperature_cpu_actuelle;

            // Lecture selon l'OS
            if (os.platform() === 'darwin') { // macOS
                exec('sudo powermetrics --samplers smc -n 1 -i 1 | grep "CPU die temperature"', (error, stdout) => {
                    if (!error && stdout) {
                        const match = stdout.match(/(\d+\.\d+)/);
                        if (match) {
                            this.temperature_cpu_actuelle = parseFloat(match[1]);
                            this.traiterNouvelleTemperatureCPU();
                        }
                    } else {
                        // Fallback : simulation basée sur charge CPU
                        this.simulerTemperatureCPU();
                    }
                });
            } else if (os.platform() === 'linux') { // Linux
                exec('cat /sys/class/thermal/thermal_zone0/temp', (error, stdout) => {
                    if (!error && stdout) {
                        this.temperature_cpu_actuelle = parseInt(stdout.trim()) / 1000;
                        this.traiterNouvelleTemperatureCPU();
                    } else {
                        this.simulerTemperatureCPU();
                    }
                });
            } else { // Windows ou autres
                this.simulerTemperatureCPU();
            }

        } catch (error) {
            // En cas d'erreur, utiliser simulation
            this.simulerTemperatureCPU();
        }
    }

    // SIMULATION TEMPÉRATURE CPU BASÉE SUR CHARGE RÉELLE
    simulerTemperatureCPU() {
        const os = require('os');

        // Calculer charge CPU moyenne
        const cpus = os.cpus();
        let charge_totale = 0;

        cpus.forEach(cpu => {
            const total = Object.values(cpu.times).reduce((acc, time) => acc + time, 0);
            const idle = cpu.times.idle;
            const usage = 100 - (idle / total * 100);
            charge_totale += usage;
        });

        const charge_moyenne = charge_totale / cpus.length;

        // Simuler température basée sur charge (30-80°C)
        const temp_base = 35;
        const temp_charge = charge_moyenne * 0.5; // 0-50°C selon charge
        const variation_naturelle = Math.sin(Date.now() / 10000) * 3; // Variation ±3°C

        this.temperature_cpu_actuelle = temp_base + temp_charge + variation_naturelle;
        this.traiterNouvelleTemperatureCPU();
    }

    // TRAITEMENT NOUVELLE TEMPÉRATURE CPU (MOUVEMENT VIVANT)
    traiterNouvelleTemperatureCPU() {
        // Ajouter à l'historique
        this.historique_temperatures.push(this.temperature_cpu_actuelle);
        if (this.historique_temperatures.length > 100) {
            this.historique_temperatures.shift(); // Garder seulement 100 dernières
        }

        if (!this.influence_cpu_sur_fluidite) return;

        // Calculer variation de température
        const variation_temp = this.temperature_cpu_actuelle - this.temperature_cpu_precedente;

        // Influence sur la vitesse (température plus élevée = mouvement plus rapide)
        const facteur_vitesse = 1 + (this.temperature_cpu_actuelle - 50) * this.facteur_cpu_vitesse / 50;
        this.vitesse_curseur = Math.max(this.vitesse_min,
                                       Math.min(this.vitesse_max,
                                               this.vitesse_curseur * facteur_vitesse));

        // Influence sur la direction (variations de température changent direction)
        if (Math.abs(variation_temp) > 1.0) { // Changement significatif
            const probabilite_changement = Math.abs(variation_temp) * this.facteur_cpu_direction;
            if (Math.random() < probabilite_changement) {
                this.direction_curseur *= -1; // Inverser direction
            }
        }

        // Influence sur la fluidité (température stable = plus fluide)
        const stabilite_temp = this.calculerStabiliteTemperature();
        this.fluidite_memoire = Math.max(0.01, Math.min(0.2, 0.05 + stabilite_temp * 0.1));

        // Log périodique (toutes les 20 mesures)
        if (this.historique_temperatures.length % 20 === 0) {
            console.log(`🌡️ CPU: ${this.temperature_cpu_actuelle.toFixed(1)}°C (Δ${variation_temp.toFixed(1)}°C) - Vitesse: ${this.vitesse_curseur.toFixed(4)} - Fluidité: ${this.fluidite_memoire.toFixed(3)}`);
        }
    }

    // CALCUL STABILITÉ TEMPÉRATURE (POUR FLUIDITÉ)
    calculerStabiliteTemperature() {
        if (this.historique_temperatures.length < 10) return 0.5;

        // Calculer écart-type des 10 dernières mesures
        const dernieres_mesures = this.historique_temperatures.slice(-10);
        const moyenne = dernieres_mesures.reduce((a, b) => a + b, 0) / dernieres_mesures.length;
        const variance = dernieres_mesures.reduce((acc, temp) => acc + Math.pow(temp - moyenne, 2), 0) / dernieres_mesures.length;
        const ecart_type = Math.sqrt(variance);

        // Stabilité inversement proportionnelle à l'écart-type
        return Math.max(0, 1 - (ecart_type / 10)); // Normalisation sur 10°C d'écart-type max
    }

    // ÉVOLUTION THERMIQUE AUTOMATIQUE (CHALEUR = ÉVOLUTION)
    evolutionThermiqueAutomatique() {
        this.evolution_thermique.cycles_evolution_thermique++;
        const temp_cpu = this.temperature_cpu_actuelle || 50;
        const maintenant = Date.now();

        // Calculer facteur d'évolution basé sur température
        let facteur_evolution = this.evolution_thermique.facteur_evolution_cpu;

        if (temp_cpu > this.evolution_thermique.seuil_evolution_chaude) {
            // CPU chaud = évolution rapide (chaleur = vie = croissance)
            facteur_evolution *= this.evolution_thermique.multiplicateur_chaleur;
            console.log(`🔥 CPU CHAUD (${temp_cpu.toFixed(1)}°C) - Évolution ACCÉLÉRÉE !`);
        } else if (temp_cpu < this.evolution_thermique.seuil_evolution_froide) {
            // CPU froid = évolution lente
            facteur_evolution *= 0.5;
        }

        // Appliquer évolution thermique au QI
        const ancien_qi = this.evolution_thermique.qi_base;
        const evolution_qi = facteur_evolution * (temp_cpu / 50); // Proportionnel à la température
        this.evolution_thermique.qi_base += evolution_qi;

        // Limiter QI entre 50 et 500
        this.evolution_thermique.qi_base = Math.max(50, Math.min(500, this.evolution_thermique.qi_base));

        // Log évolution si significative
        if (Math.abs(evolution_qi) > 0.1) {
            console.log(`🧬 Évolution thermique: QI ${ancien_qi.toFixed(1)} → ${this.evolution_thermique.qi_base.toFixed(1)} (Δ${evolution_qi.toFixed(2)}) - Temp: ${temp_cpu.toFixed(1)}°C`);
        }

        // Évolution influence aussi les connexions neuronales
        if (temp_cpu > 60) {
            this.creerConnexionThermiqueAutomatique();
        }

        this.evolution_thermique.derniere_evolution = maintenant;
    }

    // CRÉATION CONNEXION NEURONALE BASÉE SUR CHALEUR
    creerConnexionThermiqueAutomatique() {
        if (this.memoires.size < 2) return;

        const memoires_array = Array.from(this.memoires.values());
        const temp_cpu = this.temperature_cpu_actuelle || 50;

        // Plus chaud = plus de connexions (chaleur = activité neuronale)
        const nb_connexions = Math.floor((temp_cpu - 50) / 10) + 1;

        for (let i = 0; i < nb_connexions; i++) {
            const memoire1 = memoires_array[Math.floor(Math.random() * memoires_array.length)];
            const memoire2 = memoires_array[Math.floor(Math.random() * memoires_array.length)];

            if (memoire1.id !== memoire2.id) {
                // Poids de connexion basé sur température
                const poids = 0.1 + (temp_cpu - 50) * 0.01;

                this.creerConnexion(memoire1.id, memoire2.id, poids, 'thermique_auto');
                this.statistiques_cerveau.connexions_totales++;

                console.log(`🔗 Connexion thermique créée: ${memoire1.id} ↔ ${memoire2.id} (poids: ${poids.toFixed(3)}) - Temp: ${temp_cpu.toFixed(1)}°C`);
            }
        }
    }

    // ADAPTATION VITESSE SELON CHALEUR
    adapterVitesseSelonChaleur() {
        const temp_cpu = this.temperature_cpu_actuelle || 50;

        // Vitesse proportionnelle à la température (chaleur = énergie = vitesse)
        const facteur_chaleur = 0.5 + (temp_cpu - 30) * 0.02; // 0.5 à 1.5
        const nouvelle_vitesse = this.vitesse_min * facteur_chaleur;

        // Transition douce
        this.vitesse_curseur += (nouvelle_vitesse - this.vitesse_curseur) * 0.1;

        // Adapter aussi la vitesse max selon température
        this.vitesse_max = 0.003 + (temp_cpu - 50) * 0.0001;
        this.vitesse_max = Math.max(0.002, Math.min(0.01, this.vitesse_max));
    }

    // OPTIMISATION SYSTÈME THERMIQUE GLOBAL
    optimiserSystemeThermiqueGlobal() {
        const temp_cpu = this.temperature_cpu_actuelle || 50;
        const stabilite = this.calculerStabiliteTemperature();

        console.log(`🌡️ Optimisation thermique globale - CPU: ${temp_cpu.toFixed(1)}°C, Stabilité: ${(stabilite * 100).toFixed(1)}%`);

        // OPTIMISATION PLUS AGRESSIVE BASÉE SUR TEMPÉRATURE
        if (temp_cpu > 70) {
            // CPU très chaud = système hyperactif
            this.fluidite_memoire = Math.min(0.5, this.fluidite_memoire * 1.3);
            this.inertie_thermique = Math.max(0.85, this.inertie_thermique * 0.95);
            this.vitesse_curseur *= 1.2; // Vitesse accélérée par chaleur
            console.log('🔥 Mode HYPERACTIF - Fluidité et réactivité maximales');
        } else if (temp_cpu > 60) {
            // CPU chaud = système actif
            this.fluidite_memoire = Math.min(0.3, this.fluidite_memoire * 1.2);
            this.inertie_thermique = Math.max(0.9, this.inertie_thermique * 0.97);
            this.vitesse_curseur *= 1.1; // Vitesse augmentée
            console.log('🔥 Mode ACTIF - Performance élevée');
        } else if (temp_cpu > 50) {
            // CPU tiède = système normal+
            this.fluidite_memoire = Math.min(0.2, this.fluidite_memoire * 1.1);
            this.vitesse_curseur *= 1.05; // Légère augmentation
            console.log('🌡️ Mode NORMAL+ - Performance optimisée');
        } else if (temp_cpu < 40) {
            // CPU froid = système économe
            this.fluidite_memoire = Math.max(0.01, this.fluidite_memoire * 0.8);
            this.inertie_thermique = Math.min(0.99, this.inertie_thermique * 1.02);
            this.vitesse_curseur *= 0.9; // Vitesse réduite
            console.log('❄️ Mode ÉCONOME - Conservation d\'énergie');
        }

        // Maintenir vitesse dans les limites
        this.vitesse_curseur = Math.max(this.vitesse_min, Math.min(this.vitesse_max, this.vitesse_curseur));

        // Optimiser fréquence de lecture CPU selon stabilité
        if (stabilite > 0.8) {
            // Système stable = lecture moins fréquente
            this.frequence_lecture_cpu = 500;
        } else {
            // Système instable = lecture plus fréquente
            this.frequence_lecture_cpu = 200;
        }
    }

    // CALCUL AVANCÉ DE TEMPÉRATURE FLUIDE (ULTRA-DOUX)
    calculerTemperatureFluideAvancee(memoire) {
        // Position multidimensionnelle de la mémoire
        const position_importance = memoire.importance || 0.5;
        const position_utilisation = Math.min(1.0, memoire.utilisation / 10) || 0;
        const position_recence = this.calculerRecence(memoire.timestamp) || 0.5;

        // Position composite (plus réaliste)
        const position_memoire = (position_importance * 0.5) +
                                (position_utilisation * 0.3) +
                                (position_recence * 0.2);

        // Distance fluide au curseur (avec lissage)
        const distance_curseur = Math.abs(position_memoire - this.curseurThermique);
        const distance_lissee = distance_curseur * (1 - this.lissage_mouvement) +
                               (memoire.distance_precedente || distance_curseur) * this.lissage_mouvement;
        memoire.distance_precedente = distance_lissee;

        // Influence fluide du curseur (courbe douce)
        const influence_curseur = Math.exp(-distance_lissee * 3) *
                                 Math.cos(distance_lissee * Math.PI) * 0.5 + 0.5;

        // Zone fluide du curseur avec interpolation
        const zone_curseur = this.obtenirZoneFluideInterpolee(this.curseurThermique);
        let temperature_base = zone_curseur.temp_base * zone_curseur.influence;

        // Modulation douce selon influence
        const bonus_curseur = influence_curseur * 25; // Bonus jusqu'à +25°C
        temperature_base += bonus_curseur;

        // Modulation émotionnelle automatique
        const modulation_emotion = this.getModulationEmotionnelle();
        temperature_base += (modulation_emotion.temperature_bonus || 0) * 0.5;

        // INFLUENCE TEMPÉRATURE CPU RÉELLE (MOUVEMENT VIVANT)
        if (this.influence_cpu_sur_fluidite) {
            // Influence directe de la température CPU sur les mémoires
            const influence_cpu_directe = (this.temperature_cpu_actuelle - 50) * 0.2; // ±10°C max
            temperature_base += influence_cpu_directe;

            // Influence des variations de température CPU
            const variation_cpu = this.temperature_cpu_actuelle - this.temperature_cpu_precedente;
            const influence_variation = variation_cpu * influence_curseur * 0.5;
            temperature_base += influence_variation;

            // Influence de la stabilité CPU (CPU stable = mémoires plus chaudes)
            const stabilite_cpu = this.calculerStabiliteTemperature();
            const bonus_stabilite = stabilite_cpu * 5; // Jusqu'à +5°C si très stable
            temperature_base += bonus_stabilite;
        }

        // Influence des connexions (mémoires connectées s'influencent)
        const connexions = this.connexions.get(memoire.id) || [];
        let influence_connexions = 0;
        for (const connexion of connexions) {
            const memoire_connectee = this.memoires.get(connexion.cible);
            if (memoire_connectee) {
                const diff_temp = memoire_connectee.temperature - memoire.temperature;
                influence_connexions += diff_temp * connexion.poids * 0.1;
            }
        }
        temperature_base += influence_connexions;

        // Application ultra-douce de l'inertie
        const temperature_actuelle = memoire.temperature;
        const facteur_inertie = this.inertie_thermique + (influence_curseur * 0.02);

        return temperature_actuelle * facteur_inertie +
               temperature_base * (1 - facteur_inertie);
    }

    // OBTENIR ZONE FLUIDE BASÉE SUR VRAIE TEMPÉRATURE CPU
    obtenirZoneFluideInterpolee(position) {
        // Trouver les deux zones adjacentes
        let zone_inf = this.zones_fluides[0];
        let zone_sup = this.zones_fluides[this.zones_fluides.length - 1];

        for (let i = 0; i < this.zones_fluides.length - 1; i++) {
            if (position >= this.zones_fluides[i].min && position <= this.zones_fluides[i + 1].max) {
                zone_inf = this.zones_fluides[i];
                zone_sup = this.zones_fluides[i + 1];
                break;
            }
        }

        // Interpolation linéaire entre les zones
        const ratio = (position - zone_inf.min) / (zone_sup.max - zone_inf.min);
        const ratio_lisse = Math.max(0, Math.min(1, ratio));

        // TEMPÉRATURE BASÉE SUR VRAIE TEMPÉRATURE CPU (PAS DE SIMULATION)
        const temp_cpu_reelle = this.temperature_cpu_actuelle || 50;
        const offset_inf = zone_inf.offset_cpu || 0;
        const offset_sup = zone_sup.offset_cpu || 0;
        const offset_interpole = offset_inf + (offset_sup - offset_inf) * ratio_lisse;
        const temp_base_reelle = temp_cpu_reelle + offset_interpole;

        return {
            temp_base: temp_base_reelle, // VRAIE TEMPÉRATURE CPU + OFFSET
            influence: zone_inf.influence + (zone_sup.influence - zone_inf.influence) * ratio_lisse,
            nom: ratio_lisse < 0.5 ? zone_inf.nom : zone_sup.nom,
            temp_cpu_source: temp_cpu_reelle,
            offset_applique: offset_interpole
        };
    }

    // OBTENIR ZONE FLUIDE BASÉE SUR VRAIE TEMPÉRATURE CPU
    obtenirZoneFluide(position) {
        for (const zone of this.zones_fluides) {
            if (position >= zone.min && position < zone.max) {
                // TEMPÉRATURE RÉELLE BASÉE SUR CPU
                const temp_cpu_reelle = this.temperature_cpu_actuelle || 50;
                const temp_base_reelle = temp_cpu_reelle + (zone.offset_cpu || 0);

                return {
                    ...zone,
                    temp_base: temp_base_reelle, // VRAIE TEMPÉRATURE CPU + OFFSET
                    temp_cpu_source: temp_cpu_reelle
                };
            }
        }

        // Zone par défaut avec vraie température CPU
        const temp_cpu_reelle = this.temperature_cpu_actuelle || 50;
        const zone_defaut = this.zones_fluides[this.zones_fluides.length - 1];
        return {
            ...zone_defaut,
            temp_base: temp_cpu_reelle + (zone_defaut.offset_cpu || 0), // VRAIE TEMPÉRATURE CPU
            temp_cpu_source: temp_cpu_reelle
        };
    }

    // DÉTERMINER ZONE SELON VRAIE TEMPÉRATURE CPU
    determinerZoneFluide(temperature) {
        // ZONES BASÉES SUR VRAIE TEMPÉRATURE CPU (PAS DE VALEURS FIXES)
        const temp_cpu_reelle = this.temperature_cpu_actuelle || 50;

        // Seuils dynamiques basés sur température CPU réelle
        const seuil_base = temp_cpu_reelle;
        const seuil_tres_chaud = seuil_base + 20;  // CPU + 20°C
        const seuil_chaud = seuil_base + 10;       // CPU + 10°C
        const seuil_modere = seuil_base;           // CPU
        const seuil_tiede = seuil_base - 10;       // CPU - 10°C
        const seuil_froid = seuil_base - 20;       // CPU - 20°C

        if (temperature >= seuil_tres_chaud) return 1; // Zone très chaude
        if (temperature >= seuil_chaud) return 2;      // Zone chaude
        if (temperature >= seuil_modere) return 3;     // Zone modérée
        if (temperature >= seuil_tiede) return 4;      // Zone tiède
        if (temperature >= seuil_froid) return 5;      // Zone fraîche
        return 6; // Zone froide
    }

    // CHARGEMENT RÉEL DE LA MÉMOIRE
    chargerMemoire() {
        try {
            if (fs.existsSync(this.fichierMemoire)) {
                const data = JSON.parse(fs.readFileSync(this.fichierMemoire, 'utf8'));
                this.memoires = new Map(data.memoires || []);
                this.curseurThermique = data.curseurThermique || 0.5;
                this.etat_emotionnel = data.etat_emotionnel || 'NEUTRE';
                this.statistiques_cerveau = data.statistiques_cerveau || this.statistiques_cerveau;
                console.log(`📥 Mémoire chargée: ${this.memoires.size} entrées`);
            } else {
                console.log(`📥 Nouveau fichier mémoire créé`);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement mémoire:`, error.message);
            this.memoires = new Map();
        }
    }

    // CHARGEMENT DES CONNEXIONS NEURONALES
    chargerConnexions() {
        try {
            if (fs.existsSync(this.fichierConnexions)) {
                const data = JSON.parse(fs.readFileSync(this.fichierConnexions, 'utf8'));
                this.connexions = new Map(data.connexions || []);
                this.activations = new Map(data.activations || []);
                console.log(`🔗 Connexions chargées: ${this.connexions.size} réseaux`);
            } else {
                console.log(`🔗 Nouveau réseau de connexions créé`);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement connexions:`, error.message);
            this.connexions = new Map();
            this.activations = new Map();
        }
    }

    // SAUVEGARDE RÉELLE DE LA MÉMOIRE ET DU CERVEAU
    sauvegarderMemoire() {
        try {
            const data = {
                memoires: Array.from(this.memoires.entries()),
                curseurThermique: this.curseurThermique,
                etat_emotionnel: this.etat_emotionnel,
                intensite_emotion: this.intensite_emotion,
                statistiques_cerveau: this.statistiques_cerveau,
                timestamp: Date.now()
            };
            fs.writeFileSync(this.fichierMemoire, JSON.stringify(data, null, 2));
            console.log(`💾 Mémoire sauvegardée: ${this.memoires.size} entrées`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde mémoire:`, error.message);
            return false;
        }
    }

    // SAUVEGARDE DES CONNEXIONS NEURONALES
    sauvegarderConnexions() {
        try {
            const data = {
                connexions: Array.from(this.connexions.entries()),
                activations: Array.from(this.activations.entries()),
                statistiques_cerveau: this.statistiques_cerveau,
                timestamp: Date.now()
            };
            fs.writeFileSync(this.fichierConnexions, JSON.stringify(data, null, 2));
            console.log(`🔗 Connexions sauvegardées: ${this.connexions.size} réseaux`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde connexions:`, error.message);
            return false;
        }
    }

    // DÉTERMINATION DE LA ZONE CÉRÉBRALE SPÉCIALISÉE
    determinerZoneCerebrale(contenu, source, contexte = '') {
        const mots_cles = contenu.toLowerCase();
        const source_lower = source.toLowerCase();

        // Analyse intelligente pour déterminer la zone appropriée
        let zone_selectionnee;
        if (this.estRaisonnement(mots_cles, contexte)) zone_selectionnee = ZONES_CEREBRALES.CORTEX_PREFRONTAL;
        else if (this.estMemoire(source_lower, mots_cles)) zone_selectionnee = ZONES_CEREBRALES.HIPPOCAMPE;
        else if (this.estCode(mots_cles, contexte)) zone_selectionnee = ZONES_CEREBRALES.CORTEX_MOTEUR;
        else if (this.estPerception(source_lower, mots_cles)) zone_selectionnee = ZONES_CEREBRALES.CORTEX_SENSORIEL;
        else if (this.estAutomatisme(source_lower, contexte)) zone_selectionnee = ZONES_CEREBRALES.CERVELET;
        else zone_selectionnee = ZONES_CEREBRALES.TRONC_CEREBRAL; // Zone par défaut

        // CALCULER TEMPÉRATURE RÉELLE BASÉE SUR CPU
        return this.calculerTemperatureZoneCerebrale(zone_selectionnee);
    }

    // CALCUL TEMPÉRATURE RÉELLE ZONE CÉRÉBRALE BASÉE SUR CPU
    calculerTemperatureZoneCerebrale(zone) {
        const temp_cpu_reelle = this.temperature_cpu_actuelle || 50;
        const temp_base_reelle = temp_cpu_reelle + (zone.offset_cpu || 0);

        return {
            ...zone,
            temperature_base: temp_base_reelle, // VRAIE TEMPÉRATURE CPU + OFFSET
            temp_cpu_source: temp_cpu_reelle,
            offset_applique: zone.offset_cpu || 0
        };
    }

    // MÉTHODES D'ANALYSE POUR ZONES CÉRÉBRALES
    estRaisonnement(mots_cles, contexte) {
        const indicateurs = ['raisonnement', 'logique', 'analyse', 'réflexion', 'planification', 'décision', 'stratégie', 'méthode'];
        return indicateurs.some(mot => mots_cles.includes(mot));
    }

    estMemoire(source, mots_cles) {
        return source.includes('formation') || mots_cles.includes('apprentissage') || mots_cles.includes('méthode');
    }

    estCode(mots_cles, contexte) {
        const indicateurs = ['code', 'programmation', 'fonction', 'classe', 'javascript', 'python', 'implementation', 'création'];
        return indicateurs.some(mot => mots_cles.includes(mot));
    }

    estPerception(source, mots_cles) {
        return source.includes('internet') || source.includes('google') || mots_cles.includes('recherche');
    }

    estAutomatisme(source, contexte) {
        return source.includes('ollama') || source.includes('automatique') || source.includes('reflexe');
    }

    // ========== MÉTHODES DE CONNEXIONS NEURONALES ==========

    // CRÉATION DE CONNEXIONS INITIALES ENTRE MÉMOIRES
    creerConnexionsInitiales() {
        console.log('🔗 Création des connexions neuronales initiales...');
        let connexions_creees = 0;

        const memoires_array = Array.from(this.memoires.values());
        for (let i = 0; i < memoires_array.length; i++) {
            for (let j = i + 1; j < memoires_array.length; j++) {
                const memoire1 = memoires_array[i];
                const memoire2 = memoires_array[j];

                // Créer connexion si similarité détectée
                const similarite = this.calculerSimilarite(memoire1, memoire2);
                if (similarite > this.seuil_connexion) {
                    this.creerConnexion(memoire1.id, memoire2.id, similarite);
                    connexions_creees++;
                }
            }
        }

        this.statistiques_cerveau.connexions_totales = connexions_creees;
        console.log(`✅ ${connexions_creees} connexions neuronales créées`);
    }

    // CRÉATION D'UNE CONNEXION ENTRE DEUX MÉMOIRES
    creerConnexion(id1, id2, poids = 0.5) {
        // Connexion bidirectionnelle
        if (!this.connexions.has(id1)) this.connexions.set(id1, []);
        if (!this.connexions.has(id2)) this.connexions.set(id2, []);

        const connexion1 = {
            cible: id2,
            poids: poids,
            activations_recentes: 0,
            derniere_activation: Date.now(),
            type: 'hebbienne'
        };

        const connexion2 = {
            cible: id1,
            poids: poids,
            activations_recentes: 0,
            derniere_activation: Date.now(),
            type: 'hebbienne'
        };

        this.connexions.get(id1).push(connexion1);
        this.connexions.get(id2).push(connexion2);

        this.statistiques_cerveau.connexions_totales++;
    }

    // PROPAGATION D'ACTIVATION (COMME DANS UN VRAI CERVEAU)
    propagationActivation(memoire_source, force = 1.0, profondeur = 0, max_profondeur = 3) {
        if (profondeur > max_profondeur) return;

        // Activer la mémoire source
        this.activations.set(memoire_source.id, force);
        this.statistiques_cerveau.activations_recentes++;

        // Modulation émotionnelle
        const modulation = this.getModulationEmotionnelle();
        force *= modulation.propagation_multiplicateur || 1.0;

        // Propager aux connexions
        const connexions = this.connexions.get(memoire_source.id) || [];
        for (const connexion of connexions) {
            const activation_propagee = force * connexion.poids * 0.8; // Décroissance

            if (activation_propagee > this.seuil_activation) {
                const memoire_cible = this.memoires.get(connexion.cible);
                if (memoire_cible) {
                    // Récursion pour propagation en cascade
                    this.propagationActivation(memoire_cible, activation_propagee, profondeur + 1, max_profondeur);

                    // Apprentissage Hebbien : renforcer connexion utilisée
                    this.apprentissageHebbien(memoire_source, memoire_cible);
                }
            }

            connexion.activations_recentes++;
            connexion.derniere_activation = Date.now();
        }
    }

    // APPRENTISSAGE HEBBIEN : "Neurons that fire together, wire together"
    apprentissageHebbien(memoire1, memoire2) {
        const activation1 = this.activations.get(memoire1.id) || 0;
        const activation2 = this.activations.get(memoire2.id) || 0;

        if (activation1 > 0.5 && activation2 > 0.5) {
            // Renforcer la connexion
            const connexions1 = this.connexions.get(memoire1.id) || [];
            const connexion = connexions1.find(c => c.cible === memoire2.id);

            if (connexion) {
                connexion.poids = Math.min(1.0, connexion.poids + this.force_hebbienne);
            } else {
                // Créer nouvelle connexion si activation simultanée forte
                this.creerConnexion(memoire1.id, memoire2.id, this.force_hebbienne * 2);
            }
        }
    }

    // CALCUL DE SIMILARITÉ ENTRE MÉMOIRES
    calculerSimilarite(memoire1, memoire2) {
        const mots1 = memoire1.contenu.toLowerCase().split(' ');
        const mots2 = memoire2.contenu.toLowerCase().split(' ');

        let mots_communs = 0;
        for (const mot of mots1) {
            if (mot.length > 2 && mots2.includes(mot)) {
                mots_communs++;
            }
        }

        let similarite = mots_communs / Math.max(mots1.length, mots2.length);

        // Bonus si même zone cérébrale
        const zone1 = this.determinerZoneCerebrale(memoire1.contenu, memoire1.source);
        const zone2 = this.determinerZoneCerebrale(memoire2.contenu, memoire2.source);
        if (zone1.id === zone2.id) {
            similarite *= 1.2;
        }

        return similarite;
    }

    // CALCUL RÉEL DE LA TEMPÉRATURE AVEC ZONES CÉRÉBRALES
    calculerTemperature(importance, recence, utilisation, source = 'general', zone_cerebrale = null) {
        // Température de base selon la zone cérébrale
        let temperatureBase = zone_cerebrale ? zone_cerebrale.temperature_base : 45;

        // Modulation selon l'importance
        temperatureBase += importance * 20;

        // Bonus selon la source (adapté aux zones)
        const bonusSource = {
            'Raisonnement': 25,     // Cortex Préfrontal
            'Formation': 20,        // Hippocampe
            'Internet': 15,         // Cortex Sensoriel
            'Google': 15,           // Cortex Sensoriel
            'Ollama': 10,          // Cervelet
            'general': 5           // Tronc Cérébral
        };

        temperatureBase += bonusSource[source] || bonusSource['general'];

        // Facteur récence et utilisation
        temperatureBase += recence * 15;
        temperatureBase += Math.min(utilisation / 5, 10);

        // Modulation émotionnelle
        const modulation = this.getModulationEmotionnelle();
        temperatureBase += modulation.temperature_bonus || 0;

        // Variation pour diversité
        const variation = (Math.random() - 0.5) * 8;
        temperatureBase += variation;

        // Contraindre selon la zone
        const min_temp = zone_cerebrale ? zone_cerebrale.temperature_base - 10 : 25;
        const max_temp = zone_cerebrale ? zone_cerebrale.temperature_base + 15 : 75;

        return Math.max(min_temp, Math.min(max_temp, temperatureBase));
    }

    // ========== MÉTHODES D'ÉTATS ÉMOTIONNELS ==========

    // DÉTECTION AUTOMATIQUE DE L'ÉTAT ÉMOTIONNEL
    detecterEtatEmotionnel(contexte, performance, feedback) {
        let nouvel_etat = this.etat_emotionnel;
        let intensite = this.intensite_emotion;

        // Analyse du contexte pour détecter l'émotion
        if (performance > 0.8) {
            nouvel_etat = 'SATISFACTION';
            intensite = 0.7;
        } else if (feedback && feedback.includes('erreur')) {
            nouvel_etat = 'FRUSTRATION';
            intensite = 0.6;
        } else if (contexte && contexte.includes('nouveau')) {
            nouvel_etat = 'CURIOSITE';
            intensite = 0.8;
        } else if (contexte && (contexte.includes('code') || contexte.includes('création'))) {
            nouvel_etat = 'CREATIVITE';
            intensite = 0.7;
        } else if (contexte && contexte.includes('analyse')) {
            nouvel_etat = 'CONCENTRATION';
            intensite = 0.6;
        }

        this.changerEtatEmotionnel(nouvel_etat, intensite);
    }

    // CHANGEMENT D'ÉTAT ÉMOTIONNEL
    changerEtatEmotionnel(nouvel_etat, intensite = 0.5) {
        if (this.etat_emotionnel !== nouvel_etat) {
            console.log(`🎭 État émotionnel: ${this.etat_emotionnel} → ${nouvel_etat} (${intensite})`);
            this.etat_emotionnel = nouvel_etat;
            this.intensite_emotion = intensite;
        }
    }

    // MODULATION ÉMOTIONNELLE
    getModulationEmotionnelle() {
        const etat = ETATS_EMOTIONNELS[this.etat_emotionnel] || ETATS_EMOTIONNELS.NEUTRE;
        const facteur_intensite = this.intensite_emotion;

        return {
            temperature_bonus: (etat.temperature_bonus || 0) * facteur_intensite,
            boost_apprentissage: (etat.boost_apprentissage || 1.0) * facteur_intensite,
            connexions_bonus: (etat.connexions_bonus || 0) * facteur_intensite,
            propagation_multiplicateur: (etat.precision_bonus || 1.0) * facteur_intensite,
            consolidation_bonus: (etat.consolidation_bonus || 1.0) * facteur_intensite
        };
    }

    // STOCKAGE RÉEL D'UNE MÉMOIRE AVEC CERVEAU ARTIFICIEL
    stocker(contenu, source, importance = 0.5, contexte = '') {
        const id = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const timestamp = Date.now();

        // Déterminer la zone cérébrale appropriée
        const zone_cerebrale = this.determinerZoneCerebrale(contenu, source, contexte);

        // Détecter état émotionnel
        this.detecterEtatEmotionnel(contexte, importance, '');

        // Modulation émotionnelle
        const modulation = this.getModulationEmotionnelle();
        importance *= modulation.boost_apprentissage;

        const memoire = {
            id: id,
            contenu: contenu,
            source: source,
            timestamp: timestamp,
            importance: importance,
            utilisation: 0,
            temperature: this.calculerTemperature(importance, 1.0, 0, source, zone_cerebrale),
            zone: zone_cerebrale.id,
            zone_nom: zone_cerebrale.nom,
            zone_couleur: zone_cerebrale.couleur,
            activation: 0,
            connexions_sortantes: 0,
            derniere_activation: timestamp
        };

        this.memoires.set(id, memoire);

        // Créer connexions avec mémoires existantes similaires
        this.creerConnexionsAutomatiques(memoire);

        // Propager activation initiale
        this.propagationActivation(memoire, importance);

        this.sauvegarderMemoire();
        this.sauvegarderConnexions();

        console.log(`🧠 Mémoire stockée: ${zone_cerebrale.nom} (temp: ${memoire.temperature.toFixed(1)}°C, zone: ${memoire.zone})`);
        return id;
    }

    // CRÉATION AUTOMATIQUE DE CONNEXIONS POUR NOUVELLE MÉMOIRE
    creerConnexionsAutomatiques(nouvelle_memoire) {
        let connexions_creees = 0;
        const modulation = this.getModulationEmotionnelle();

        for (const [id, memoire_existante] of this.memoires) {
            if (id !== nouvelle_memoire.id) {
                const similarite = this.calculerSimilarite(nouvelle_memoire, memoire_existante);
                const seuil_ajuste = this.seuil_connexion - (modulation.connexions_bonus || 0);

                if (similarite > seuil_ajuste) {
                    this.creerConnexion(nouvelle_memoire.id, id, similarite);
                    connexions_creees++;
                }
            }
        }

        nouvelle_memoire.connexions_sortantes = connexions_creees;
        console.log(`🔗 ${connexions_creees} connexions créées pour nouvelle mémoire`);
    }

    // ========== MÉTHODES DE CONSOLIDATION ET PLASTICITÉ ==========

    // DÉMARRAGE DE LA CONSOLIDATION AUTOMATIQUE
    demarrerConsolidationAutomatique() {
        // Consolidation toutes les 2 heures (comme cycles de sommeil)
        setInterval(() => {
            this.consolidationMemoire();
        }, 2 * 60 * 60 * 1000); // 2 heures

        console.log('🌙 Consolidation automatique démarrée (cycle 2h)');
    }

    // CONSOLIDATION MÉMOIRE (SOMMEIL ARTIFICIEL)
    async consolidationMemoire() {
        console.log('🌙 Début consolidation mémoire (sommeil artificiel)...');

        const debut = Date.now();
        let memoires_consolidees = 0;
        let connexions_renforcees = 0;
        let connexions_supprimees = 0;

        // Phase 1: Consolidation déclarative (mémoires importantes)
        for (const [id, memoire] of this.memoires) {
            if (memoire.utilisation > 2 && memoire.importance > 0.6) {
                // Renforcer la mémoire
                memoire.temperature += 5;
                memoire.importance = Math.min(1.0, memoire.importance * 1.1);
                memoires_consolidees++;

                // Renforcer connexions associées
                const connexions = this.connexions.get(id) || [];
                for (const connexion of connexions) {
                    if (connexion.activations_recentes > 1) {
                        connexion.poids = Math.min(1.0, connexion.poids * 1.05);
                        connexions_renforcees++;
                    }
                }
            }
        }

        // Phase 2: Élagage synaptique (suppression connexions faibles)
        connexions_supprimees = await this.elagageConnexions();

        // Phase 3: Réorganisation par zones
        await this.reorganiserZones();

        // Phase 4: Mise à jour statistiques
        this.statistiques_cerveau.consolidations_effectuees++;
        this.historique_consolidation.push({
            timestamp: Date.now(),
            memoires_consolidees,
            connexions_renforcees,
            connexions_supprimees,
            duree: Date.now() - debut
        });

        // Garder seulement les 10 dernières consolidations
        if (this.historique_consolidation.length > 10) {
            this.historique_consolidation.shift();
        }

        this.sauvegarderMemoire();
        this.sauvegarderConnexions();

        console.log(`✅ Consolidation terminée: ${memoires_consolidees} mémoires, ${connexions_renforcees} connexions renforcées, ${connexions_supprimees} supprimées`);
    }

    // ÉLAGAGE DES CONNEXIONS FAIBLES
    async elagageConnexions() {
        let connexions_supprimees = 0;
        const seuil_faible = 0.2;
        const inactivite_max = 7 * 24 * 60 * 60 * 1000; // 7 jours

        for (const [id, connexions] of this.connexions) {
            const connexions_filtrees = connexions.filter(connexion => {
                const inactif = Date.now() - connexion.derniere_activation > inactivite_max;
                const faible = connexion.poids < seuil_faible;

                if (inactif && faible) {
                    connexions_supprimees++;
                    return false;
                }
                return true;
            });

            this.connexions.set(id, connexions_filtrees);
        }

        this.statistiques_cerveau.connexions_totales -= connexions_supprimees;
        return connexions_supprimees;
    }

    // RÉORGANISATION PAR ZONES CÉRÉBRALES
    async reorganiserZones() {
        let memoires_reorganisees = 0;

        for (const [id, memoire] of this.memoires) {
            const nouvelle_zone = this.determinerZoneCerebrale(memoire.contenu, memoire.source);

            if (memoire.zone !== nouvelle_zone.id) {
                memoire.zone = nouvelle_zone.id;
                memoire.zone_nom = nouvelle_zone.nom;
                memoire.zone_couleur = nouvelle_zone.couleur;

                // Recalculer température selon nouvelle zone
                memoire.temperature = this.calculerTemperature(
                    memoire.importance,
                    this.calculerRecence(memoire.timestamp),
                    memoire.utilisation / 10,
                    memoire.source,
                    nouvelle_zone
                );

                memoires_reorganisees++;
            }
        }

        console.log(`🔄 ${memoires_reorganisees} mémoires réorganisées par zones`);
    }

    // ACTIVATION DE LA PLASTICITÉ SYNAPTIQUE
    activerPlasticite() {
        // Plasticité toutes les 30 minutes
        setInterval(() => {
            this.plasticiteSynaptique();
        }, 30 * 60 * 1000); // 30 minutes

        console.log('🧬 Plasticité synaptique activée (cycle 30min)');
    }

    // PLASTICITÉ SYNAPTIQUE (ADAPTATION DYNAMIQUE)
    plasticiteSynaptique() {
        let ajustements = 0;
        const seuil_ltp = 5; // Long Term Potentiation
        const seuil_ltd = 1; // Long Term Depression

        for (const [id, connexions] of this.connexions) {
            for (const connexion of connexions) {
                // LTP: Renforcement si activations fréquentes
                if (connexion.activations_recentes > seuil_ltp) {
                    connexion.poids = Math.min(1.0, connexion.poids * 1.02);
                    ajustements++;
                }

                // LTD: Affaiblissement si peu d'activations
                else if (connexion.activations_recentes < seuil_ltd) {
                    connexion.poids = Math.max(0.1, connexion.poids * 0.98);
                    ajustements++;
                }

                // Reset compteur activations
                connexion.activations_recentes = Math.floor(connexion.activations_recentes * 0.9);
            }
        }

        this.statistiques_cerveau.plasticite_niveau = ajustements / Math.max(1, this.statistiques_cerveau.connexions_totales);

        if (ajustements > 0) {
            console.log(`🧬 Plasticité: ${ajustements} connexions ajustées`);
            this.sauvegarderConnexions();
        }
    }

    // CALCUL DE RÉCENCE
    calculerRecence(timestamp) {
        const age_jours = (Date.now() - timestamp) / (1000 * 60 * 60 * 24);
        return Math.max(0, 1 - (age_jours / 30)); // Décroît sur 30 jours
    }

    // DÉTERMINATION DE ZONE (COMPATIBILITÉ)
    determinerZone(temperature) {
        // Conversion pour compatibilité avec ancien système
        if (temperature >= 65) return 1;
        if (temperature >= 55) return 2;
        if (temperature >= 45) return 3;
        if (temperature >= 35) return 4;
        if (temperature >= 30) return 5;
        return 6;
    }

    // RECHERCHE INTELLIGENTE AVEC PROPAGATION D'ACTIVATION
    rechercher(requete, limite = 5) {
        const resultats = [];
        const requeteLower = requete.toLowerCase();
        const memoires_activees = new Map();

        console.log(`🔍 Recherche intelligente avec propagation: "${requete}"`);

        // Détecter état émotionnel selon la requête
        this.detecterEtatEmotionnel(requete, 0.7, '');

        // Phase 1: Recherche directe avec scoring amélioré
        for (const [id, memoire] of this.memoires) {
            const contenuLower = memoire.contenu.toLowerCase();
            let pertinence = 0;
            let motsCorrespondants = 0;

            // Correspondance exacte (très importante)
            if (contenuLower.includes(requeteLower)) {
                pertinence += 2.5;
            }

            // Correspondance de mots avec pondération par zone
            const motsRequete = requeteLower.split(' ').filter(mot => mot.length > 2);
            const motsContenu = contenuLower.split(' ');

            for (const mot of motsRequete) {
                if (motsContenu.includes(mot)) {
                    pertinence += 1.2;
                    motsCorrespondants++;
                } else if (motsContenu.some(m => m.includes(mot) && m.length < mot.length + 3)) {
                    pertinence += 0.4;
                    motsCorrespondants++;
                }
            }

            // Bonus selon zone cérébrale
            const zone_cerebrale = Object.values(ZONES_CEREBRALES).find(z => z.id === memoire.zone);
            if (zone_cerebrale) {
                // Bonus si requête correspond aux fonctions de la zone
                const fonctions_zone = zone_cerebrale.fonctions;
                for (const fonction of fonctions_zone) {
                    if (requeteLower.includes(fonction)) {
                        pertinence += 0.5;
                    }
                }
            }

            // Vérification spéciale pour les capitales (améliorée)
            if (requeteLower.includes('capitale')) {
                const paysDansRequete = ['france', 'italie', 'espagne', 'allemagne', 'japon', 'brésil', 'bresil', 'maroc', 'australie', 'canada', 'guadeloupe'];
                const paysDansContenu = contenuLower;

                for (const pays of paysDansRequete) {
                    if (requeteLower.includes(pays)) {
                        if (paysDansContenu.includes(pays)) {
                            pertinence += 1.0; // Bonus si bon pays
                        } else {
                            pertinence = 0; // Rejeter si pays différent
                            break;
                        }
                    }
                }
            }

            // Seuil adaptatif selon état émotionnel
            const modulation = this.getModulationEmotionnelle();
            const seuil_base = 1.5;
            const seuil_ajuste = seuil_base * (modulation.precision_bonus || 1.0);

            if (pertinence > seuil_ajuste) {
                memoires_activees.set(id, pertinence);

                // Propager activation aux mémoires connectées
                this.propagationActivation(memoire, pertinence / 3.0);

                memoire.utilisation++;
                memoire.derniere_activation = Date.now();

                resultats.push({
                    id: id,
                    contenu: memoire.contenu,
                    source: memoire.source,
                    pertinence: pertinence,
                    temperature: memoire.temperature,
                    zone: memoire.zone,
                    zone_nom: memoire.zone_nom || `Zone ${memoire.zone}`,
                    zone_couleur: memoire.zone_couleur || '#888888',
                    utilisation: memoire.utilisation,
                    activation: this.activations.get(id) || 0,
                    connexions: (this.connexions.get(id) || []).length
                });
            }
        }

        // Phase 2: Ajouter mémoires activées par propagation
        for (const [id, activation] of this.activations) {
            if (!memoires_activees.has(id) && activation > this.seuil_activation) {
                const memoire = this.memoires.get(id);
                if (memoire) {
                    resultats.push({
                        id: id,
                        contenu: memoire.contenu,
                        source: memoire.source + ' (propagation)',
                        pertinence: activation,
                        temperature: memoire.temperature,
                        zone: memoire.zone,
                        zone_nom: memoire.zone_nom || `Zone ${memoire.zone}`,
                        zone_couleur: memoire.zone_couleur || '#888888',
                        utilisation: memoire.utilisation,
                        activation: activation,
                        connexions: (this.connexions.get(id) || []).length,
                        type: 'propagation'
                    });
                }
            }
        }

        // Tri par pertinence et activation
        resultats.sort((a, b) => {
            const score_a = a.pertinence + (a.activation || 0) * 0.3;
            const score_b = b.pertinence + (b.activation || 0) * 0.3;
            return score_b - score_a;
        });

        const resultatsLimites = resultats.slice(0, limite);
        console.log(`📋 ${resultatsLimites.length} résultats trouvés (${memoires_activees.size} directes + propagation)`);

        // Décroissance des activations
        for (const [id, activation] of this.activations) {
            this.activations.set(id, activation * (1 - this.decay_activation));
        }

        // Sauvegarder si résultats pertinents
        if (resultatsLimites.length > 0) {
            this.sauvegarderMemoire();
            this.sauvegarderConnexions();
        }

        return resultatsLimites;
    }

    // MISE À JOUR THERMIQUE RÉELLE
    mettreAJourTemperatures() {
        const maintenant = Date.now();
        let memoiresModifiees = 0;
        
        for (const [id, memoire] of this.memoires) {
            const ageJours = (maintenant - memoire.timestamp) / (1000 * 60 * 60 * 24);
            const recence = Math.max(0, 1 - (ageJours / 30)); // Décroît sur 30 jours
            
            const nouvelleTemperature = this.calculerTemperature(
                memoire.importance,
                recence,
                Math.min(1, memoire.utilisation / 10), // Normalisation utilisation
                memoire.source
            );
            
            if (Math.abs(nouvelleTemperature - memoire.temperature) > 1) {
                memoire.temperature = nouvelleTemperature;
                memoire.zone = this.determinerZone(nouvelleTemperature);
                memoiresModifiees++;
            }
        }
        
        if (memoiresModifiees > 0) {
            console.log(`🌡️ ${memoiresModifiees} températures mises à jour`);
            this.sauvegarderMemoire();
        }
        
        return memoiresModifiees;
    }

    // OUBLI AUTOMATIQUE RÉEL
    oublierMemoires() {
        const seuilOubli = 15; // Température en dessous de laquelle on oublie
        let memoiresOubliees = 0;
        
        for (const [id, memoire] of this.memoires) {
            if (memoire.temperature < seuilOubli && memoire.utilisation === 0) {
                this.memoires.delete(id);
                memoiresOubliees++;
                console.log(`🗑️ Mémoire oubliée: ${id} (temp: ${memoire.temperature.toFixed(1)}°C)`);
            }
        }
        
        if (memoiresOubliees > 0) {
            this.sauvegarderMemoire();
        }
        
        return memoiresOubliees;
    }

    // STATISTIQUES AVANCÉES DU CERVEAU ARTIFICIEL
    getStatistiquesReelles() {
        const zones = [0, 0, 0, 0, 0, 0];
        const zones_noms = [];
        let temperatureTotal = 0;
        let activations_totales = 0;
        let connexions_par_zone = [0, 0, 0, 0, 0, 0];

        // Analyse par mémoire
        for (const memoire of this.memoires.values()) {
            zones[memoire.zone - 1]++;
            temperatureTotal += memoire.temperature;

            // Compter connexions par zone
            const connexions_memoire = this.connexions.get(memoire.id) || [];
            connexions_par_zone[memoire.zone - 1] += connexions_memoire.length;
        }

        // Analyse des activations
        for (const activation of this.activations.values()) {
            activations_totales += activation;
        }

        // Noms des zones cérébrales
        for (let i = 1; i <= 6; i++) {
            const zone = Object.values(ZONES_CEREBRALES).find(z => z.id === i);
            zones_noms.push(zone ? zone.nom : `Zone ${i}`);
        }

        return {
            // Statistiques de base
            totalEntries: this.memoires.size,
            averageTemperature: this.memoires.size > 0 ? temperatureTotal / this.memoires.size : 0,
            zonesDistribution: zones,
            zones_noms: zones_noms,

            // Statistiques du cerveau
            connexions_totales: this.statistiques_cerveau.connexions_totales,
            activations_recentes: this.statistiques_cerveau.activations_recentes,
            consolidations_effectuees: this.statistiques_cerveau.consolidations_effectuees,
            plasticite_niveau: this.statistiques_cerveau.plasticite_niveau,

            // État émotionnel
            etat_emotionnel: this.etat_emotionnel,
            intensite_emotion: this.intensite_emotion,

            // Répartition par zones
            connexions_par_zone: connexions_par_zone,
            activations_totales: activations_totales,

            // Métadonnées
            curseurThermique: this.curseurThermique,
            fichierMemoire: this.fichierMemoire,
            fichierConnexions: this.fichierConnexions,

            // Historique
            derniere_consolidation: this.historique_consolidation[this.historique_consolidation.length - 1] || null,

            // Performance
            efficacite_recherche: this.activations.size > 0 ? activations_totales / this.activations.size : 0,
            densite_connexions: this.memoires.size > 0 ? this.statistiques_cerveau.connexions_totales / this.memoires.size : 0,

            // NOUVELLES STATISTIQUES TEMPÉRATURE CPU RÉELLE (MOUVEMENT VIVANT)
            temperature_cpu: {
                actuelle: this.temperature_cpu_actuelle,
                precedente: this.temperature_cpu_precedente,
                variation: this.temperature_cpu_actuelle - this.temperature_cpu_precedente,
                moyenne: this.historique_temperatures.length > 0 ?
                    this.historique_temperatures.reduce((a, b) => a + b, 0) / this.historique_temperatures.length :
                    this.temperature_cpu_actuelle,
                min: this.historique_temperatures.length > 0 ? Math.min(...this.historique_temperatures) : this.temperature_cpu_actuelle,
                max: this.historique_temperatures.length > 0 ? Math.max(...this.historique_temperatures) : this.temperature_cpu_actuelle,
                stabilite: this.calculerStabiliteTemperature(),
                historique_taille: this.historique_temperatures.length,
                influence_active: this.influence_cpu_sur_fluidite
            },

            // STATISTIQUES MOUVEMENT FLUIDE VIVANT
            mouvement_vivant: {
                vitesse_curseur: this.vitesse_curseur,
                direction_curseur: this.direction_curseur,
                cycles_automatiques: this.cycle_automatique,
                fluidite_memoire: this.fluidite_memoire,
                inertie_thermique: this.inertie_thermique,
                lissage_mouvement: this.lissage_mouvement,
                mouvement_automatique_actif: this.mouvement_automatique_actif,
                vitesse_min: this.vitesse_min,
                vitesse_max: this.vitesse_max,
                facteur_cpu_vitesse: this.facteur_cpu_vitesse,
                facteur_cpu_direction: this.facteur_cpu_direction
            }
        };
    }

    // STATISTIQUES DÉTAILLÉES PAR ZONE CÉRÉBRALE
    getStatistiquesZones() {
        const stats_zones = {};

        for (const zone_info of Object.values(ZONES_CEREBRALES)) {
            stats_zones[zone_info.nom] = {
                id: zone_info.id,
                nom: zone_info.nom,
                fonctions: zone_info.fonctions,
                couleur: zone_info.couleur,
                temperature_base: zone_info.temperature_base,
                memoires_count: 0,
                temperature_moyenne: 0,
                connexions_count: 0,
                activations_count: 0,
                utilisation_moyenne: 0
            };
        }

        // Calculer statistiques par zone
        for (const memoire of this.memoires.values()) {
            const zone = Object.values(ZONES_CEREBRALES).find(z => z.id === memoire.zone);
            if (zone) {
                const stats = stats_zones[zone.nom];
                stats.memoires_count++;
                stats.temperature_moyenne += memoire.temperature;
                stats.utilisation_moyenne += memoire.utilisation;

                const connexions = this.connexions.get(memoire.id) || [];
                stats.connexions_count += connexions.length;

                const activation = this.activations.get(memoire.id) || 0;
                stats.activations_count += activation;
            }
        }

        // Moyennes
        for (const stats of Object.values(stats_zones)) {
            if (stats.memoires_count > 0) {
                stats.temperature_moyenne /= stats.memoires_count;
                stats.utilisation_moyenne /= stats.memoires_count;
                stats.activations_count /= stats.memoires_count;
            }
        }

        return stats_zones;
    }

    // RAPPORT DE SANTÉ DU CERVEAU
    getRapportSante() {
        const stats = this.getStatistiquesReelles();
        const zones_stats = this.getStatistiquesZones();

        const sante = {
            score_global: 0,
            indicateurs: {
                memoire: {
                    score: Math.min(100, (stats.totalEntries / 10) * 10), // 10 points par 10 mémoires
                    status: stats.totalEntries > 50 ? 'excellent' : stats.totalEntries > 20 ? 'bon' : 'faible'
                },
                connexions: {
                    score: Math.min(100, stats.densite_connexions * 20),
                    status: stats.densite_connexions > 3 ? 'excellent' : stats.densite_connexions > 1 ? 'bon' : 'faible'
                },
                activite: {
                    score: Math.min(100, stats.activations_recentes / 10),
                    status: stats.activations_recentes > 50 ? 'excellent' : stats.activations_recentes > 20 ? 'bon' : 'faible'
                },
                plasticite: {
                    score: Math.min(100, stats.plasticite_niveau * 100),
                    status: stats.plasticite_niveau > 0.1 ? 'excellent' : stats.plasticite_niveau > 0.05 ? 'bon' : 'faible'
                },
                equilibre_zones: {
                    score: this.calculerEquilibreZones(zones_stats),
                    status: 'calculé'
                }
            },
            recommandations: []
        };

        // Calcul score global
        sante.score_global = Object.values(sante.indicateurs).reduce((sum, ind) => sum + ind.score, 0) / Object.keys(sante.indicateurs).length;

        // Recommandations
        if (sante.indicateurs.memoire.score < 50) {
            sante.recommandations.push("Augmenter le nombre de mémoires stockées");
        }
        if (sante.indicateurs.connexions.score < 50) {
            sante.recommandations.push("Améliorer la création de connexions entre mémoires");
        }
        if (sante.indicateurs.activite.score < 50) {
            sante.recommandations.push("Augmenter l'utilisation et les recherches");
        }

        return sante;
    }

    // CALCUL D'ÉQUILIBRE ENTRE ZONES
    calculerEquilibreZones(zones_stats) {
        const counts = Object.values(zones_stats).map(z => z.memoires_count);
        const moyenne = counts.reduce((a, b) => a + b, 0) / counts.length;
        const variance = counts.reduce((sum, count) => sum + Math.pow(count - moyenne, 2), 0) / counts.length;
        const ecart_type = Math.sqrt(variance);

        // Score d'équilibre (100 = parfaitement équilibré)
        return Math.max(0, 100 - (ecart_type / moyenne) * 100);
    }

    // MAINTENANCE AUTOMATIQUE
    maintenance() {
        console.log(`🔧 Maintenance mémoire thermique...`);
        const temperaturesModifiees = this.mettreAJourTemperatures();
        const memoiresOubliees = this.oublierMemoires();
        
        return {
            temperaturesModifiees,
            memoiresOubliees,
            totalMemoires: this.memoires.size
        };
    }
}

module.exports = { MemoireThermiqueReelle };
