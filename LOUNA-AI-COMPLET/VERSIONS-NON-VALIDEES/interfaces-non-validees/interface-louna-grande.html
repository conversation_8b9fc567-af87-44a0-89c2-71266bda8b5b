<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Interface Corrigée</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, 
                #000000 0%, 
                #1a0a1a 25%, 
                #2d1b2d 50%, 
                #4a2c4a 75%, 
                #663d66 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        /* BARRE D'INFORMATIONS EN HAUT */
        .info-bar {
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 2px solid #ff69b4;
            padding: 10px 20px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 80px;
        }

        .info-item {
            text-align: center;
            flex: 1;
            padding: 0 15px;
        }

        .info-title {
            font-size: 12px;
            color: #ff69b4;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 10px #ff69b4;
            margin-bottom: 3px;
        }

        .info-detail {
            font-size: 10px;
            color: #cccccc;
        }

        /* CONTENEUR PRINCIPAL */
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding-top: 80px;
        }

        /* ZONE DE CHAT */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 18px;
            border-radius: 20px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease-in;
        }

        .message.user {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            margin-left: auto;
            text-align: right;
            color: white;
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .message.assistant {
            background: linear-gradient(135deg, #4a2c4a, #663d66);
            margin-right: auto;
            border: 1px solid rgba(255, 105, 180, 0.3);
            box-shadow: 0 4px 15px rgba(74, 44, 74, 0.4);
        }

        .message.system {
            background: rgba(255, 165, 0, 0.2);
            border: 1px solid #ffa500;
            margin: 0 auto;
            text-align: center;
            font-style: italic;
            color: #ffa500;
        }

        .message-source {
            font-size: 11px;
            color: #ff69b4;
            margin-top: 5px;
            font-style: italic;
        }

        /* ZONE DE SAISIE */
        .input-container {
            display: flex;
            gap: 10px;
            padding: 0 20px 20px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #ff69b4;
            border-radius: 25px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 16px;
            outline: none;
            backdrop-filter: blur(10px);
        }

        .chat-input:focus {
            border-color: #ff1493;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 20px rgba(255, 105, 180, 0.6);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* ANIMATIONS */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .pulse {
            animation: pulse 2s ease-in-out;
        }

        /* SCROLLBAR PERSONNALISÉE */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff1493, #dc143c);
        }
    </style>
</head>
<body>
    <!-- BARRE D'INFORMATIONS EN HAUT -->
    <div class="info-bar">
        <div class="info-item">
            <div class="info-title">🧠 QI Évolutif</div>
            <div class="info-value" id="coefficientQI">127</div>
            <div class="info-detail">Intelligence</div>
        </div>
        <div class="info-item">
            <div class="info-title">🧠 Neurones</div>
            <div class="info-value" id="neuronesCount">127.0M</div>
            <div class="info-detail">Auto-expansion</div>
        </div>
        <div class="info-item">
            <div class="info-title">⚡ Accélérateurs</div>
            <div class="info-value" id="accelerateursCount">6</div>
            <div class="info-detail">KYBER Auto-scaling</div>
        </div>
        <div class="info-item">
            <div class="info-title">��️ Mémoire Thermique</div>
            <div class="info-value" id="temperatureMemoire">50.0°C</div>
            <div class="info-detail">Glissement fluide</div>
        </div>
        <div class="info-item">
            <div class="info-title">💬 Conversations</div>
            <div class="info-value" id="conversationsCount">0</div>
            <div class="info-detail">Contexte actif</div>
        </div>
    </div>

    <!-- CONTENEUR PRINCIPAL -->
    <div class="container">
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message system">
                    🚀 LOUNA-AI EXPERT OPÉRATIONNEL - Mémoire thermique glissante active
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" class="chat-input" id="chatInput" 
                       placeholder="Posez votre question à LOUNA-AI..." 
                       onkeypress="handleKeyPress(event)">
                <button class="send-button" id="sendButton" onclick="sendMessage()">
                    Envoyer
                </button>
            </div>
        </div>
    </div>

    <script>
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');

        // FONCTION POUR AJOUTER UN MESSAGE
        function addMessage(content, type, source = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let messageContent = content;
            if (source && type === 'assistant') {
                messageContent += `<div class="message-source">Source: ${source}</div>`;
            }
            
            messageDiv.innerHTML = messageContent;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // FONCTION POUR METTRE À JOUR LE QI
        function updateQI(newQI) {
            const qiElement = document.getElementById('coefficientQI');
            qiElement.textContent = newQI;
            qiElement.classList.add('pulse');
            setTimeout(() => qiElement.classList.remove('pulse'), 2000);
        }

        // FONCTION POUR METTRE À JOUR LES NEURONES
        function updateNeurones(qi) {
            const neuronesElement = document.getElementById('neuronesCount');
            const neurones = (qi * 1.0).toFixed(1);
            neuronesElement.textContent = neurones + 'M';
            neuronesElement.classList.add('pulse');
            setTimeout(() => neuronesElement.classList.remove('pulse'), 2000);
        }

        // FONCTION POUR METTRE À JOUR LES ACCÉLÉRATEURS
        function updateAccelerateurs(qi) {
            const accelerateursElement = document.getElementById('accelerateursCount');
            const accelerateurs = Math.min(15, Math.floor(qi / 20));
            accelerateursElement.textContent = accelerateurs;
            accelerateursElement.classList.add('pulse');
            setTimeout(() => accelerateursElement.classList.remove('pulse'), 2000);
        }

        // FONCTION POUR METTRE À JOUR LA TEMPÉRATURE
        function updateTemperature(temp) {
            const tempElement = document.getElementById('temperatureMemoire');
            tempElement.textContent = temp + '°C';
            tempElement.classList.add('pulse');
            setTimeout(() => tempElement.classList.remove('pulse'), 2000);
        }

        // FONCTION POUR METTRE À JOUR LES CONVERSATIONS
        function updateConversations(count) {
            const convElement = document.getElementById('conversationsCount');
            convElement.textContent = count;
            convElement.classList.add('pulse');
            setTimeout(() => convElement.classList.remove('pulse'), 2000);
        }

        // FONCTION POUR ENVOYER UN MESSAGE
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // Ajouter le message de l'utilisateur
            addMessage(message, 'user');
            chatInput.value = '';
            sendButton.disabled = true;

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success) {
                    // Ajouter la réponse de l'assistant
                    addMessage(data.response, 'assistant', data.source);
                    
                    // Mettre à jour toutes les informations
                    if (data.coefficient_intellectuel) {
                        updateQI(data.coefficient_intellectuel);
                        updateNeurones(data.coefficient_intellectuel);
                        updateAccelerateurs(data.coefficient_intellectuel);
                    }
                } else {
                    addMessage('Erreur: ' + (data.error || 'Réponse invalide'), 'system');
                }

            } catch (error) {
                console.error('Erreur envoi message:', error);
                addMessage('Erreur de connexion au serveur', 'system');
            } finally {
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        // FONCTION POUR GÉRER LA TOUCHE ENTRÉE
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !sendButton.disabled) {
                sendMessage();
            }
        }

        // FONCTION POUR CHARGER LES STATISTIQUES
        async function loadStats() {
            try {
                const response = await fetch('/stats');
                const data = await response.json();
                
                if (data.success && data.stats) {
                    const stats = data.stats;
                    
                    // Mettre à jour QI
                    if (data.coefficient_intellectuel) {
                        updateQI(data.coefficient_intellectuel);
                        updateNeurones(data.coefficient_intellectuel);
                        updateAccelerateurs(data.coefficient_intellectuel);
                    }
                    
                    // Mettre à jour température
                    if (stats.memoire_thermique_glissante && stats.memoire_thermique_glissante.curseurThermique) {
                        updateTemperature(parseFloat(stats.memoire_thermique_glissante.curseurThermique).toFixed(1));
                    }
                    
                    // Mettre à jour conversations
                    if (stats.memoire_conversationnelle && stats.memoire_conversationnelle.total_conversations) {
                        updateConversations(stats.memoire_conversationnelle.total_conversations);
                    }
                }
            } catch (error) {
                console.error('Erreur chargement stats:', error);
            }
        }

        // CHARGER LES STATS AU DÉMARRAGE ET PÉRIODIQUEMENT
        loadStats();
        setInterval(loadStats, 10000); // Toutes les 10 secondes

        // FOCUS SUR L'INPUT AU CHARGEMENT
        chatInput.focus();
    </script>
</body>
</html>
