#!/usr/bin/env node

/**
 * 🧪 TEST SYSTÈME ÉMOTIONNEL
 */

const axios = require('axios').default;

async function testerEmotions() {
    console.log('🎭 TEST SYSTÈME ÉMOTIONNEL');
    console.log('==========================');
    
    try {
        // Test API Status
        console.log('\n📊 TEST API ÉMOTIONS STATUS');
        const response_status = await axios.get('http://localhost:3000/api/emotions/status', { timeout: 5000 });
        console.log('✅ API Status:', JSON.stringify(response_status.data, null, 2));
        
        // Test API Idées
        console.log('\n💡 TEST API IDÉES CRÉATIVES');
        const response_idees = await axios.get('http://localhost:3000/api/emotions/idees', { timeout: 5000 });
        console.log('✅ API Idées:', JSON.stringify(response_idees.data, null, 2));
        
        console.log('\n🎉 SYSTÈME ÉMOTIONNEL OPÉRATIONNEL !');
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
    }
}

testerEmotions();
