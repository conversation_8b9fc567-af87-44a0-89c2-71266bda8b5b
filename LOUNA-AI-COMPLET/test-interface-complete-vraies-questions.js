#!/usr/bin/env node

/**
 * TEST COMPLET DE L'INTERFACE LOUNA-AI AVEC VRAIES QUESTIONS
 * Test automatisé pour vérifier le fonctionnement complet de l'interface
 */

const axios = require('axios');
const fs = require('fs');

class TesteurInterfaceComplete {
    constructor() {
        this.baseUrl = 'http://localhost:3001';
        this.resultats = [];
        this.conversationId = Date.now();
        
        console.log('🧪 TESTEUR INTERFACE LOUNA-AI COMPLÈTE');
        console.log('=====================================');
    }

    async testerConnexionServeur() {
        console.log('\n🔍 TEST 1: Connexion au serveur...');
        try {
            const response = await axios.get(`${this.baseUrl}/api/stats`);
            console.log('✅ Serveur accessible');
            console.log(`📊 QI actuel: ${response.data.coefficient_intellectuel || 'N/A'}`);
            console.log(`🧠 Mémoires: ${response.data.stats?.memoire_thermique?.totalEntries || 'N/A'}`);
            console.log(`🌡️ Température: ${response.data.stats?.memoire_thermique?.currentTemperature?.toFixed(1) || 'N/A'}°C`);
            return true;
        } catch (error) {
            console.log('❌ Erreur connexion serveur:', error.message);
            return false;
        }
    }

    async testerQuestionSimple() {
        console.log('\n🗣️ TEST 2: Question simple...');
        const question = "Bonjour LOUNA-AI, comment allez-vous ?";
        
        try {
            const response = await axios.post(`${this.baseUrl}/api/chat`, {
                message: question
            });

            if (response.data.success) {
                console.log('✅ Réponse reçue');
                console.log(`📝 Question: ${question}`);
                console.log(`🤖 Réponse: ${response.data.reponse?.substring(0, 100)}...`);
                console.log(`🧠 QI après réponse: ${response.data.qi_actuel || 'N/A'}`);
                
                this.resultats.push({
                    test: 'Question simple',
                    question: question,
                    reponse: response.data.reponse,
                    qi: response.data.qi_actuel,
                    succes: true
                });
                return true;
            } else {
                console.log('❌ Erreur dans la réponse:', response.data.error);
                return false;
            }
        } catch (error) {
            console.log('❌ Erreur requête:', error.message);
            return false;
        }
    }

    async testerSuiviConversation() {
        console.log('\n🔄 TEST 3: Suivi de conversation...');
        
        const questions = [
            "Parlez-moi de l'Italie",
            "Quelle est sa capitale ?",
            "Et sa population ?",
            "Maintenant parlez-moi de la France",
            "Quelle est sa capitale ?"
        ];

        let conversationReussie = true;
        
        for (let i = 0; i < questions.length; i++) {
            const question = questions[i];
            console.log(`\n   Question ${i+1}: ${question}`);
            
            try {
                const response = await axios.post(`${this.baseUrl}/api/chat`, {
                    message: question,
                    conversationId: this.conversationId
                });

                if (response.data.success) {
                    const reponse = response.data.reponse;
                    console.log(`   🤖 Réponse: ${reponse.substring(0, 80)}...`);
                    
                    // Vérifications spécifiques
                    if (i === 1 && !reponse.toLowerCase().includes('rome')) {
                        console.log('   ⚠️ ATTENTION: La réponse ne mentionne pas Rome pour la capitale de l\'Italie');
                        conversationReussie = false;
                    }
                    
                    if (i === 4 && !reponse.toLowerCase().includes('paris')) {
                        console.log('   ⚠️ ATTENTION: La réponse ne mentionne pas Paris pour la capitale de la France');
                        conversationReussie = false;
                    }
                    
                    this.resultats.push({
                        test: `Suivi conversation ${i+1}`,
                        question: question,
                        reponse: reponse,
                        qi: response.data.qi_actuel,
                        succes: true
                    });
                } else {
                    console.log(`   ❌ Erreur: ${response.data.error}`);
                    conversationReussie = false;
                }
                
                // Pause entre questions
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.log(`   ❌ Erreur requête: ${error.message}`);
                conversationReussie = false;
            }
        }
        
        if (conversationReussie) {
            console.log('✅ Suivi de conversation réussi');
        } else {
            console.log('❌ Problèmes détectés dans le suivi de conversation');
        }
        
        return conversationReussie;
    }

    async testerQuestionsComplexes() {
        console.log('\n🧠 TEST 4: Questions complexes...');
        
        const questionsComplexes = [
            {
                question: "Expliquez la théorie de la relativité d'Einstein en termes simples",
                motsCles: ['einstein', 'relativité', 'temps', 'espace', 'vitesse', 'lumière']
            },
            {
                question: "Qu'est-ce que l'intelligence artificielle et comment fonctionne-t-elle ?",
                motsCles: ['intelligence', 'artificielle', 'algorithme', 'apprentissage', 'données']
            },
            {
                question: "Résolvez cette équation: 2x + 5 = 13",
                motsCles: ['x', '4', 'équation', 'résolution']
            }
        ];

        let questionsReussies = 0;
        
        for (const test of questionsComplexes) {
            console.log(`\n   Question: ${test.question}`);
            
            try {
                const response = await axios.post(`${this.baseUrl}/api/chat`, {
                    message: test.question
                });

                if (response.data.success) {
                    const reponse = response.data.reponse.toLowerCase();
                    console.log(`   🤖 Réponse: ${response.data.reponse.substring(0, 100)}...`);
                    
                    // Vérifier la présence de mots-clés
                    const motsTrouves = test.motsCles.filter(mot => 
                        reponse.includes(mot.toLowerCase())
                    );
                    
                    console.log(`   🔍 Mots-clés trouvés: ${motsTrouves.length}/${test.motsCles.length}`);
                    
                    if (motsTrouves.length >= test.motsCles.length / 2) {
                        console.log('   ✅ Réponse pertinente');
                        questionsReussies++;
                    } else {
                        console.log('   ⚠️ Réponse peu pertinente');
                    }
                    
                    this.resultats.push({
                        test: 'Question complexe',
                        question: test.question,
                        reponse: response.data.reponse,
                        motsClesTrouves: motsTrouves,
                        pertinence: motsTrouves.length >= test.motsCles.length / 2,
                        succes: true
                    });
                } else {
                    console.log(`   ❌ Erreur: ${response.data.error}`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 1500));
                
            } catch (error) {
                console.log(`   ❌ Erreur requête: ${error.message}`);
            }
        }
        
        console.log(`\n✅ Questions complexes réussies: ${questionsReussies}/${questionsComplexes.length}`);
        return questionsReussies === questionsComplexes.length;
    }

    async testerMemoireThermique() {
        console.log('\n🌡️ TEST 5: Mémoire thermique...');
        
        try {
            const response = await axios.get(`${this.baseUrl}/api/stats`);
            
            if (response.data.success && response.data.stats?.memoire_thermique) {
                const memoire = response.data.stats.memoire_thermique;
                console.log('✅ Mémoire thermique active');
                console.log(`📊 Entrées totales: ${memoire.totalEntries}`);
                console.log(`🌡️ Température actuelle: ${memoire.currentTemperature?.toFixed(1)}°C`);
                console.log(`🎯 Zone active: ${memoire.currentZone}`);
                
                return memoire.totalEntries > 0 && memoire.currentTemperature > 0;
            } else {
                console.log('❌ Mémoire thermique non accessible');
                return false;
            }
        } catch (error) {
            console.log('❌ Erreur test mémoire thermique:', error.message);
            return false;
        }
    }

    async genererRapport() {
        console.log('\n📊 GÉNÉRATION DU RAPPORT DE TEST');
        console.log('=================================');
        
        const rapport = {
            timestamp: new Date().toISOString(),
            tests_effectues: this.resultats.length,
            tests_reussis: this.resultats.filter(r => r.succes).length,
            resultats_detailles: this.resultats,
            recommandations: []
        };
        
        // Analyse des résultats
        const tauxReussite = (rapport.tests_reussis / rapport.tests_effectues) * 100;
        
        console.log(`✅ Tests réussis: ${rapport.tests_reussis}/${rapport.tests_effectues} (${tauxReussite.toFixed(1)}%)`);
        
        if (tauxReussite >= 90) {
            console.log('🎉 EXCELLENT: Interface fonctionne parfaitement !');
        } else if (tauxReussite >= 70) {
            console.log('✅ BON: Interface fonctionne bien avec quelques améliorations possibles');
            rapport.recommandations.push('Améliorer la pertinence des réponses complexes');
        } else {
            console.log('⚠️ ATTENTION: Problèmes détectés dans l\'interface');
            rapport.recommandations.push('Vérifier la configuration du serveur');
            rapport.recommandations.push('Améliorer le système de réponses');
        }
        
        // Sauvegarder le rapport
        const nomFichier = `rapport-test-interface-${Date.now()}.json`;
        fs.writeFileSync(nomFichier, JSON.stringify(rapport, null, 2));
        console.log(`📄 Rapport sauvegardé: ${nomFichier}`);
        
        return rapport;
    }

    async executerTousLesTests() {
        console.log('🚀 DÉMARRAGE DES TESTS COMPLETS\n');
        
        const tests = [
            () => this.testerConnexionServeur(),
            () => this.testerQuestionSimple(),
            () => this.testerSuiviConversation(),
            () => this.testerQuestionsComplexes(),
            () => this.testerMemoireThermique()
        ];
        
        let testsReussis = 0;
        
        for (const test of tests) {
            const resultat = await test();
            if (resultat) testsReussis++;
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log(`\n🏁 TESTS TERMINÉS: ${testsReussis}/${tests.length} réussis`);
        
        await this.genererRapport();
        
        return testsReussis === tests.length;
    }
}

// Exécution des tests
if (require.main === module) {
    const testeur = new TesteurInterfaceComplete();
    testeur.executerTousLesTests()
        .then(succes => {
            if (succes) {
                console.log('\n🎉 TOUS LES TESTS RÉUSSIS ! Interface parfaitement fonctionnelle !');
                process.exit(0);
            } else {
                console.log('\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ. Vérifiez les logs ci-dessus.');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n❌ ERREUR CRITIQUE:', error);
            process.exit(1);
        });
}

module.exports = TesteurInterfaceComplete;
