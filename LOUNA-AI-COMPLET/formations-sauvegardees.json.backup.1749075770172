[{"id": "prog_1749035540536_7khp6gj8e", "timestamp": 1749035540536, "sujet": "Formation JavaScript Avancé", "type": "programmation", "contenu": {"code": "\n// Classe moderne avec méthodes avancées\nclass GestionnaireIA {\n    constructor(nom, qi = 100) {\n        this.nom = nom;\n        this.qi = qi;\n        this.memoires = new Map();\n        this.competences = new Set();\n    }\n\n    async apprendreCompetence(competence, difficulte = 1) {\n        try {\n            console.log(`🎓 Apprentissage: ${competence}`);\n\n            // Simulation d'apprentissage\n            const tempsApprentissage = difficulte * 1000;\n            await new Promise(resolve => setTimeout(resolve, tempsApprentissage));\n\n            this.competences.add(competence);\n            this.qi += difficulte * 5;\n\n            // Stocker en mémoire\n            this.memoires.set(competence, {\n                timestamp: Date.now(),\n                difficulte: difficulte,\n                maitrise: Math.random() * 0.3 + 0.7 // 70-100%\n            });\n\n            return {\n                success: true,\n                message: `Compétence ${competence} acquise ! QI: ${this.qi}`,\n                nouvelleCompetence: competence\n            };\n        } catch (error) {\n            return { success: false, error: error.message };\n        }\n    }\n\n    evaluerPerformance() {\n        const totalCompetences = this.competences.size;\n        const qiMoyen = this.qi / Math.max(totalCompetences, 1);\n\n        return {\n            competences: totalCompetences,\n            qi: this.qi,\n            qiMoyen: Math.round(qiMoyen),\n            niveau: qiMoyen > 150 ? 'Expert' : qiMoyen > 100 ? 'Avancé' : 'Débutant'\n        };\n    }\n}\n\n// Utilisation\nconst louna = new GestionnaireIA('LOUNA-AI', 355);\nlouna.apprendreCompetence('JavaScript Avancé', 3)\n    .then(resultat => console.log(resultat.message));\n                ", "explication": "Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "langage": "Inconnu", "complexite": "Élevée", "patterns": ["<PERSON><PERSON><PERSON><PERSON>"], "bonnesPratiques": 60}, "niveau": "Intermédiaire", "competencesAcquises": ["Programmation Orientée Objet", "Programmation Asynchrone", "Gestion d'Erreurs", "Optimisation"]}, {"id": "prog_1749072250561_iy6la2cyo", "timestamp": 1749072250561, "sujet": "Formation JavaScript Avancé", "type": "programmation", "contenu": {"code": "\n// Classe moderne avec méthodes avancées\nclass GestionnaireIA {\n    constructor(nom, qi = 100) {\n        this.nom = nom;\n        this.qi = qi;\n        this.memoires = new Map();\n        this.competences = new Set();\n    }\n\n    async apprendreCompetence(competence, difficulte = 1) {\n        try {\n            console.log(`🎓 Apprentissage: ${competence}`);\n\n            // Simulation d'apprentissage\n            const tempsApprentissage = difficulte * 1000;\n            await new Promise(resolve => setTimeout(resolve, tempsApprentissage));\n\n            this.competences.add(competence);\n            this.qi += difficulte * 5;\n\n            // Stocker en mémoire\n            this.memoires.set(competence, {\n                timestamp: Date.now(),\n                difficulte: difficulte,\n                maitrise: Math.random() * 0.3 + 0.7 // 70-100%\n            });\n\n            return {\n                success: true,\n                message: `Compétence ${competence} acquise ! QI: ${this.qi}`,\n                nouvelleCompetence: competence\n            };\n        } catch (error) {\n            return { success: false, error: error.message };\n        }\n    }\n\n    evaluerPerformance() {\n        const totalCompetences = this.competences.size;\n        const qiMoyen = this.qi / Math.max(totalCompetences, 1);\n\n        return {\n            competences: totalCompetences,\n            qi: this.qi,\n            qiMoyen: Math.round(qiMoyen),\n            niveau: qiMoyen > 150 ? 'Expert' : qiMoyen > 100 ? 'Avancé' : 'Débutant'\n        };\n    }\n}\n\n// Utilisation\nconst louna = new GestionnaireIA('LOUNA-AI', 355);\nlouna.apprendreCompetence('JavaScript Avancé', 3)\n    .then(resultat => console.log(resultat.message));\n                ", "explication": "Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "langage": "Inconnu", "complexite": "Élevée", "patterns": ["<PERSON><PERSON><PERSON><PERSON>"], "bonnesPratiques": 60}, "niveau": "Intermédiaire", "competencesAcquises": ["Programmation Orientée Objet", "Programmation Asynchrone", "Gestion d'Erreurs", "Optimisation"]}, {"id": "prog_1749073087839_ozq8a66yp", "timestamp": 1749073087839, "sujet": "Formation JavaScript Avancé", "type": "programmation", "contenu": {"code": "\n// Classe moderne avec méthodes avancées\nclass GestionnaireIA {\n    constructor(nom, qi = 100) {\n        this.nom = nom;\n        this.qi = qi;\n        this.memoires = new Map();\n        this.competences = new Set();\n    }\n\n    async apprendreCompetence(competence, difficulte = 1) {\n        try {\n            console.log(`🎓 Apprentissage: ${competence}`);\n\n            // Simulation d'apprentissage\n            const tempsApprentissage = difficulte * 1000;\n            await new Promise(resolve => setTimeout(resolve, tempsApprentissage));\n\n            this.competences.add(competence);\n            this.qi += difficulte * 5;\n\n            // Stocker en mémoire\n            this.memoires.set(competence, {\n                timestamp: Date.now(),\n                difficulte: difficulte,\n                maitrise: Math.random() * 0.3 + 0.7 // 70-100%\n            });\n\n            return {\n                success: true,\n                message: `Compétence ${competence} acquise ! QI: ${this.qi}`,\n                nouvelleCompetence: competence\n            };\n        } catch (error) {\n            return { success: false, error: error.message };\n        }\n    }\n\n    evaluerPerformance() {\n        const totalCompetences = this.competences.size;\n        const qiMoyen = this.qi / Math.max(totalCompetences, 1);\n\n        return {\n            competences: totalCompetences,\n            qi: this.qi,\n            qiMoyen: Math.round(qiMoyen),\n            niveau: qiMoyen > 150 ? 'Expert' : qiMoyen > 100 ? 'Avancé' : 'Débutant'\n        };\n    }\n}\n\n// Utilisation\nconst louna = new GestionnaireIA('LOUNA-AI', 355);\nlouna.apprendreCompetence('JavaScript Avancé', 3)\n    .then(resultat => console.log(resultat.message));\n                ", "explication": "Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "langage": "Inconnu", "complexite": "Élevée", "patterns": ["<PERSON><PERSON><PERSON><PERSON>"], "bonnesPratiques": 60}, "niveau": "Intermédiaire", "competencesAcquises": ["Programmation Orientée Objet", "Programmation Asynchrone", "Gestion d'Erreurs", "Optimisation"]}, {"id": "prog_1749075709391_wcpvfx04y", "timestamp": 1749075709391, "sujet": "Formation JavaScript Avancé", "type": "programmation", "contenu": {"code": "\n// Classe moderne avec méthodes avancées\nclass GestionnaireIA {\n    constructor(nom, qi = 100) {\n        this.nom = nom;\n        this.qi = qi;\n        this.memoires = new Map();\n        this.competences = new Set();\n    }\n\n    async apprendreCompetence(competence, difficulte = 1) {\n        try {\n            console.log(`🎓 Apprentissage: ${competence}`);\n\n            // Simulation d'apprentissage\n            const tempsApprentissage = difficulte * 1000;\n            await new Promise(resolve => setTimeout(resolve, tempsApprentissage));\n\n            this.competences.add(competence);\n            this.qi += difficulte * 5;\n\n            // Stocker en mémoire\n            this.memoires.set(competence, {\n                timestamp: Date.now(),\n                difficulte: difficulte,\n                maitrise: Math.random() * 0.3 + 0.7 // 70-100%\n            });\n\n            return {\n                success: true,\n                message: `Compétence ${competence} acquise ! QI: ${this.qi}`,\n                nouvelleCompetence: competence\n            };\n        } catch (error) {\n            return { success: false, error: error.message };\n        }\n    }\n\n    evaluerPerformance() {\n        const totalCompetences = this.competences.size;\n        const qiMoyen = this.qi / Math.max(totalCompetences, 1);\n\n        return {\n            competences: totalCompetences,\n            qi: this.qi,\n            qiMoyen: Math.round(qiMoyen),\n            niveau: qiMoyen > 150 ? 'Expert' : qiMoyen > 100 ? 'Avancé' : 'Débutant'\n        };\n    }\n}\n\n// Utilisation\nconst louna = new GestionnaireIA('LOUNA-AI', 355);\nlouna.apprendreCompetence('JavaScript Avancé', 3)\n    .then(resultat => console.log(resultat.message));\n                ", "explication": "Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "langage": "Inconnu", "complexite": "Élevée", "patterns": ["<PERSON><PERSON><PERSON><PERSON>"], "bonnesPratiques": 60}, "niveau": "Intermédiaire", "competencesAcquises": ["Programmation Orientée Objet", "Programmation Asynchrone", "Gestion d'Erreurs", "Optimisation"]}]