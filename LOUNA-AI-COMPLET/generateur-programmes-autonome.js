/**
 * GÉNÉRATEUR DE PROGRAMMES AUTONOME POUR LOUNA-AI
 * Crée des programmes personnalisés selon les spécifications analysées
 */

const fs = require('fs');
const path = require('path');

class GenerateurProgrammesAutonome {
    constructor() {
        // Templates de code par type de tâche
        this.templatesCode = new Map();
        this.programmesCrees = new Map();
        this.dossierSortie = path.join(__dirname, 'programmes-crees');
        
        // Créer le dossier de sortie
        if (!fs.existsSync(this.dossierSortie)) {
            fs.mkdirSync(this.dossierSortie, { recursive: true });
        }
        
        this.initialiserTemplates();
    }

    // CRÉER UN PROGRAMME SELON LES SPÉCIFICATIONS
    async creerProgramme(specifications) {
        console.log(`🚀 Création du programme: ${specifications.nom_suggere}`);
        
        try {
            // 1. Sélectionner le template approprié
            const template = this.selectionnerTemplate(specifications);
            
            // 2. Générer le code
            const codeGenere = this.genererCode(template, specifications);
            
            // 3. Créer les fichiers
            const fichiersCrees = await this.creerFichiers(codeGenere, specifications);
            
            // 4. Créer la documentation
            const documentation = this.creerDocumentation(specifications, fichiersCrees);
            
            // 5. Enregistrer le programme
            const programme = {
                nom: specifications.nom_suggere,
                description: specifications.description,
                fichiers: fichiersCrees,
                documentation: documentation,
                specifications: specifications,
                date_creation: new Date().toISOString(),
                version: '1.0.0'
            };
            
            this.programmesCrees.set(specifications.nom_suggere, programme);
            
            return {
                success: true,
                programme: programme,
                message: `Programme "${specifications.nom_suggere}" créé avec succès !`
            };
            
        } catch (error) {
            console.error(`❌ Erreur création programme: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: `Erreur lors de la création: ${error.message}`
            };
        }
    }

    // SÉLECTIONNER LE TEMPLATE APPROPRIÉ
    selectionnerTemplate(specifications) {
        const type = this.determinerTypeTemplate(specifications);
        
        if (this.templatesCode.has(type)) {
            return this.templatesCode.get(type);
        }
        
        // Template générique par défaut
        return this.templatesCode.get('generique');
    }

    determinerTypeTemplate(specifications) {
        const desc = (specifications.description || "").toLowerCase();
        
        if (desc.includes('calcul') || desc.includes('mathématique')) return 'calculatrice';
        if (desc.includes('fichier') || desc.includes('organise')) return 'gestionnaire_fichiers';
        if (desc.includes('texte') || desc.includes('format')) return 'traitement_texte';
        if (desc.includes('image') || desc.includes('photo')) return 'traitement_image';
        if (desc.includes('télécharge') || desc.includes('download')) return 'downloader';
        if (desc.includes('données') || desc.includes('csv')) return 'analyseur_donnees';
        
        return 'generique';
    }

    // GÉNÉRER LE CODE
    genererCode(template, specifications) {
        const code = {
            principal: this.genererCodePrincipal(template, specifications),
            utilitaires: this.genererCodeUtilitaires(template, specifications),
            tests: this.genererCodeTests(template, specifications),
            package: this.genererPackageJson(specifications)
        };
        
        return code;
    }

    genererCodePrincipal(template, specs) {
        let code = template.header.replace(/{{NOM_PROGRAMME}}/g, specs.nom_suggere);
        code = code.replace(/{{DESCRIPTION}}/g, specs.description);
        code = code.replace(/{{DATE}}/g, new Date().toISOString());
        
        // Générer les imports
        code += this.genererImports(specs.technologies);
        
        // Générer la classe principale
        code += this.genererClassePrincipale(template, specs);
        
        // Générer la fonction main
        code += this.genererFonctionMain(template, specs);
        
        return code;
    }

    genererImports(technologies) {
        let imports = '\n// Imports requis\n';
        
        if (technologies.includes('fs')) {
            imports += 'const fs = require(\'fs\');\n';
        }
        if (technologies.includes('path')) {
            imports += 'const path = require(\'path\');\n';
        }
        if (technologies.includes('axios')) {
            imports += 'const axios = require(\'axios\');\n';
        }
        if (technologies.includes('Sharp')) {
            imports += 'const sharp = require(\'sharp\');\n';
        }
        
        return imports + '\n';
    }

    genererClassePrincipale(template, specs) {
        const nomClasse = this.convertirEnNomClasse(specs.nom_suggere);
        
        let code = `class ${nomClasse} {\n`;
        code += '    constructor() {\n';
        code += '        this.version = "1.0.0";\n';
        code += `        this.description = "${specs.description}";\n`;
        code += '        console.log(`🚀 ${this.description} initialisé`);\n';
        code += '    }\n\n';
        
        // Ajouter les méthodes du template
        code += template.methodes;
        
        code += '}\n\n';
        
        return code;
    }

    genererFonctionMain(template, specs) {
        let code = '// Fonction principale\n';
        code += 'async function main() {\n';
        code += '    try {\n';
        code += `        const programme = new ${this.convertirEnNomClasse(specs.nom_suggere)}();\n`;
        code += '        \n';
        code += '        // Traitement principal\n';
        code += template.logique_principale;
        code += '        \n';
        code += '        console.log("✅ Traitement terminé avec succès !");\n';
        code += '    } catch (error) {\n';
        code += '        console.error("❌ Erreur:", error.message);\n';
        code += '        process.exit(1);\n';
        code += '    }\n';
        code += '}\n\n';
        code += '// Exécuter si appelé directement\n';
        code += 'if (require.main === module) {\n';
        code += '    main();\n';
        code += '}\n\n';
        code += `module.exports = ${this.convertirEnNomClasse(specs.nom_suggere)};\n`;
        
        return code;
    }

    convertirEnNomClasse(nom) {
        return nom.split('_')
            .map(mot => mot.charAt(0).toUpperCase() + mot.slice(1))
            .join('');
    }

    genererCodeUtilitaires(template, specs) {
        return `/**
 * Utilitaires pour ${specs.nom_suggere}
 */

class Utilitaires {
    static formaterDate(date = new Date()) {
        return date.toISOString().split('T')[0];
    }
    
    static validerEntree(valeur, type = 'string') {
        if (type === 'string') return typeof valeur === 'string' && valeur.length > 0;
        if (type === 'number') return typeof valeur === 'number' && !isNaN(valeur);
        if (type === 'array') return Array.isArray(valeur);
        return false;
    }
    
    static afficherProgres(actuel, total) {
        const pourcentage = Math.round((actuel / total) * 100);
        const barre = '█'.repeat(Math.floor(pourcentage / 5)) + '░'.repeat(20 - Math.floor(pourcentage / 5));
        process.stdout.write(\`\\r[\${barre}] \${pourcentage}%\`);
    }
}

module.exports = Utilitaires;
`;
    }

    genererCodeTests(template, specs) {
        const nomClasse = this.convertirEnNomClasse(specs.nom_suggere);
        
        return `/**
 * Tests pour ${specs.nom_suggere}
 */

const ${nomClasse} = require('./${specs.nom_suggere}');

class Tests {
    static async executerTousLesTests() {
        console.log('🧪 Début des tests...');
        
        try {
            await this.testInitialisation();
            await this.testFonctionnalitePrincipale();
            await this.testGestionErreurs();
            
            console.log('✅ Tous les tests sont passés !');
        } catch (error) {
            console.error('❌ Échec des tests:', error.message);
            process.exit(1);
        }
    }
    
    static async testInitialisation() {
        console.log('  �� Test initialisation...');
        const programme = new ${nomClasse}();
        
        if (!programme.version) throw new Error('Version non définie');
        if (!programme.description) throw new Error('Description non définie');
        
        console.log('  ✅ Initialisation OK');
    }
    
    static async testFonctionnalitePrincipale() {
        console.log('  🔍 Test fonctionnalité principale...');

        // Test de génération de programme
        const testSpec = {
            nom: 'test-programme',
            description: 'Programme de test',
            fonctionnalites: ['calcul', 'affichage']
        };

        const generateur = new GenerateurProgrammesAutonome();
        const resultat = await generateur.genererProgramme(testSpec);

        if (resultat && resultat.fichiers && resultat.fichiers.length > 0) {
            console.log('  ✅ Fonctionnalité principale OK');
        } else {
            throw new Error('Test fonctionnalité principale échoué');
        }
    }

    static async testGestionErreurs() {
        console.log('  🔍 Test gestion erreurs...');

        // Test avec spécification invalide
        try {
            const generateur = new GenerateurProgrammesAutonome();
            await generateur.genererProgramme(null);
            throw new Error('Devrait lever une erreur');
        } catch (error) {
            if (error.message.includes('Spécification invalide')) {
                console.log('  ✅ Gestion erreurs OK');
            } else {
                throw error;
            }
        }
    }
}

// Exécuter les tests si appelé directement
if (require.main === module) {
    Tests.executerTousLesTests();
}

module.exports = Tests;
`;
    }

    genererPackageJson(specs) {
        const dependencies = {};
        
        if (specs.technologies && specs.technologies.includes('axios')) dependencies.axios = '^1.6.0';
        if (specs.technologies && specs.technologies.includes('Sharp')) dependencies.sharp = '^0.33.0';
        
        return JSON.stringify({
            name: specs.nom_suggere.replace(/_/g, '-'),
            version: '1.0.0',
            description: specs.description,
            main: `${specs.nom_suggere}.js`,
            scripts: {
                start: `node ${specs.nom_suggere}.js`,
                test: `node ${specs.nom_suggere}_tests.js`
            },
            dependencies: dependencies,
            author: 'LOUNA-AI',
            license: 'MIT',
            created: new Date().toISOString()
        }, null, 2);
    }

    // CRÉER LES FICHIERS
    async creerFichiers(code, specs) {
        const fichiers = [];
        
        try {
            // Fichier principal
            const cheminPrincipal = path.join(this.dossierSortie, `${specs.nom_suggere}.js`);
            fs.writeFileSync(cheminPrincipal, code.principal);
            fichiers.push({ nom: `${specs.nom_suggere}.js`, chemin: cheminPrincipal, type: 'principal' });
            
            // Fichier utilitaires
            const cheminUtilitaires = path.join(this.dossierSortie, `${specs.nom_suggere}_utilitaires.js`);
            fs.writeFileSync(cheminUtilitaires, code.utilitaires);
            fichiers.push({ nom: `${specs.nom_suggere}_utilitaires.js`, chemin: cheminUtilitaires, type: 'utilitaires' });
            
            // Fichier tests
            const cheminTests = path.join(this.dossierSortie, `${specs.nom_suggere}_tests.js`);
            fs.writeFileSync(cheminTests, code.tests);
            fichiers.push({ nom: `${specs.nom_suggere}_tests.js`, chemin: cheminTests, type: 'tests' });
            
            // Package.json
            const cheminPackage = path.join(this.dossierSortie, `${specs.nom_suggere}_package.json`);
            fs.writeFileSync(cheminPackage, code.package);
            fichiers.push({ nom: `${specs.nom_suggere}_package.json`, chemin: cheminPackage, type: 'configuration' });
            
            console.log(`📁 ${fichiers.length} fichiers créés`);
            return fichiers;
            
        } catch (error) {
            throw new Error(`Erreur création fichiers: ${error.message}`);
        }
    }

    // CRÉER LA DOCUMENTATION
    creerDocumentation(specs, fichiers) {
        const doc = `# ${specs.nom_suggere}

## Description
${specs.description}

## Informations
- **Version**: 1.0.0
- **Créé par**: LOUNA-AI
- **Date**: ${new Date().toLocaleDateString()}
- **Complexité**: ${specs.complexite}
- **Technologies**: ${specs.technologies.join(', ')}

## Installation
\`\`\`bash
cd programmes-crees
npm install
\`\`\`

## Utilisation
\`\`\`bash
node ${specs.nom_suggere}.js
\`\`\`

## Tests
\`\`\`bash
npm test
\`\`\`

## Fichiers
${fichiers.map(f => `- **${f.nom}**: ${f.type}`).join('\n')}

## Support
Programme créé automatiquement par LOUNA-AI.
Pour des modifications, consultez le code source.
`;

        // Sauvegarder la documentation
        const cheminDoc = path.join(this.dossierSortie, `${specs.nom_suggere}_README.md`);
        fs.writeFileSync(cheminDoc, doc);
        
        return {
            contenu: doc,
            chemin: cheminDoc
        };
    }

    // INITIALISER LES TEMPLATES
    initialiserTemplates() {
        // Template calculatrice
        this.templatesCode.set('calculatrice', {
            header: `/**
 * {{NOM_PROGRAMME}}
 * {{DESCRIPTION}}
 * Créé automatiquement par LOUNA-AI le {{DATE}}
 */
`,
            methodes: `    calculer(expression) {
        try {
            // Validation sécurisée de l'expression
            if (!/^[0-9+\\-*/.() ]+$/.test(expression)) {
                throw new Error('Expression invalide');
            }
            
            const resultat = eval(expression);
            console.log(\`📊 \${expression} = \${resultat}\`);
            return resultat;
        } catch (error) {
            console.error('❌ Erreur calcul:', error.message);
            return null;
        }
    }
    
    calculerAvance(operation, ...valeurs) {
        switch(operation) {
            case 'moyenne':
                return valeurs.reduce((a, b) => a + b, 0) / valeurs.length;
            case 'somme':
                return valeurs.reduce((a, b) => a + b, 0);
            case 'max':
                return Math.max(...valeurs);
            case 'min':
                return Math.min(...valeurs);
            default:
                throw new Error('Opération non supportée');
        }
    }`,
            logique_principale: `        // Exemple d'utilisation
        const resultat1 = await programme.calculer('2 + 3 * 4');
        const resultat2 = await programme.calculerAvance('moyenne', 10, 20, 30);
        
        console.log('Résultats:', { resultat1, resultat2 });`
        });

        // Template gestionnaire de fichiers
        this.templatesCode.set('gestionnaire_fichiers', {
            header: `/**
 * {{NOM_PROGRAMME}}
 * {{DESCRIPTION}}
 * Créé automatiquement par LOUNA-AI le {{DATE}}
 */
`,
            methodes: `    async organiserFichiers(dossierSource, critere = 'extension') {
        try {
            const fichiers = fs.readdirSync(dossierSource);
            const organisation = {};
            
            for (const fichier of fichiers) {
                const cheminComplet = path.join(dossierSource, fichier);
                const stats = fs.statSync(cheminComplet);
                
                if (stats.isFile()) {
                    let cle;
                    if (critere === 'extension') {
                        cle = path.extname(fichier) || 'sans_extension';
                    } else if (critere === 'taille') {
                        cle = stats.size > 1024*1024 ? 'gros' : 'petit';
                    }
                    
                    if (!organisation[cle]) organisation[cle] = [];
                    organisation[cle].push(fichier);
                }
            }
            
            console.log('📁 Organisation:', organisation);
            return organisation;
        } catch (error) {
            console.error('❌ Erreur organisation:', error.message);
            return null;
        }
    }
    
    async renommerFichiers(dossier, pattern, remplacement) {
        try {
            const fichiers = fs.readdirSync(dossier);
            let compteur = 0;
            
            for (const fichier of fichiers) {
                if (fichier.includes(pattern)) {
                    const nouveauNom = fichier.replace(pattern, remplacement);
                    const ancienChemin = path.join(dossier, fichier);
                    const nouveauChemin = path.join(dossier, nouveauNom);
                    
                    fs.renameSync(ancienChemin, nouveauChemin);
                    compteur++;
                }
            }
            
            console.log(\`✅ \${compteur} fichiers renommés\`);
            return compteur;
        } catch (error) {
            console.error('❌ Erreur renommage:', error.message);
            return 0;
        }
    }`,
            logique_principale: `        // Exemple d'utilisation
        const organisation = await programme.organiserFichiers('./test');
        const renommes = await programme.renommerFichiers('./test', 'old', 'new');
        
        console.log('Résultats:', { organisation, renommes });`
        });

        // Template générique
        this.templatesCode.set('generique', {
            header: `/**
 * {{NOM_PROGRAMME}}
 * {{DESCRIPTION}}
 * Créé automatiquement par LOUNA-AI le {{DATE}}
 */
`,
            methodes: `    async traiterDonnees(donnees) {
        try {
            console.log('🔄 Traitement des données...');
            
            // Logique de traitement générique
            const resultat = Array.isArray(donnees) 
                ? donnees.map(item => this.traiterItem(item))
                : this.traiterItem(donnees);
            
            console.log('✅ Traitement terminé');
            return resultat;
        } catch (error) {
            console.error('❌ Erreur traitement:', error.message);
            return null;
        }
    }
    
    traiterItem(item) {
        // Traitement d'un élément individuel
        return {
            original: item,
            traite: true,
            timestamp: new Date().toISOString()
        };
    }`,
            logique_principale: `        // Exemple d'utilisation
        const donnees = ['item1', 'item2', 'item3'];
        const resultat = await programme.traiterDonnees(donnees);
        
        console.log('Résultat:', resultat);`
        });

        console.log(`📋 ${this.templatesCode.size} templates initialisés`);
    }

    // STATISTIQUES
    obtenirStatistiques() {
        return {
            programmes_crees: this.programmesCrees.size,
            templates_disponibles: this.templatesCode.size,
            dossier_sortie: this.dossierSortie
        };
    }
}

module.exports = GenerateurProgrammesAutonome;
