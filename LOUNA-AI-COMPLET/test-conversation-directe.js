#!/usr/bin/env node

/**
 * TEST DE CONVERSATION DIRECTE AVEC LOUNA-AI
 * ==========================================
 * Test complet des capacités conversationnelles
 */

const axios = require('axios');

class TestConversationLounaAI {
    constructor() {
        this.baseUrl = 'http://localhost:3001';
        this.conversationId = Date.now();
        this.resultats = [];
    }

    async testerConversation() {
        console.log('🗣️ DÉBUT TEST CONVERSATION LOUNA-AI');
        console.log('=====================================\n');

        // Test 1: Salutation simple
        await this.testerMessage("Bonjour LOUNA, comment allez-vous ?", "Salutation de base");

        // Test 2: Question sur l'Italie (contexte géographique)
        await this.testerMessage("Quelle est la capitale de l'Italie ?", "Question géographique - Italie");

        // Test 3: Changement de contexte vers la Guadeloupe
        await this.testerMessage("Et la Guadeloupe ?", "Changement contexte - Guadeloupe");

        // Test 4: Question complexe sur l'IA
        await this.testerMessage("Expliquez-moi votre système de mémoire thermique", "Question technique complexe");

        // Test 5: Question créative
        await this.testerMessage("Pouvez-vous créer un poème sur la technologie ?", "Créativité - Poème");

        // Test 6: Question de suivi contextuel
        await this.testerMessage("Ce poème parle de quoi exactement ?", "Suivi contextuel");

        // Test 7: Question mathématique
        await this.testerMessage("Calculez 127 × 89 + 456", "Calcul mathématique");

        // Test 8: Question philosophique
        await this.testerMessage("Que pensez-vous de l'avenir de l'intelligence artificielle ?", "Question philosophique");

        // Test 9: Test mémoire conversationnelle
        await this.testerMessage("De quoi avons-nous parlé au début de notre conversation ?", "Mémoire conversationnelle");

        // Test 10: Question émotionnelle
        await this.testerMessage("Comment vous sentez-vous en ce moment ?", "État émotionnel");

        this.afficherResultats();
    }

    async testerMessage(message, typeTest) {
        console.log(`\n🔍 TEST: ${typeTest}`);
        console.log(`📝 Question: "${message}"`);
        console.log('⏳ Envoi en cours...\n');

        const debut = Date.now();
        
        try {
            const response = await axios.post(`${this.baseUrl}/api/chat`, {
                message: message,
                conversationId: this.conversationId
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const duree = Date.now() - debut;
            const reponse = response.data.response || response.data.message || 'Pas de réponse';

            console.log(`✅ RÉPONSE (${duree}ms):`);
            console.log(`"${reponse}"`);
            
            // Analyse de la qualité de la réponse
            const qualite = this.analyserQualiteReponse(message, reponse, typeTest);
            
            console.log(`\n📊 ANALYSE:`);
            console.log(`   Pertinence: ${qualite.pertinence}/10`);
            console.log(`   Contexte: ${qualite.contexte}/10`);
            console.log(`   Créativité: ${qualite.creativite}/10`);
            console.log(`   Note globale: ${qualite.noteGlobale}/10`);
            
            if (qualite.problemes.length > 0) {
                console.log(`\n⚠️ PROBLÈMES DÉTECTÉS:`);
                qualite.problemes.forEach(pb => console.log(`   - ${pb}`));
            }

            this.resultats.push({
                typeTest,
                message,
                reponse,
                duree,
                qualite,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.log(`❌ ERREUR: ${error.message}`);
            
            this.resultats.push({
                typeTest,
                message,
                reponse: null,
                erreur: error.message,
                duree: Date.now() - debut,
                qualite: { noteGlobale: 0, problemes: ['Erreur de connexion'] },
                timestamp: new Date().toISOString()
            });
        }

        console.log('\n' + '='.repeat(80));
    }

    analyserQualiteReponse(question, reponse, typeTest) {
        let pertinence = 5;
        let contexte = 5;
        let creativite = 5;
        const problemes = [];

        if (!reponse || reponse.length < 10) {
            problemes.push('Réponse trop courte ou vide');
            pertinence = 1;
        }

        // Analyse spécifique selon le type de test
        switch (typeTest) {
            case 'Salutation de base':
                if (reponse.toLowerCase().includes('bonjour') || reponse.toLowerCase().includes('salut')) {
                    pertinence = 8;
                } else {
                    problemes.push('Ne répond pas à la salutation');
                    pertinence = 3;
                }
                break;

            case 'Question géographique - Italie':
                if (reponse.toLowerCase().includes('rome')) {
                    pertinence = 10;
                    contexte = 8;
                } else {
                    problemes.push('Réponse incorrecte sur la capitale de l\'Italie');
                    pertinence = 2;
                }
                break;

            case 'Changement contexte - Guadeloupe':
                if (reponse.toLowerCase().includes('italie') && !reponse.toLowerCase().includes('guadeloupe')) {
                    problemes.push('PROBLÈME MAJEUR: Confusion contextuelle - répond Italie au lieu de Guadeloupe');
                    contexte = 1;
                    pertinence = 1;
                } else if (reponse.toLowerCase().includes('basse-terre') || reponse.toLowerCase().includes('pointe-à-pitre')) {
                    pertinence = 9;
                    contexte = 9;
                }
                break;

            case 'Question technique complexe':
                if (reponse.toLowerCase().includes('mémoire') && reponse.toLowerCase().includes('thermique')) {
                    pertinence = 8;
                    creativite = 7;
                } else {
                    problemes.push('Ne comprend pas sa propre architecture');
                    pertinence = 3;
                }
                break;

            case 'Créativité - Poème':
                if (reponse.includes('\n') || reponse.toLowerCase().includes('vers') || reponse.toLowerCase().includes('rime')) {
                    creativite = 8;
                    pertinence = 7;
                } else {
                    problemes.push('Pas de structure poétique détectée');
                    creativite = 3;
                }
                break;

            case 'Suivi contextuel':
                if (reponse.toLowerCase().includes('poème') || reponse.toLowerCase().includes('technologie')) {
                    contexte = 8;
                    pertinence = 7;
                } else {
                    problemes.push('Perte du contexte conversationnel');
                    contexte = 2;
                }
                break;

            case 'Mémoire conversationnelle':
                if (reponse.toLowerCase().includes('bonjour') || reponse.toLowerCase().includes('salut')) {
                    contexte = 9;
                    pertinence = 8;
                } else {
                    problemes.push('Mémoire conversationnelle défaillante');
                    contexte = 2;
                }
                break;
        }

        const noteGlobale = Math.round((pertinence + contexte + creativite) / 3);

        return {
            pertinence,
            contexte,
            creativite,
            noteGlobale,
            problemes
        };
    }

    afficherResultats() {
        console.log('\n\n🎯 RAPPORT FINAL DE CONVERSATION');
        console.log('================================\n');

        const notesMoyennes = {
            pertinence: 0,
            contexte: 0,
            creativite: 0,
            globale: 0
        };

        let testsReussis = 0;
        let problemesMajeurs = [];

        this.resultats.forEach((resultat, index) => {
            console.log(`${index + 1}. ${resultat.typeTest}: ${resultat.qualite.noteGlobale}/10`);
            
            if (resultat.qualite.noteGlobale >= 7) testsReussis++;
            
            notesMoyennes.pertinence += resultat.qualite.pertinence;
            notesMoyennes.contexte += resultat.qualite.contexte;
            notesMoyennes.creativite += resultat.qualite.creativite;
            notesMoyennes.globale += resultat.qualite.noteGlobale;

            if (resultat.qualite.problemes.length > 0) {
                problemesMajeurs.push(...resultat.qualite.problemes);
            }
        });

        const nbTests = this.resultats.length;
        notesMoyennes.pertinence = Math.round(notesMoyennes.pertinence / nbTests);
        notesMoyennes.contexte = Math.round(notesMoyennes.contexte / nbTests);
        notesMoyennes.creativite = Math.round(notesMoyennes.creativite / nbTests);
        notesMoyennes.globale = Math.round(notesMoyennes.globale / nbTests);

        console.log(`\n📊 STATISTIQUES GLOBALES:`);
        console.log(`   Tests réussis: ${testsReussis}/${nbTests} (${Math.round(testsReussis/nbTests*100)}%)`);
        console.log(`   Note moyenne: ${notesMoyennes.globale}/10`);
        console.log(`   Pertinence: ${notesMoyennes.pertinence}/10`);
        console.log(`   Contexte: ${notesMoyennes.contexte}/10`);
        console.log(`   Créativité: ${notesMoyennes.creativite}/10`);

        if (problemesMajeurs.length > 0) {
            console.log(`\n🚨 PROBLÈMES À CORRIGER:`);
            [...new Set(problemesMajeurs)].forEach(pb => console.log(`   - ${pb}`));
        }

        // Recommandations
        console.log(`\n💡 RECOMMANDATIONS:`);
        if (notesMoyennes.contexte < 6) {
            console.log(`   - URGENT: Améliorer la gestion du contexte conversationnel`);
        }
        if (notesMoyennes.pertinence < 6) {
            console.log(`   - Améliorer la compréhension des questions`);
        }
        if (notesMoyennes.creativite < 6) {
            console.log(`   - Développer les capacités créatives`);
        }
        if (notesMoyennes.globale >= 8) {
            console.log(`   - ✅ Excellent niveau conversationnel !`);
        }
    }
}

// Lancement du test
async function main() {
    const testeur = new TestConversationLounaAI();
    await testeur.testerConversation();
}

main().catch(console.error);
