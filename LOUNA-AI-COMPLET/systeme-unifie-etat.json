{"version": "3.0.0", "timestamp": "2025-06-05T03:02:29.477Z", "etat_fluide": {"temperature_globale": 45, "fluidite_active": true, "vitesse_deplacement": 0.001, "coherence_systeme": 0.47124964474900666, "synchronisation": true}, "metriques": {"operations_totales": 0, "reponses_agent": 0, "accelerations_appliquees": 0, "deplacements_fluides": 7240, "coherence_moyenne": 0.47124964474900666, "performance_globale": 0}, "agent_19gb": {"url": "http://localhost:11434", "modele_principal": "codellama:34b-instruct", "modele_rapide": "mistral:7b", "actif": true, "derniere_reponse": null, "verrouille": true, "tentatives_reconnexion": 0, "max_tentatives": 10, "delai_reconnexion": 5000, "keep_alive": true, "timeout_requete": 30000, "derniere_verification": 1749092489762}, "config_auto_accelerateurs": {"installation_automatique": true, "detection_besoins": true, "adaptation_dynamique": true, "persistance_infinie": true, "seuil_performance": 70, "types_prioritaires": ["KYBER_NEURAL", "KYBER_QUANTUM"]}, "performance_globale": 66.42499289498014}