/**
 * SERVEUR DE TEST SIMPLE POUR VÉRIFIER LES INTERFACES
 * Test rapide sans dépendances complexes
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

class TesteurInterfacesSimple {
    constructor() {
        this.port = 3000;
        this.interfaces = [
            { nom: 'Interface Principale', fichier: 'interface-louna-complete.html', route: '/' },
            { nom: 'Cerveau 3D Vivant', fichier: 'interface-3d-cerveau-vivant.html', route: '/3d' },
            { nom: 'Pensées & Émotions', fichier: 'interface-cerveau-pensees-emotions.html', route: '/cerveau' },
            { nom: 'Test QI Avancé', fichier: 'interface-test-qi-avance.html', route: '/test-qi' },
            { nom: 'Test Live Ultime', fichier: 'test-live-ultra-complexe.html', route: '/test-live' },
            { nom: 'Formations', fichier: 'interface-formations.html', route: '/formations' },
            { nom: 'Cours Langage Naturel', fichier: 'interface-apprentissage-langage-naturel.html', route: '/langage' }
        ];
        this.resultats = {
            interfaces_trouvees: 0,
            interfaces_manquantes: 0,
            erreurs: [],
            details: []
        };
    }

    async demarrer() {
        console.log('🧪 TESTEUR D\'INTERFACES SIMPLE - REEL LOUNA AI V5');
        console.log('==================================================');
        
        // Vérifier l'existence des fichiers
        await this.verifierFichiers();
        
        // Créer le serveur
        this.creerServeur();
        
        // Démarrer le serveur
        this.server = http.createServer(this.gererRequete.bind(this));
        
        this.server.listen(this.port, () => {
            console.log(`\n✅ Serveur de test démarré sur http://localhost:${this.port}`);
            this.afficherResultats();
            this.lancerTestsAutomatiques();
        });
    }

    async verifierFichiers() {
        console.log('\n📁 VÉRIFICATION DES FICHIERS INTERFACES');
        console.log('======================================');
        
        for (const interface of this.interfaces) {
            const cheminFichier = path.join(__dirname, interface.fichier);
            
            try {
                if (fs.existsSync(cheminFichier)) {
                    const stats = fs.statSync(cheminFichier);
                    const taille = (stats.size / 1024).toFixed(1);
                    
                    console.log(`✅ ${interface.nom}: ${interface.fichier} (${taille} KB)`);
                    this.resultats.interfaces_trouvees++;
                    this.resultats.details.push({
                        nom: interface.nom,
                        fichier: interface.fichier,
                        route: interface.route,
                        taille: taille,
                        statut: 'OK'
                    });
                } else {
                    console.log(`❌ ${interface.nom}: ${interface.fichier} MANQUANT`);
                    this.resultats.interfaces_manquantes++;
                    this.resultats.erreurs.push(`Fichier manquant: ${interface.fichier}`);
                    this.resultats.details.push({
                        nom: interface.nom,
                        fichier: interface.fichier,
                        route: interface.route,
                        statut: 'MANQUANT'
                    });
                }
            } catch (error) {
                console.log(`❌ ${interface.nom}: ERREUR - ${error.message}`);
                this.resultats.erreurs.push(`Erreur ${interface.fichier}: ${error.message}`);
            }
        }
    }

    gererRequete(req, res) {
        const parsedUrl = url.parse(req.url, true);
        const pathname = parsedUrl.pathname;
        
        // Trouver l'interface correspondante
        let interface = this.interfaces.find(i => i.route === pathname);
        
        // Route par défaut
        if (pathname === '/' || !interface) {
            interface = this.interfaces[0]; // Interface principale
        }
        
        // API de test
        if (pathname === '/api/test') {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                message: 'Serveur de test fonctionnel',
                interfaces: this.resultats.details,
                timestamp: new Date().toISOString()
            }));
            return;
        }
        
        // Servir le fichier HTML
        const cheminFichier = path.join(__dirname, interface.fichier);
        
        if (fs.existsSync(cheminFichier)) {
            fs.readFile(cheminFichier, 'utf8', (err, data) => {
                if (err) {
                    res.writeHead(500, { 'Content-Type': 'text/html' });
                    res.end(`<h1>Erreur 500</h1><p>Erreur lecture fichier: ${err.message}</p>`);
                } else {
                    res.writeHead(200, { 'Content-Type': 'text/html' });
                    res.end(data);
                }
            });
        } else {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <h1>Interface Non Trouvée</h1>
                <p>L'interface <strong>${interface.nom}</strong> (${interface.fichier}) n'existe pas.</p>
                <p><a href="/">Retour à l'accueil</a></p>
            `);
        }
    }

    afficherResultats() {
        console.log('\n📊 RÉSULTATS VÉRIFICATION INTERFACES');
        console.log('====================================');
        console.log(`✅ Interfaces trouvées: ${this.resultats.interfaces_trouvees}/${this.interfaces.length}`);
        console.log(`❌ Interfaces manquantes: ${this.resultats.interfaces_manquantes}/${this.interfaces.length}`);
        
        if (this.resultats.erreurs.length > 0) {
            console.log('\n🚨 ERREURS DÉTECTÉES:');
            this.resultats.erreurs.forEach(erreur => console.log(`   - ${erreur}`));
        }
        
        console.log('\n🌐 URLS DE TEST:');
        this.interfaces.forEach(interface => {
            const statut = this.resultats.details.find(d => d.route === interface.route)?.statut || 'INCONNU';
            const emoji = statut === 'OK' ? '✅' : '❌';
            console.log(`   ${emoji} ${interface.nom}: http://localhost:${this.port}${interface.route}`);
        });
        
        const pourcentage = (this.resultats.interfaces_trouvees / this.interfaces.length * 100).toFixed(1);
        console.log(`\n🎯 TAUX DE RÉUSSITE: ${pourcentage}%`);
    }

    async lancerTestsAutomatiques() {
        console.log('\n🤖 LANCEMENT DES TESTS AUTOMATIQUES...');
        
        // Attendre 2 secondes puis tester les URLs
        setTimeout(() => {
            this.testerURLs();
        }, 2000);
    }

    async testerURLs() {
        console.log('\n🔍 TEST DES URLS...');
        
        for (const interface of this.interfaces) {
            const detail = this.resultats.details.find(d => d.route === interface.route);
            if (detail && detail.statut === 'OK') {
                console.log(`🌐 Test ${interface.nom}: http://localhost:${this.port}${interface.route}`);
            }
        }
        
        console.log('\n✅ Tests terminés ! Ouvrez les URLs dans votre navigateur pour vérifier.');
    }

    creerServeur() {
        // Configuration basique du serveur
        console.log('🔧 Configuration du serveur de test...');
    }
}

// Lancement si exécuté directement
if (require.main === module) {
    const testeur = new TesteurInterfacesSimple();
    testeur.demarrer().catch(error => {
        console.error('💥 Erreur fatale:', error);
        process.exit(1);
    });
}

module.exports = TesteurInterfacesSimple;
