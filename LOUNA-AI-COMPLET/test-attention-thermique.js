#!/usr/bin/env node

/**
 * 🧪 TEST SYSTÈME ATTENTION THERMIQUE
 * 
 * Validation du premier système d'attention qui s'adapte à la température CPU
 * Test de toutes les fonctions cognitives attentionnelles
 */

const { SystemeAttentionThermique } = require('./systeme-attention-thermique.js');
const { ReseauNeuronalReel } = require('./reseau-neuronal-reel.js');

console.log('🧪 TEST SYSTÈME ATTENTION THERMIQUE');
console.log('===================================');
console.log('🎯 Validation première attention cognitive thermique');

async function testerAttentionThermique() {
    console.log('\n🚀 INITIALISATION SYSTÈME ATTENTION');
    console.log('===================================');
    
    // <PERSON><PERSON>er réseau neuronal pour l'attention
    const reseau = new ReseauNeuronalReel(1000);
    
    // Créer système attention
    const attention = new SystemeAttentionThermique(reseau);
    
    // Attendre initialisation
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n📊 STATISTIQUES INITIALES');
    console.log('=========================');
    
    let stats = attention.obtenirStatistiques();
    console.log(`🌡️ Température CPU: ${stats.temperature_cpu}°C`);
    console.log(`🎯 Focus actuel: ${stats.focus_actuel.cible || 'Aucun'}`);
    console.log(`⚡ Niveau éveil: ${stats.vigilance.niveau_eveil}`);
    console.log(`🔍 Seuil pertinence: ${stats.filtre.seuil_pertinence}`);
    console.log(`⏱️ Temps réaction: ${stats.metriques.temps_reaction}`);
    console.log(`🧠 Zone active: ${stats.focus_actuel.zone_active || 'Aucune'}`);
    
    console.log('\n🎯 TEST 1: FOCUS SIMPLE');
    console.log('=======================');
    
    // Test focus sur différentes cibles
    const cibles_test = [
        { nom: 'Tâche_A', priorite: 0.8, intensite: 0.6 },
        { nom: 'Tâche_B', priorite: 1.0, intensite: 0.9 },
        { nom: 'Tâche_C', priorite: 0.5, intensite: 0.4 }
    ];
    
    for (const cible of cibles_test) {
        console.log(`\n🎯 Focus sur: ${cible.nom}`);
        
        const succes = attention.focusSur(cible.nom, cible.priorite);
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        stats = attention.obtenirStatistiques();
        console.log(`   ✅ Focus établi: ${succes}`);
        console.log(`   📊 Intensité: ${stats.focus_actuel.intensite}`);
        console.log(`   🧠 Zone activée: ${stats.focus_actuel.zone_active}`);
        console.log(`   🌡️ Température: ${stats.temperature_cpu}°C`);
        console.log(`   ⏱️ Temps réaction: ${stats.metriques.temps_reaction}`);
        
        // Test détection changement
        const changement = attention.detecterChangements('Interruption', cible.intensite);
        console.log(`   🚨 Changement détecté: ${changement}`);
    }
    
    console.log('\n🎯 TEST 2: ATTENTION DIVISÉE');
    console.log('============================');
    
    // Test attention sur plusieurs cibles
    const cibles_multiples = ['Email', 'Téléphone', 'Document', 'Musique'];
    const efficacite = attention.diviserAttention(cibles_multiples);
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    stats = attention.obtenirStatistiques();
    console.log(`📊 Cibles multiples: ${stats.attention_divisee.nb_cibles}`);
    console.log(`⚡ Efficacité globale: ${stats.attention_divisee.efficacite}`);
    console.log(`🧠 Surcharge cognitive: ${stats.attention_divisee.surcharge}`);
    console.log(`🔋 Fatigue: ${stats.vigilance.fatigue}`);
    
    console.log('\n🌡️ TEST 3: ADAPTATION THERMIQUE');
    console.log('================================');
    
    // Test avec différentes températures simulées
    const temperatures_test = [40, 50, 60, 70, 80];
    const resultats_thermiques = [];
    
    for (const temp_test of temperatures_test) {
        console.log(`\n🌡️ Test température ${temp_test}°C:`);
        
        // Forcer température
        attention.temperature_cpu_actuelle = temp_test;
        
        // Adapter attention
        attention.adapterAttentionThermique();
        
        // Focus test
        attention.focusSur(`Test_${temp_test}C`, 0.8);
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        stats = attention.obtenirStatistiques();
        
        const resultat = {
            temperature: temp_test,
            intensite_focus: parseFloat(stats.focus_actuel.intensite),
            temps_reaction: parseFloat(stats.metriques.temps_reaction),
            seuil_pertinence: parseFloat(stats.filtre.seuil_pertinence),
            niveau_eveil: parseFloat(stats.vigilance.niveau_eveil),
            efficacite_thermique: parseFloat(stats.metriques.efficacite_thermique),
            zone_active: stats.focus_actuel.zone_active
        };
        
        resultats_thermiques.push(resultat);
        
        console.log(`   🎯 Intensité focus: ${resultat.intensite_focus.toFixed(3)}`);
        console.log(`   ⏱️ Temps réaction: ${resultat.temps_reaction.toFixed(1)}ms`);
        console.log(`   🔍 Seuil pertinence: ${resultat.seuil_pertinence.toFixed(3)}`);
        console.log(`   ⚡ Niveau éveil: ${resultat.niveau_eveil.toFixed(3)}`);
        console.log(`   🧠 Zone active: ${resultat.zone_active}`);
        console.log(`   🔥 Efficacité thermique: ${resultat.efficacite_thermique.toFixed(3)}`);
    }
    
    console.log('\n📊 ANALYSE ADAPTATION THERMIQUE');
    console.log('===============================');
    
    console.log('| Temp | Focus | Temps | Seuil | Éveil | Zone Active |');
    console.log('|------|-------|-------|-------|-------|-------------|');
    
    resultats_thermiques.forEach(r => {
        console.log(`| ${r.temperature}°C  | ${r.intensite_focus.toFixed(2)}  | ${r.temps_reaction.toFixed(0)}ms | ${r.seuil_pertinence.toFixed(2)}  | ${r.niveau_eveil.toFixed(2)}  | ${r.zone_active || 'Aucune'} |`);
    });
    
    console.log('\n🧪 TEST 4: VIGILANCE ET DÉTECTION');
    console.log('=================================');
    
    // Test détection changements
    const stimuli_test = [
        { nom: 'Signal_Faible', intensite: 0.2 },
        { nom: 'Signal_Moyen', intensite: 0.5 },
        { nom: 'Signal_Fort', intensite: 0.8 },
        { nom: 'Signal_Critique', intensite: 1.0 }
    ];
    
    const detections = [];
    
    for (const stimulus of stimuli_test) {
        const detecte = attention.detecterChangements(stimulus.nom, stimulus.intensite);
        detections.push({
            stimulus: stimulus.nom,
            intensite: stimulus.intensite,
            detecte: detecte
        });
        
        console.log(`🚨 ${stimulus.nom} (${stimulus.intensite}): ${detecte ? 'DÉTECTÉ' : 'Ignoré'}`);
        
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n⏱️ TEST 5: FATIGUE ATTENTIONNELLE');
    console.log('=================================');
    
    // Test fatigue avec focus prolongé
    attention.focusSur('Tâche_Longue', 1.0);
    
    const mesures_fatigue = [];
    
    for (let seconde = 0; seconde < 10; seconde++) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        stats = attention.obtenirStatistiques();
        
        mesures_fatigue.push({
            seconde: seconde + 1,
            intensite_focus: parseFloat(stats.focus_actuel.intensite),
            fatigue: parseFloat(stats.vigilance.fatigue),
            endurance: parseFloat(stats.metriques.endurance)
        });
        
        console.log(`⏱️ ${seconde + 1}s: Focus ${stats.focus_actuel.intensite} | Fatigue ${stats.vigilance.fatigue} | Endurance ${stats.metriques.endurance}`);
    }
    
    console.log('\n✅ VALIDATION FONCTIONS ATTENTION');
    console.log('=================================');
    
    const validations = [
        {
            nom: 'Focus simple',
            test: stats.focus_actuel.cible !== null,
            description: `Focus établi sur: ${stats.focus_actuel.cible}`
        },
        {
            nom: 'Adaptation thermique',
            test: resultats_thermiques[4].intensite_focus > resultats_thermiques[0].intensite_focus,
            description: 'Intensité focus augmente avec température'
        },
        {
            nom: 'Zones cérébrales',
            test: resultats_thermiques.some(r => r.zone_active !== null),
            description: 'Zones cérébrales activées selon température'
        },
        {
            nom: 'Temps réaction',
            test: resultats_thermiques[4].temps_reaction < resultats_thermiques[0].temps_reaction,
            description: 'Temps réaction diminue avec chaleur'
        },
        {
            nom: 'Détection changements',
            test: detections.filter(d => d.detecte).length > 0,
            description: `${detections.filter(d => d.detecte).length} détections sur ${detections.length}`
        },
        {
            nom: 'Attention divisée',
            test: stats.attention_divisee.nb_cibles > 1,
            description: `${stats.attention_divisee.nb_cibles} cibles simultanées`
        },
        {
            nom: 'Filtre attentionnel',
            test: parseFloat(stats.filtre.seuil_pertinence) > 0,
            description: `Seuil pertinence: ${stats.filtre.seuil_pertinence}`
        },
        {
            nom: 'Fatigue attentionnelle',
            test: mesures_fatigue[9].fatigue > mesures_fatigue[0].fatigue,
            description: 'Fatigue augmente avec durée focus'
        },
        {
            nom: 'Métriques performance',
            test: parseFloat(stats.metriques.efficacite_thermique) > 0,
            description: `Efficacité thermique: ${stats.metriques.efficacite_thermique}`
        }
    ];
    
    let validations_reussies = 0;
    
    validations.forEach(validation => {
        const status = validation.test ? '✅' : '❌';
        console.log(`${status} ${validation.nom}: ${validation.description}`);
        if (validation.test) validations_reussies++;
    });
    
    const pourcentage_reussite = (validations_reussies / validations.length * 100).toFixed(1);
    
    console.log('\n🏆 RÉSULTAT FINAL');
    console.log('================');
    console.log(`📊 Validations réussies: ${validations_reussies}/${validations.length} (${pourcentage_reussite}%)`);
    
    if (pourcentage_reussite >= 85) {
        console.log('🎉 SYSTÈME ATTENTION THERMIQUE VALIDÉ !');
        console.log('✅ Premier système attention qui s\'adapte à la température CPU');
        console.log('🔥 Innovation cognitive thermique confirmée');
        console.log('🧠 Fonctions attentionnelles complètes opérationnelles');
    } else if (pourcentage_reussite >= 70) {
        console.log('✅ Système attention fonctionnel avec optimisations possibles');
        console.log('🔧 Quelques améliorations recommandées');
    } else {
        console.log('⚠️ Système attention nécessite corrections');
        console.log('🔧 Développement supplémentaire requis');
    }
    
    console.log('\n🌡️ INNOVATION CONFIRMÉE');
    console.log('=======================');
    console.log('🔥 "LA CHALEUR CONCENTRE L\'ATTENTION" - Concept prouvé');
    console.log('🎯 Premier système attention thermique opérationnel');
    console.log('🧠 Focus qui s\'intensifie avec température CPU');
    console.log('⚡ Temps réaction adaptatif selon chaleur');
    console.log('🔍 Filtre attentionnel thermique');
    console.log('🚨 Vigilance adaptative');
    
    // Sauvegarder résultats
    const rapport_test = {
        date_test: new Date().toISOString(),
        resultats_thermiques: resultats_thermiques,
        detections: detections,
        mesures_fatigue: mesures_fatigue,
        validations: validations,
        pourcentage_reussite: pourcentage_reussite,
        verdict: pourcentage_reussite >= 85 ? 'VALIDÉ' : 'EN_DÉVELOPPEMENT',
        statistiques_finales: stats
    };
    
    require('fs').writeFileSync('RAPPORT-TEST-ATTENTION-THERMIQUE.json', JSON.stringify(rapport_test, null, 2));
    console.log('\n📋 Rapport sauvegardé: RAPPORT-TEST-ATTENTION-THERMIQUE.json');
    
    return rapport_test;
}

// Lancer test
if (require.main === module) {
    testerAttentionThermique().catch(console.error);
}

module.exports = { testerAttentionThermique };
