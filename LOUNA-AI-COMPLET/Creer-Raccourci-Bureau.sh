#!/bin/bash

# 🖥️ CRÉATEUR DE RACCOURCI BUREAU LOUNA-AI

echo "🖥️ CRÉATION DU RACCOURCI BUREAU LOUNA-AI"
echo "========================================"

# Créer un alias vers l'application
DESKTOP_PATH="$HOME/Desktop"
APP_PATH="/Applications/LOUNA-AI Complete.app"

if [ -d "$APP_PATH" ]; then
    echo "📱 Application trouvée: $APP_PATH"
    
    # Créer un alias sur le bureau
    osascript << EOF
tell application "Finder"
    make alias file to POSIX file "$APP_PATH" at desktop
    set name of result to "🧠 LOUNA-AI Complete"
end tell
EOF
    
    echo "✅ Raccourci créé sur le Bureau: 🧠 LOUNA-AI Complete"
    echo ""
    echo "🎯 Vous pouvez maintenant:"
    echo "   • Double-cliquer sur l'icône du Bureau"
    echo "   • Ou lancer depuis Applications"
    echo "   • Ou utiliser Launchpad"
    
else
    echo "❌ Application non trouvée dans /Applications"
    echo "🔧 Création d'un script de lancement alternatif..."
    
    # Créer un script de lancement direct sur le bureau
    cat > "$DESKTOP_PATH/🧠 Lancer LOUNA-AI.command" << 'EOF'
#!/bin/bash
cd "/Volumes/ALDO et MIM/LOUNA-AI-COMPLET"
osascript -e 'display notification "🚀 Démarrage de LOUNA-AI..." with title "LOUNA-AI"'
node serveur-interface-complete.js &
sleep 6
open "http://localhost:3000/interface-louna-complete.html"
osascript -e 'display notification "✅ LOUNA-AI Interface Complète active !" with title "LOUNA-AI"'
EOF
    
    chmod +x "$DESKTOP_PATH/🧠 Lancer LOUNA-AI.command"
    echo "✅ Script de lancement créé sur le Bureau"
fi

echo ""
echo "🎉 RACCOURCIS LOUNA-AI CRÉÉS AVEC SUCCÈS !"
