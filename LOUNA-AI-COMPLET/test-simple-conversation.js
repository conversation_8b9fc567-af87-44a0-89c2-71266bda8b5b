#!/usr/bin/env node

/**
 * TEST SIMPLE DE CONVERSATION AVEC LOUNA-AI
 * ==========================================
 */

const axios = require('axios');

async function testerConversation() {
    console.log('🗣️ TEST SIMPLE CONVERSATION LOUNA-AI');
    console.log('====================================\n');

    const questions = [
        "Bonjour LOUNA, comment allez-vous ?",
        "Quelle est la capitale de l'Italie ?",
        "Et la Guadeloupe ?",
        "De quoi avons-nous parlé au début ?"
    ];

    for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        console.log(`\n${i + 1}. Question: "${question}"`);
        console.log('⏳ Envoi...');

        try {
            const response = await axios.post('http://localhost:3001/api/chat', {
                message: question,
                conversationId: 'test-conversation'
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const reponse = response.data.response || response.data.message || 'Pas de réponse';
            console.log(`✅ Réponse: "${reponse}"`);

            // Analyse rapide
            if (i === 1 && !reponse.toLowerCase().includes('rome')) {
                console.log('⚠️ PROBLÈME: Réponse incorrecte sur la capitale de l\'Italie');
            }
            if (i === 2 && reponse.toLowerCase().includes('italie')) {
                console.log('🚨 PROBLÈME MAJEUR: Confusion contextuelle - répond Italie au lieu de Guadeloupe');
            }

        } catch (error) {
            console.log(`❌ ERREUR: ${error.message}`);
        }

        console.log('─'.repeat(60));
    }
}

testerConversation().catch(console.error);
