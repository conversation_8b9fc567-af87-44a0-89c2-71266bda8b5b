#!/usr/bin/env node

/**
 * 🧪 TEST SYSTÈME COMPLET SÉCURISÉ
 * 
 * Test de tous les systèmes de sécurité et sauvegarde
 */

const { SystemeSauvegardeUltraSecurise } = require('./systeme-sauvegarde-ultra-securise.js');
const { ProtocoleMCPSecurise } = require('./protocole-mcp-securise.js');
const { RechercheGoogleSecurisee } = require('./recherche-google-securisee.js');

async function testerSystemeCompletSecurise() {
    console.log('🧪 TEST SYSTÈME COMPLET SÉCURISÉ');
    console.log('================================');
    
    try {
        // 1. TEST SYSTÈME SAUVEGARDE
        console.log('\n🔒 Test Système Sauvegarde Ultra-Sécurisé...');
        const sauvegarde = new SystemeSauvegardeUltraSecurise();
        
        // Attendre initialisation
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const statsSauvegarde = sauvegarde.obtenirStatistiques();
        console.log('📊 Stats Sauvegarde:', statsSauvegarde);
        
        // 2. TEST PROTOCOLE MCP
        console.log('\n🔐 Test Protocole MCP Sécurisé...');
        const mcp = new ProtocoleMCPSecurise();
        
        // Attendre initialisation
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test recherche applications
        const resultatsApps = await mcp.rechercherSecurise('calculator');
        console.log(`✅ Applications trouvées: ${resultatsApps.length}`);
        resultatsApps.forEach(app => {
            console.log(`   📱 ${app.nom} (${app.type}) - Sécurité: ${(app.securite * 100).toFixed(0)}%`);
        });
        
        const statsMCP = mcp.obtenirStatistiques();
        console.log('📊 Stats MCP:', statsMCP);
        
        // 3. TEST RECHERCHE GOOGLE SÉCURISÉE
        console.log('\n🔍 Test Recherche Google Sécurisée...');
        const recherche = new RechercheGoogleSecurisee();
        
        // Attendre initialisation
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test recherche
        const resultatsRecherche = await recherche.rechercherSecurise('intelligence artificielle', 3);
        console.log(`✅ Résultats trouvés: ${resultatsRecherche.length}`);
        resultatsRecherche.forEach(resultat => {
            console.log(`   🔍 ${resultat.titre}`);
            console.log(`      Source: ${resultat.source} - Pertinence: ${(resultat.pertinence * 100).toFixed(0)}%`);
        });
        
        const statsRecherche = recherche.obtenirStatistiques();
        console.log('📊 Stats Recherche:', statsRecherche);
        
        // 4. TEST RECHERCHE TECHNIQUE
        console.log('\n🛠️ Test Recherche Technique...');
        const resultatstech = await recherche.rechercherTechnique('javascript async await');
        console.log(`✅ Résultats techniques: ${resultatstech.length}`);
        
        // 5. TEST INTÉGRATION COMPLÈTE
        console.log('\n🔗 Test Intégration Complète...');
        
        // Simuler question nécessitant recherche
        const questionInconnue = "Qu'est-ce que le nouveau framework Svelte 5 ?";
        console.log(`❓ Question: ${questionInconnue}`);
        
        // Recherche automatique
        const resultatsAuto = await recherche.rechercherSecurise(questionInconnue, 2);
        
        if (resultatsAuto.length > 0) {
            console.log('✅ Recherche automatique réussie:');
            resultatsAuto.forEach(resultat => {
                console.log(`   📖 ${resultat.titre}`);
                console.log(`      ${resultat.description.substring(0, 100)}...`);
            });
        }
        
        // 6. TEST SÉCURITÉ ET MONITORING
        console.log('\n🛡️ Test Sécurité et Monitoring...');
        
        // Vérifier logs de sécurité
        const logsSecurite = mcp.etat.logs_acces.slice(-5);
        console.log(`📝 Derniers logs MCP: ${logsSecurite.length}`);
        logsSecurite.forEach(log => {
            console.log(`   ${new Date(log.timestamp).toLocaleTimeString()} - ${log.operation}`);
        });
        
        // 7. RÉSUMÉ FINAL
        console.log('\n📋 RÉSUMÉ FINAL');
        console.log('===============');
        console.log(`✅ Système Sauvegarde: ${statsSauvegarde.sauvegardes_reussies} sauvegardes`);
        console.log(`✅ Protocole MCP: ${statsMCP.applications_detectees} apps détectées`);
        console.log(`✅ Recherche Google: ${statsRecherche.requetes_effectuees} requêtes`);
        console.log(`✅ VPN Status: ${statsRecherche.vpn_status}`);
        console.log(`✅ Cache: ${statsRecherche.cache_entries} entrées`);
        
        // 8. TEST PERFORMANCE
        console.log('\n⚡ Test Performance...');
        const debutPerf = Date.now();
        
        await Promise.all([
            recherche.rechercherSecurise('test performance', 1),
            mcp.rechercherSecurise('test'),
            new Promise(resolve => setTimeout(resolve, 100))
        ]);
        
        const finPerf = Date.now();
        console.log(`⏱️ Temps total: ${finPerf - debutPerf}ms`);
        
        console.log('\n🎉 TOUS LES TESTS RÉUSSIS !');
        console.log('===========================');
        console.log('🔒 Système de sauvegarde ultra-sécurisé opérationnel');
        console.log('🔐 Protocole MCP sécurisé fonctionnel');
        console.log('🔍 Recherche Google sécurisée active');
        console.log('🛡️ Monitoring et logs de sécurité en place');
        console.log('⚡ Performance optimale');
        console.log('🌐 Prêt pour recherche automatique quand agent ne sait pas');
        
        return true;
        
    } catch (error) {
        console.error('❌ ERREUR DANS LES TESTS:', error);
        console.error('Stack:', error.stack);
        return false;
    }
}

// Fonction de test spécifique pour la recherche automatique
async function testerRechercheAutomatique() {
    console.log('\n🤖 TEST RECHERCHE AUTOMATIQUE');
    console.log('==============================');
    
    const recherche = new RechercheGoogleSecurisee();
    
    // Questions qui déclenchent "je ne sais pas"
    const questionsInconnues = [
        "Quelle est la capitale de Bhutan ?",
        "Comment fonctionne le nouveau processeur M4 d'Apple ?",
        "Qu'est-ce que le framework Astro 4.0 ?",
        "Quelles sont les dernières découvertes sur Mars en 2024 ?",
        "Comment installer Deno 2.0 ?"
    ];
    
    for (const question of questionsInconnues) {
        console.log(`\n❓ Question: ${question}`);
        
        // Simuler réponse "je ne sais pas" de l'agent
        const reponseAgent = "Je ne sais pas cette information spécifique.";
        console.log(`🤖 Agent: ${reponseAgent}`);
        
        // Déclencher recherche automatique
        console.log('🔍 Déclenchement recherche automatique...');
        const resultats = await recherche.rechercherSecurise(question, 2);
        
        if (resultats.length > 0) {
            console.log('✅ Informations trouvées:');
            resultats.forEach((resultat, index) => {
                console.log(`   ${index + 1}. ${resultat.titre}`);
                console.log(`      ${resultat.description.substring(0, 80)}...`);
                console.log(`      Source: ${resultat.source}`);
            });
        } else {
            console.log('⚠️ Aucune information trouvée');
        }
        
        // Pause entre questions
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n✅ Test recherche automatique terminé');
}

// Fonction de test pour la sauvegarde continue
async function testerSauvegardeContinue() {
    console.log('\n💾 TEST SAUVEGARDE CONTINUE');
    console.log('============================');
    
    const sauvegarde = new SystemeSauvegardeUltraSecurise();
    
    // Attendre initialisation
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('📝 Simulation modifications fichiers...');
    
    // Simuler modifications de fichiers critiques
    const fs = require('fs');
    const testFile = './test-sauvegarde.json';
    
    for (let i = 0; i < 5; i++) {
        const data = {
            timestamp: Date.now(),
            iteration: i,
            data: `Test data ${i}`,
            random: Math.random()
        };
        
        fs.writeFileSync(testFile, JSON.stringify(data, null, 2));
        console.log(`📝 Fichier modifié (${i + 1}/5)`);
        
        // Attendre détection et sauvegarde
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Nettoyer
    if (fs.existsSync(testFile)) {
        fs.unlinkSync(testFile);
    }
    
    const stats = sauvegarde.obtenirStatistiques();
    console.log('📊 Statistiques sauvegarde:', stats);
    
    console.log('✅ Test sauvegarde continue terminé');
}

// Exécuter tous les tests
async function executerTousLesTests() {
    console.log('🚀 DÉMARRAGE TESTS COMPLETS');
    console.log('============================');
    
    try {
        // Test principal
        const testPrincipal = await testerSystemeCompletSecurise();
        
        if (testPrincipal) {
            // Tests spécialisés
            await testerRechercheAutomatique();
            await testerSauvegardeContinue();
            
            console.log('\n🏆 TOUS LES TESTS TERMINÉS AVEC SUCCÈS !');
            console.log('=========================================');
            console.log('🔐 Système ultra-sécurisé opérationnel');
            console.log('💾 Sauvegarde continue active');
            console.log('🔍 Recherche automatique fonctionnelle');
            console.log('🛡️ Protection maximale en place');
            console.log('⚡ Performance optimale');
            
        } else {
            console.log('❌ Échec des tests principaux');
        }
        
    } catch (error) {
        console.error('💥 ERREUR CRITIQUE:', error);
    }
}

// Lancer les tests si exécuté directement
if (require.main === module) {
    executerTousLesTests();
}

module.exports = {
    testerSystemeCompletSecurise,
    testerRechercheAutomatique,
    testerSauvegardeContinue,
    executerTousLesTests
};
