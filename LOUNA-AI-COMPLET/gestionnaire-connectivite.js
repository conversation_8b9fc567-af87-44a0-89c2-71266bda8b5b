/**
 * 📡 GESTIONNAIRE DE CONNECTIVITÉ POUR LOUNA-AI (MODE LECTURE SEULE)
 *
 * Module de lecture de l'état Wi-Fi, AirDrop, Bluetooth et connectivité réseau
 * AUCUNE ACTION AUTOMATIQUE - Mode manuel uniquement
 */

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class GestionnaireConnectivite {
    constructor() {
        this.etatConnexion = {
            wifi: { actif: false, ssid: null, signal: 0, vitesse: 0 },
            airdrop: { actif: false, visible: false, mode: 'contacts' },
            bluetooth: { actif: false, peripheriques: [] },
            internet: { actif: false, ping: 0, debit: 0 },
            hotspot: { actif: false, nom: null, clients: 0 }
        };
        this.historique = [];
        this.tentativesReconnexion = 0;
        this.maxTentatives = 5;
        this.intervalleVerification = 30000; // 30 secondes
        this.diagnostics = [];
        this.initGestionnaire();
    }

    initGestionnaire() {
        console.log('📡 Gestionnaire de connectivité initialisé (mode manuel)');
        // Vérification automatique désactivée
        // this.verifierConnectiviteComplete();
        this.demarrerSurveillance();
    }

    // VÉRIFICATION COMPLÈTE DE LA CONNECTIVITÉ
    async verifierConnectiviteComplete() {
        console.log('🔍 Vérification complète de la connectivité...');
        
        try {
            await Promise.all([
                this.verifierWiFi(),
                this.verifierAirDrop(),
                this.verifierBluetooth(),
                this.verifierInternet(),
                this.verifierHotspot()
            ]);

            this.enregistrerEtat();
            return this.genererRapportConnectivite();

        } catch (error) {
            console.error('❌ Erreur vérification connectivité:', error);
            this.diagnostics.push({
                timestamp: Date.now(),
                type: 'erreur',
                message: `Erreur vérification: ${error.message}`
            });
        }
    }

    // GESTION WI-FI
    async verifierWiFi() {
        return new Promise((resolve) => {
            exec('networksetup -getairportnetwork en0', (error, stdout, stderr) => {
                if (error) {
                    this.etatConnexion.wifi = { actif: false, ssid: null, signal: 0, vitesse: 0 };
                    this.diagnostics.push({
                        timestamp: Date.now(),
                        type: 'wifi',
                        message: 'Wi-Fi non connecté ou interface non trouvée'
                    });
                } else {
                    const match = stdout.match(/Current Wi-Fi Network: (.+)/);
                    if (match) {
                        this.etatConnexion.wifi.actif = true;
                        this.etatConnexion.wifi.ssid = match[1].trim();
                        this.obtenirSignalWiFi();
                    } else {
                        this.etatConnexion.wifi.actif = false;
                    }
                }
                resolve();
            });
        });
    }

    async obtenirSignalWiFi() {
        return new Promise((resolve) => {
            exec('/System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport -I', (error, stdout) => {
                if (!error) {
                    const signalMatch = stdout.match(/agrCtlRSSI: (-?\d+)/);
                    const vitesseMatch = stdout.match(/lastTxRate: (\d+)/);
                    
                    if (signalMatch) {
                        this.etatConnexion.wifi.signal = parseInt(signalMatch[1]);
                    }
                    if (vitesseMatch) {
                        this.etatConnexion.wifi.vitesse = parseInt(vitesseMatch[1]);
                    }
                }
                resolve();
            });
        });
    }

    async activerWiFi() {
        console.log('📶 Activation du Wi-Fi...');
        return new Promise((resolve) => {
            exec('networksetup -setairportpower en0 on', (error) => {
                if (error) {
                    console.error('❌ Erreur activation Wi-Fi:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    console.log('✅ Wi-Fi activé');
                    setTimeout(() => this.verifierWiFi(), 3000);
                    resolve({ success: true, message: 'Wi-Fi activé avec succès' });
                }
            });
        });
    }

    async connecterWiFi(ssid, motDePasse = null) {
        console.log(`📶 Connexion au réseau Wi-Fi: ${ssid}`);
        
        let commande = `networksetup -setairportnetwork en0 "${ssid}"`;
        if (motDePasse) {
            commande += ` "${motDePasse}"`;
        }

        return new Promise((resolve) => {
            exec(commande, (error) => {
                if (error) {
                    console.error('❌ Erreur connexion Wi-Fi:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    console.log(`✅ Connecté au Wi-Fi: ${ssid}`);
                    setTimeout(() => this.verifierWiFi(), 5000);
                    resolve({ success: true, message: `Connecté au réseau ${ssid}` });
                }
            });
        });
    }

    // GESTION AIRDROP
    async verifierAirDrop() {
        return new Promise((resolve) => {
            exec('defaults read com.apple.sharingd DiscoverableMode', (error, stdout) => {
                if (error) {
                    this.etatConnexion.airdrop.actif = false;
                } else {
                    const mode = stdout.trim();
                    this.etatConnexion.airdrop.actif = mode !== 'Off';
                    this.etatConnexion.airdrop.visible = mode !== 'Off';
                    this.etatConnexion.airdrop.mode = mode === 'Everyone' ? 'tous' : 'contacts';
                }
                resolve();
            });
        });
    }

    async activerAirDrop(mode = 'contacts') {
        console.log(`📤 Activation d'AirDrop en mode: ${mode}`);
        
        const modeSystem = mode === 'tous' ? 'Everyone' : 'Contacts Only';
        
        return new Promise((resolve) => {
            exec(`defaults write com.apple.sharingd DiscoverableMode "${modeSystem}"`, (error) => {
                if (error) {
                    console.error('❌ Erreur activation AirDrop:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    // Redémarrer le service AirDrop
                    exec('sudo launchctl unload /System/Library/LaunchDaemons/com.apple.sharingd.plist && sudo launchctl load /System/Library/LaunchDaemons/com.apple.sharingd.plist', () => {
                        console.log('✅ AirDrop activé');
                        setTimeout(() => this.verifierAirDrop(), 3000);
                        resolve({ success: true, message: `AirDrop activé en mode ${mode}` });
                    });
                }
            });
        });
    }

    async desactiverAirDrop() {
        console.log('📤 Désactivation d\'AirDrop...');
        
        return new Promise((resolve) => {
            exec('defaults write com.apple.sharingd DiscoverableMode "Off"', (error) => {
                if (error) {
                    console.error('❌ Erreur désactivation AirDrop:', error);
                    resolve({ success: false, message: error.message });
                } else {
                    console.log('✅ AirDrop désactivé');
                    this.etatConnexion.airdrop.actif = false;
                    resolve({ success: true, message: 'AirDrop désactivé' });
                }
            });
        });
    }

    // GESTION BLUETOOTH
    async verifierBluetooth() {
        return new Promise((resolve) => {
            exec('system_profiler SPBluetoothDataType', (error, stdout) => {
                if (error) {
                    this.etatConnexion.bluetooth.actif = false;
                } else {
                    this.etatConnexion.bluetooth.actif = stdout.includes('Bluetooth Power: On');
                    // Extraire les périphériques connectés
                    const peripheriques = this.extrairePeripheriques(stdout);
                    this.etatConnexion.bluetooth.peripheriques = peripheriques;
                }
                resolve();
            });
        });
    }

    extrairePeripheriques(stdout) {
        const peripheriques = [];
        const lignes = stdout.split('\n');
        let peripheriqueActuel = null;

        for (const ligne of lignes) {
            if (ligne.includes(':') && !ligne.includes('Bluetooth') && ligne.trim().length > 0) {
                if (peripheriqueActuel) {
                    peripheriques.push(peripheriqueActuel);
                }
                peripheriqueActuel = {
                    nom: ligne.split(':')[0].trim(),
                    connecte: false,
                    type: 'inconnu'
                };
            } else if (peripheriqueActuel && ligne.includes('Connected: Yes')) {
                peripheriqueActuel.connecte = true;
            } else if (peripheriqueActuel && ligne.includes('Minor Type:')) {
                peripheriqueActuel.type = ligne.split(':')[1].trim();
            }
        }

        if (peripheriqueActuel) {
            peripheriques.push(peripheriqueActuel);
        }

        return peripheriques;
    }

    // GESTION INTERNET
    async verifierInternet() {
        return new Promise((resolve) => {
            const debut = Date.now();
            exec('ping -c 1 *******', (error, stdout) => {
                const ping = Date.now() - debut;
                
                if (error) {
                    this.etatConnexion.internet.actif = false;
                    this.etatConnexion.internet.ping = 0;
                } else {
                    this.etatConnexion.internet.actif = true;
                    this.etatConnexion.internet.ping = ping;
                    this.testerDebit();
                }
                resolve();
            });
        });
    }

    async testerDebit() {
        // Test de débit simplifié
        return new Promise((resolve) => {
            const debut = Date.now();
            exec('curl -s -w "%{speed_download}" -o /dev/null http://speedtest.ftp.otenet.gr/files/test1Mb.db', (error, stdout) => {
                if (!error && stdout) {
                    const debit = Math.round(parseFloat(stdout) / 1024); // KB/s
                    this.etatConnexion.internet.debit = debit;
                }
                resolve();
            });
        });
    }

    // GESTION HOTSPOT
    async verifierHotspot() {
        return new Promise((resolve) => {
            exec('system_profiler SPAirPortDataType', (error, stdout) => {
                if (error) {
                    this.etatConnexion.hotspot.actif = false;
                } else {
                    // Vérifier si le hotspot personnel est actif
                    this.etatConnexion.hotspot.actif = stdout.includes('Personal Hotspot');
                    if (this.etatConnexion.hotspot.actif) {
                        const nomMatch = stdout.match(/Network Name: (.+)/);
                        if (nomMatch) {
                            this.etatConnexion.hotspot.nom = nomMatch[1].trim();
                        }
                    }
                }
                resolve();
            });
        });
    }

    // SURVEILLANCE CONTINUE (DÉSACTIVÉE)
    demarrerSurveillance() {
        console.log('👁️ Surveillance de connectivité désactivée (mode manuel)');

        // Surveillance désactivée pour éviter les reconnexions automatiques
        // La vérification se fait uniquement sur demande via l'API

        // setInterval(async () => {
        //     await this.verifierConnectiviteComplete();
        //
        //     // Auto-correction si nécessaire
        //     if (!this.etatConnexion.wifi.actif && this.tentativesReconnexion < this.maxTentatives) {
        //         console.log('🔄 Tentative de reconnexion Wi-Fi...');
        //         await this.activerWiFi();
        //         this.tentativesReconnexion++;
        //     }
        //
        // }, this.intervalleVerification);
    }

    // ENREGISTREMENT ET HISTORIQUE
    enregistrerEtat() {
        const etat = {
            timestamp: Date.now(),
            ...this.etatConnexion
        };
        
        this.historique.push(etat);
        
        // Garder seulement les 100 derniers états
        if (this.historique.length > 100) {
            this.historique.shift();
        }
    }

    // GÉNÉRATION DE RAPPORTS
    genererRapportConnectivite() {
        const wifi = this.etatConnexion.wifi;
        const airdrop = this.etatConnexion.airdrop;
        const bluetooth = this.etatConnexion.bluetooth;
        const internet = this.etatConnexion.internet;
        const hotspot = this.etatConnexion.hotspot;

        return `
📡 **RAPPORT DE CONNECTIVITÉ LOUNA-AI**
=====================================

📶 **WI-FI :**
• État : ${wifi.actif ? '✅ Connecté' : '❌ Déconnecté'}
• Réseau : ${wifi.ssid || 'Aucun'}
• Signal : ${wifi.signal}dBm
• Vitesse : ${wifi.vitesse}Mbps

📤 **AIRDROP :**
• État : ${airdrop.actif ? '✅ Actif' : '❌ Inactif'}
• Visibilité : ${airdrop.visible ? '👁️ Visible' : '🔒 Masqué'}
• Mode : ${airdrop.mode}

🔵 **BLUETOOTH :**
• État : ${bluetooth.actif ? '✅ Actif' : '❌ Inactif'}
• Périphériques : ${bluetooth.peripheriques.length}
${bluetooth.peripheriques.map(p => `  • ${p.nom} (${p.connecte ? 'Connecté' : 'Déconnecté'})`).join('\n')}

🌐 **INTERNET :**
• État : ${internet.actif ? '✅ Connecté' : '❌ Déconnecté'}
• Ping : ${internet.ping}ms
• Débit : ${internet.debit}KB/s

📱 **HOTSPOT :**
• État : ${hotspot.actif ? '✅ Actif' : '❌ Inactif'}
• Nom : ${hotspot.nom || 'Aucun'}
• Clients : ${hotspot.clients}

🔧 **DIAGNOSTICS :**
${this.diagnostics.slice(-5).map(d => 
    `• ${new Date(d.timestamp).toLocaleTimeString()} - ${d.type}: ${d.message}`
).join('\n')}

📊 **RECOMMANDATIONS :**
${this.genererRecommandations()}
        `.trim();
    }

    genererRecommandations() {
        const recommandations = [];

        if (!this.etatConnexion.wifi.actif) {
            recommandations.push('🔧 Activer le Wi-Fi pour une meilleure connectivité');
        } else if (this.etatConnexion.wifi.signal < -70) {
            recommandations.push('📶 Signal Wi-Fi faible, se rapprocher du routeur');
        }

        if (!this.etatConnexion.airdrop.actif) {
            recommandations.push('📤 Activer AirDrop pour le partage de fichiers');
        }

        if (!this.etatConnexion.internet.actif) {
            recommandations.push('🌐 Vérifier la connexion Internet');
        } else if (this.etatConnexion.internet.ping > 100) {
            recommandations.push('⚡ Latence élevée, vérifier la qualité de connexion');
        }

        if (this.etatConnexion.bluetooth.actif && this.etatConnexion.bluetooth.peripheriques.length === 0) {
            recommandations.push('🔵 Aucun périphérique Bluetooth connecté');
        }

        return recommandations.length > 0 ? 
            recommandations.map(r => `• ${r}`).join('\n') : 
            '• ✅ Toutes les connexions sont optimales';
    }

    // MÉTHODES D'API
    obtenirEtatConnectivite() {
        return {
            etat: this.etatConnexion,
            historique: this.historique.slice(-10),
            diagnostics: this.diagnostics.slice(-10),
            tentativesReconnexion: this.tentativesReconnexion,
            derniereMiseAJour: Date.now()
        };
    }

    async correctionAutomatique() {
        console.log('🔧 Correction automatique désactivée (mode manuel)');

        // Correction automatique désactivée pour éviter les modifications non désirées
        return {
            success: false,
            message: 'Correction automatique désactivée - utilisez les commandes manuelles',
            corrections: []
        };
    }
}

module.exports = { GestionnaireConnectivite };
