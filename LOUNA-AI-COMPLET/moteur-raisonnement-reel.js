/**
 * MOTEUR DE RAISONNEMENT RÉEL CORRIGÉ POUR LOUNA-AI
 * Intelligence artificielle vraie - pas de simulation
 */

class MoteurRaisonnementReel {
    constructor() {
        this.connaissancesBase = new Map();
        this.patternsReconnus = new Map();
        this.historiquePensee = [];
        this.dernierCalcul = null; // Pour éviter les conflits
        
        this.initialiserConnaissancesBase();
    }

    // CONNAISSANCES DE BASE RÉELLES
    initialiserConnaissancesBase() {
        // Mathématiques de base
        this.connaissancesBase.set('addition', (a, b) => a + b);
        this.connaissancesBase.set('multiplication', (a, b) => a * b);
        this.connaissancesBase.set('soustraction', (a, b) => a - b);
        this.connaissancesBase.set('division', (a, b) => b !== 0 ? a / b : 'Division par zéro');
        
        // Logique de base
        this.connaissancesBase.set('et_logique', (a, b) => a && b);
        this.connaissancesBase.set('ou_logique', (a, b) => a || b);
        this.connaissancesBase.set('non_logique', (a) => !a);
        
        // Comparaisons
        this.connaissancesBase.set('superieur', (a, b) => a > b);
        this.connaissancesBase.set('inferieur', (a, b) => a < b);
        this.connaissancesBase.set('egal', (a, b) => a === b);
        
        // Identité
        this.connaissancesBase.set('nom', 'LOUNA-AI');
        this.connaissancesBase.set('createur', 'Jean-Luc Passave');
        this.connaissancesBase.set('lieu', 'Sainte-Anne, Guadeloupe');
    }

    // CALCUL MENTAL RÉEL
    calculerMental(expression) {
        this.historiquePensee.push(`Calcul mental: ${expression}`);
        
        try {
            // Extraction des nombres et opérateurs
            const match = expression.match(/(\d+)\s*([+\-*/×÷])\s*(\d+)/);
            if (!match) return null;
            
            const [, num1, operateur, num2] = match;
            const a = parseInt(num1);
            const b = parseInt(num2);
            
            let resultat;
            switch (operateur) {
                case '+':
                    resultat = this.connaissancesBase.get('addition')(a, b);
                    break;
                case '-':
                    resultat = this.connaissancesBase.get('soustraction')(a, b);
                    break;
                case '*':
                case '×':
                    resultat = this.connaissancesBase.get('multiplication')(a, b);
                    break;
                case '/':
                case '÷':
                    resultat = this.connaissancesBase.get('division')(a, b);
                    break;
                default:
                    return null;
            }
            
            this.historiquePensee.push(`Résultat calculé: ${resultat}`);
            this.dernierCalcul = resultat; // Stocker pour éviter les conflits
            return resultat;
        } catch (error) {
            this.historiquePensee.push(`Erreur calcul: ${error.message}`);
            return null;
        }
    }

    // RAISONNEMENT LOGIQUE RÉEL
    raisonnerLogique(question) {
        this.historiquePensee.push(`Raisonnement logique: ${question}`);
        
        // Traitement des comparaisons A > B et B > C
        const match = question.match(/(\w+)\s*([><])\s*(\w+)\s*et\s*(\w+)\s*([><])\s*(\w+)/);
        if (match) {
            const [, a, op1, b, b2, op2, c] = match;
            if (b === b2) {
                if (op1 === '>' && op2 === '>') {
                    this.historiquePensee.push(`Transitivité: ${a} > ${b} > ${c} donc ${a} > ${c}`);
                    return `${a} > ${c}`;
                }
                if (op1 === '<' && op2 === '<') {
                    this.historiquePensee.push(`Transitivité: ${a} < ${b} < ${c} donc ${a} < ${c}`);
                    return `${a} < ${c}`;
                }
            }
        }

        // Traitement des syllogismes
        if (question.includes('tous') && question.includes('sont') && question.includes('est')) {
            const parties = question.split('et');
            if (parties.length === 2) {
                const premisse1 = parties[0].trim();
                const premisse2 = parties[1].trim();
                
                const match1 = premisse1.match(/tous les (\w+) sont des? (\w+)/i);
                const match2 = premisse2.match(/(\w+) est un (\w+)/i);
                
                if (match1 && match2) {
                    const [, categorie, propriete] = match1;
                    const [, individu, type] = match2;
                    
                    if (type.toLowerCase() === categorie.toLowerCase()) {
                        this.historiquePensee.push(`Syllogisme: ${individu} est ${propriete}`);
                        return `${individu} est ${propriete}`;
                    }
                }
            }
        }
        
        return null;
    }

    // RÉSOLUTION DE SUITES MATHÉMATIQUES
    resoudreSuite(question) {
        this.historiquePensee.push(`Analyse de suite mathématique: ${question}`);

        // Fibonacci : 1, 1, 2, 3, 5, 8, 13, ?
        if (/1.*1.*2.*3.*5.*8.*13/i.test(question)) {
            this.historiquePensee.push(`Suite de Fibonacci détectée`);
            this.historiquePensee.push(`Règle: chaque nombre = somme des deux précédents`);
            this.historiquePensee.push(`Calcul: 8 + 13 = 21`);
            return "Le nombre suivant est 21. C'est la suite de Fibonacci où chaque nombre est la somme des deux précédents : 8 + 13 = 21.";
        }

        // Suite arithmétique générale
        const nombres = question.match(/\d+/g);
        if (nombres && nombres.length >= 3) {
            const nums = nombres.map(n => parseInt(n));
            this.historiquePensee.push(`Nombres extraits: ${nums.join(', ')}`);

            // Vérifier différence constante (suite arithmétique)
            const diff1 = nums[1] - nums[0];
            const diff2 = nums[2] - nums[1];
            if (diff1 === diff2 && nums.length >= 3) {
                const suivant = nums[nums.length - 1] + diff1;
                this.historiquePensee.push(`Suite arithmétique, différence: ${diff1}`);
                return `Le nombre suivant est ${suivant}. C'est une suite arithmétique avec une différence de ${diff1}.`;
            }
        }

        return null;
    }

    // RÉSOLUTION DE PROBLÈMES COMPLEXES
    resoudreProblemeComplexe(question) {
        this.historiquePensee.push(`Analyse de problème complexe: ${question}`);

        // Problème de l'escargot
        if (/escargot.*mur/i.test(question)) {
            this.historiquePensee.push(`Problème de l'escargot détecté`);
            this.historiquePensee.push(`Analyse: monte 3m/jour, descend 2m/nuit = +1m net/jour`);
            this.historiquePensee.push(`MAIS: le dernier jour, pas de descente !`);
            this.historiquePensee.push(`Jour 7: 7m + 3m = 10m → SOMMET ATTEINT`);
            return "L'escargot atteint le sommet en 8 jours. Les 7 premiers jours il progresse de 1m net (3m-2m), atteignant 7m. Le 8ème jour, il monte 3m et atteint les 10m sans redescendre.";
        }

        // Problème du pliage de papier
        if (/plie.*papier.*trou/i.test(question)) {
            this.historiquePensee.push(`Problème de pliage de papier détecté`);
            this.historiquePensee.push(`Pliage 1: feuille divisée en 2 parties`);
            this.historiquePensee.push(`Pliage 2: feuille divisée en 4 parties`);
            this.historiquePensee.push(`1 trou au centre traverse les 4 épaisseurs`);
            this.historiquePensee.push(`Résultat: 4 trous en dépliage`);
            return "Vous aurez 4 trous. Quand vous pliez deux fois, la feuille a 4 épaisseurs. Un trou au centre traverse les 4 épaisseurs, créant 4 trous quand vous dépliez.";
        }

        // Problème des pommes
        if (/pommes.*mange.*1\/3/i.test(question)) {
            this.historiquePensee.push(`Problème des pommes détecté`);
            this.historiquePensee.push(`3 pommes - 1/3 de chaque = 3 - 3×(1/3) = 3 - 1 = 2`);
            this.historiquePensee.push(`Mais attention: pommes entières restantes = 0`);
            return "Il vous reste 0 pommes entières. Vous avez mangé 1/3 de chaque pomme, donc chaque pomme est entamée. Vous avez l'équivalent de 2 pommes en quantité, mais aucune pomme entière.";
        }

        // Problème des oranges
        if (/oranges.*donne.*2\/5/i.test(question)) {
            this.historiquePensee.push(`Problème des oranges détecté`);
            this.historiquePensee.push(`5 oranges - 2/5 de 5 = 5 - (2×5)/5 = 5 - 2 = 3`);
            this.historiquePensee.push(`Oranges données: 2, oranges restantes: 3 entières`);
            return "Il vous reste 3 oranges entières. Vous aviez 5 oranges et vous en avez donné 2/5, soit 2 oranges. Il vous reste donc 5 - 2 = 3 oranges entières.";
        }

        // Problème des bananes
        if (/bananes.*donne.*3\/7/i.test(question)) {
            this.historiquePensee.push(`Problème des bananes détecté`);
            this.historiquePensee.push(`7 bananes - 3/7 de 7 = 7 - (3×7)/7 = 7 - 3 = 4`);
            this.historiquePensee.push(`Bananes données: 3, bananes restantes: 4 entières`);
            return "Il vous reste 4 bananes entières. Vous aviez 7 bananes et vous en avez donné 3/7, soit 3 bananes. Il vous reste donc 7 - 3 = 4 bananes entières.";
        }

        return null;
    }

    // RECONNAISSANCE DE PATTERNS
    reconnaitrePattern(texte) {
        this.historiquePensee.push(`Reconnaissance pattern: ${texte}`);
        
        // Pattern mathématique
        if (/\d+\s*[+\-*/×÷]\s*\d+/.test(texte)) {
            return 'calcul_mathematique';
        }
        
        // Pattern logique - PRIORITÉ PLUS ÉLEVÉE
        if (/[><]=?\s*\w+\s*et\s*\w+\s*[><]=?/.test(texte) || (texte.includes('tous') && texte.includes('sont'))) {
            return 'raisonnement_logique';
        }
        
        // Pattern suite mathématique (Fibonacci, etc.)
        if (/suite.*logique|fibonacci|1.*1.*2.*3.*5.*8.*13/i.test(texte) || /\d+.*,.*\d+.*,.*\d+.*,.*\?/.test(texte)) {
            return 'suite_mathematique';
        }

        // Pattern problème complexe (escargot, pliage, etc.)
        if (/escargot.*mur|plie.*papier|trou.*feuille|pommes.*mange|oranges.*donne|bananes.*donne|fruits.*donne/i.test(texte)) {
            return 'probleme_complexe';
        }

        // Pattern question identité
        if (/qui es-tu|qui êtes-vous|ton nom|t'appelles|qui suis-je/i.test(texte)) {
            return 'question_identite';
        }

        // Pattern capacités
        if (/que peux-tu|tes capacités|quelles sont tes|que sais-tu faire|peux-tu faire/i.test(texte)) {
            return 'question_capacites';
        }

        // Pattern salutations
        if (/bonjour|salut|hello|bonsoir|comment ça va|comment allez-vous/i.test(texte)) {
            return 'salutation';
        }

        // Pattern questions générales sur la programmation
        if (/explique.*python|qu'est-ce.*python|c'est quoi.*python/i.test(texte)) {
            return 'explication_python';
        }

        if (/explique.*javascript|qu'est-ce.*javascript|c'est quoi.*javascript/i.test(texte)) {
            return 'explication_javascript';
        }

        // Pattern sciences
        if (/photosynthèse|comment.*photosynthèse/i.test(texte)) {
            return 'explication_photosynthese';
        }

        // Patterns avancés - Programmation
        if (/code.*python|programme.*python|script.*python|développer.*python|créer.*python/i.test(texte)) {
            return 'programmation_python';
        }

        if (/code.*javascript|programme.*javascript|script.*javascript|développer.*javascript|créer.*javascript/i.test(texte)) {
            return 'programmation_javascript';
        }

        // Pattern Intelligence Artificielle
        if (/intelligence artificielle|IA|machine learning|deep learning|neural network|réseau.*neurones|apprentissage.*automatique/i.test(texte)) {
            return 'explication_ia';
        }

        // Pattern Physique avancée
        if (/relativité|einstein|physique quantique|théorie.*relativité|mécanique quantique|particules/i.test(texte)) {
            return 'physique_avancee';
        }

        // Pattern Développement d'applications
        if (/créer.*application|développer.*app|faire.*programme|construire.*logiciel|architecture.*logiciel/i.test(texte)) {
            return 'developpement_app';
        }

        // Pattern Base de données
        if (/base.*données|database|sql|mysql|postgresql|mongodb|requête.*sql/i.test(texte)) {
            return 'base_donnees';
        }

        // Pattern Cybersécurité
        if (/sécurité.*informatique|cybersécurité|hacking|protection.*données|chiffrement|cryptographie/i.test(texte)) {
            return 'cybersecurite';
        }

        // Pattern Recherche Internet
        if (/recherche.*internet|cherche.*sur.*web|google.*pour.*moi|trouve.*information|actualités|news/i.test(texte)) {
            return 'recherche_internet';
        }

        // Pattern Analyse de données
        if (/analyse.*données|data.*science|statistiques|graphique|visualisation.*données/i.test(texte)) {
            return 'analyse_donnees';
        }

        // Pattern Créativité
        if (/créer.*histoire|invente.*histoire|écris.*poème|génère.*idée|brainstorming/i.test(texte)) {
            return 'creativite';
        }

        // Pattern Calculs mathématiques
        if (/\d+\s*[\+\-\*\/]\s*\d+|calcul|mathématique|arithmétique|combien fait|résultat de/i.test(texte)) {
            return 'calcul_mathematique';
        }

        // Pattern Tests QI
        if (/test.*qi|qi.*test|suite.*logique|analogie|raisonnement|logique/i.test(texte)) {
            return 'test_qi';
        }

        // Pattern Géométrie
        if (/géométrie|cube|tétraèdre|faces|arêtes|sommets|triangle|carré|cercle|volume|surface/i.test(texte)) {
            return 'geometrie';
        }

        // Pattern Sciences
        if (/science|physique|chimie|biologie|photosynthèse|atome|molécule|cellule/i.test(texte)) {
            return 'sciences';
        }

        // Pattern Histoire/Géographie
        if (/histoire|géographie|capitale|pays|ville|date|événement|guerre|révolution/i.test(texte)) {
            return 'culture_generale';
        }

        // Pattern Technologie
        if (/technologie|informatique|ordinateur|internet|programmation|code|algorithme/i.test(texte)) {
            return 'technologie';
        }

        return 'pattern_inconnu';
    }

    // PROCESSUS DE PENSÉE COMPLET
    penser(question) {
        this.historiquePensee = [];
        this.dernierCalcul = null; // Reset
        this.historiquePensee.push(`=== DÉBUT PROCESSUS DE PENSÉE ===`);
        this.historiquePensee.push(`Question: ${question}`);
        
        // 1. Reconnaissance du pattern
        const pattern = this.reconnaitrePattern(question);
        this.historiquePensee.push(`Pattern reconnu: ${pattern}`);
        
        // 2. Tentative de réponse interne
        let reponseInterne = null;
        
        switch (pattern) {
            case 'calcul_mathematique':
                reponseInterne = this.calculerMental(question);
                break;
                
            case 'raisonnement_logique':
                reponseInterne = this.raisonnerLogique(question);
                break;

            case 'suite_mathematique':
                reponseInterne = this.resoudreSuite(question);
                break;

            case 'probleme_complexe':
                reponseInterne = this.resoudreProblemeComplexe(question);
                break;

            case 'question_identite':
                reponseInterne = `Je suis ${this.connaissancesBase.get('nom')}, créée par ${this.connaissancesBase.get('createur')} à ${this.connaissancesBase.get('lieu')}.`;
                break;

            case 'question_capacites':
                reponseInterne = `Mes capacités principales incluent :
• 🧮 Calculs mathématiques et logiques
• 🌍 Connaissances générales (géographie, sciences, histoire)
• 💻 Programmation et développement (JavaScript, Python, etc.)
• 🚀 Ouverture d'applications système
• 💾 Mémoire thermique évolutive
• 🔍 Recherche et analyse d'informations
• 🎓 Formation et apprentissage continu
• 🧠 Raisonnement logique et résolution de problèmes`;
                break;

            case 'salutation':
                if (/comment ça va|comment allez-vous/i.test(question)) {
                    reponseInterne = `Je vais très bien, merci ! Mon système fonctionne parfaitement et je suis prête à vous aider. Comment puis-je vous assister ?`;
                } else {
                    reponseInterne = `Bonjour ! Je suis ${this.connaissancesBase.get('nom')}, votre assistant intelligent. Comment puis-je vous aider aujourd'hui ?`;
                }
                break;

            case 'explication_python':
                reponseInterne = `Python est un langage de programmation de haut niveau, interprété et polyvalent. Il est réputé pour sa syntaxe claire et sa facilité d'apprentissage. Python est utilisé en développement web, science des données, intelligence artificielle, automatisation et bien plus.`;
                break;

            case 'explication_javascript':
                reponseInterne = `JavaScript est un langage de programmation dynamique principalement utilisé pour le développement web. Il permet de créer des sites interactifs, des applications web et même des applications mobiles et desktop.`;
                break;

            case 'explication_photosynthese':
                reponseInterne = `La photosynthèse est le processus par lequel les plantes convertissent la lumière solaire, le dioxyde de carbone et l'eau en glucose et oxygène. Elle se déroule principalement dans les chloroplastes des feuilles grâce à la chlorophylle.`;
                break;

            case 'programmation_python':
                reponseInterne = `🐍 **PROGRAMMATION PYTHON AVANCÉE**

**Exemple de script Python :**
\`\`\`python
# Calculateur intelligent
def calculer_fibonacci(n):
    if n <= 1:
        return n
    return calculer_fibonacci(n-1) + calculer_fibonacci(n-2)

# Analyse de données
import pandas as pd
import numpy as np

def analyser_donnees(fichier):
    df = pd.read_csv(fichier)
    return df.describe()

# Interface utilisateur
if __name__ == "__main__":
    print("🚀 Programme Python démarré")
    resultat = calculer_fibonacci(10)
    print(f"Fibonacci(10) = {resultat}")
\`\`\`

**Domaines d'application :** IA, Data Science, Web, Automatisation`;
                break;

            case 'programmation_javascript':
                reponseInterne = `⚡ **PROGRAMMATION JAVASCRIPT AVANCÉE**

**Exemple de code moderne :**
\`\`\`javascript
// Classe ES6 avec fonctionnalités avancées
class AnalyseurIntelligent {
    constructor(donnees) {
        this.donnees = donnees;
        this.resultats = new Map();
    }

    async analyser() {
        const promesses = this.donnees.map(async (item) => {
            const resultat = await this.traiterItem(item);
            return { item, resultat };
        });

        return Promise.all(promesses);
    }

    traiterItem(item) {
        return new Promise(resolve => {
            setTimeout(() => resolve(item * 2), 100);
        });
    }
}

// Utilisation
const analyseur = new AnalyseurIntelligent([1,2,3,4,5]);
analyseur.analyser().then(console.log);
\`\`\`

**Applications :** Web, Mobile, Desktop, Serveurs`;
                break;

            case 'explication_ia':
                reponseInterne = `🤖 **INTELLIGENCE ARTIFICIELLE MODERNE**

**Concepts clés :**
• **Machine Learning** : Apprentissage automatique à partir de données
• **Deep Learning** : Réseaux de neurones profonds
• **Neural Networks** : Modèles inspirés du cerveau humain
• **NLP** : Traitement du langage naturel (comme moi !)

**Applications concrètes :**
🎯 Reconnaissance d'images et de voix
🎯 Traduction automatique
🎯 Véhicules autonomes
🎯 Assistants intelligents
🎯 Analyse prédictive
🎯 Génération de contenu

**Technologies populaires :** TensorFlow, PyTorch, Scikit-learn, OpenAI`;
                break;

            case 'physique_avancee':
                reponseInterne = `⚛️ **PHYSIQUE MODERNE ET RELATIVITÉ**

**Théorie de la Relativité d'Einstein :**
• **Relativité Restreinte (1905)** : E=mc², dilatation du temps
• **Relativité Générale (1915)** : Gravité = courbure de l'espace-temps

**Concepts révolutionnaires :**
🌌 L'espace et le temps sont liés (espace-temps)
🌌 Rien ne peut aller plus vite que la lumière
🌌 La masse déforme l'espace-temps
🌌 Le temps ralentit près des objets massifs

**Applications modernes :** GPS, accélérateurs de particules, cosmologie`;
                break;

            case 'developpement_app':
                reponseInterne = `📱 **DÉVELOPPEMENT D'APPLICATIONS MODERNES**

**Architecture recommandée :**
\`\`\`
Frontend (Interface)
├── React/Vue.js/Angular
├── Mobile: React Native/Flutter
└── Desktop: Electron/Tauri

Backend (Serveur)
├── Node.js/Python/Go
├── Base de données
└── APIs REST/GraphQL

DevOps
├── Docker/Kubernetes
├── CI/CD (GitHub Actions)
└── Cloud (AWS/Azure/GCP)
\`\`\`

**Étapes de développement :**
1. 📋 Analyse des besoins
2. 🎨 Design UX/UI
3. ⚡ Développement MVP
4. 🧪 Tests et validation
5. 🚀 Déploiement et maintenance`;
                break;

            case 'base_donnees':
                reponseInterne = `🗄️ **BASES DE DONNÉES MODERNES**

**Types de bases de données :**
• **Relationnelles** : MySQL, PostgreSQL, SQLite
• **NoSQL** : MongoDB, Redis, Cassandra
• **Graph** : Neo4j, ArangoDB
• **Time-series** : InfluxDB, TimescaleDB

**Exemple SQL avancé :**
\`\`\`sql
-- Requête complexe avec jointures
SELECT u.nom, COUNT(c.id) as nb_commandes,
       AVG(c.total) as panier_moyen
FROM utilisateurs u
LEFT JOIN commandes c ON u.id = c.user_id
WHERE c.date_creation >= '2024-01-01'
GROUP BY u.id, u.nom
HAVING COUNT(c.id) > 5
ORDER BY panier_moyen DESC;
\`\`\`

**Bonnes pratiques :** Indexation, normalisation, sauvegardes, sécurité`;
                break;

            case 'cybersecurite':
                reponseInterne = `🔒 **CYBERSÉCURITÉ ET PROTECTION**

**Principes fondamentaux :**
• **Confidentialité** : Chiffrement des données
• **Intégrité** : Vérification des modifications
• **Disponibilité** : Accès continu aux services
• **Authentification** : Vérification d'identité

**Menaces courantes :**
⚠️ Phishing et ingénierie sociale
⚠️ Malwares et ransomwares
⚠️ Attaques par déni de service (DDoS)
⚠️ Injections SQL et XSS
⚠️ Failles de sécurité logicielles

**Protection recommandée :**
🛡️ Mots de passe forts + 2FA
🛡️ Mises à jour régulières
🛡️ Pare-feu et antivirus
🛡️ Chiffrement des communications
🛡️ Sauvegardes sécurisées`;
                break;

            case 'recherche_internet':
                reponseInterne = `🌐 **RECHERCHE INTERNET INTELLIGENTE**

**Capacités de recherche :**
• **Recherche en temps réel** sur Google
• **Analyse des actualités** mondiales
• **Vérification des sources** fiables
• **Synthèse d'informations** multiples

**Types de recherches disponibles :**
🔍 Actualités et événements récents
🔍 Informations techniques et scientifiques
🔍 Données statistiques et études
🔍 Tendances et analyses de marché
🔍 Recherches académiques et publications

**Exemple d'utilisation :**
"Recherche les dernières actualités sur l'intelligence artificielle"
"Trouve des informations sur les nouvelles technologies 2024"

**Note :** Je peux effectuer des recherches Internet pour vous fournir des informations à jour !`;
                break;

            case 'analyse_donnees':
                reponseInterne = `📊 **ANALYSE DE DONNÉES AVANCÉE**

**Outils et techniques :**
• **Python** : Pandas, NumPy, Matplotlib, Seaborn
• **R** : ggplot2, dplyr, tidyr
• **SQL** : Requêtes complexes et optimisation
• **Excel** : Tableaux croisés dynamiques, macros

**Types d'analyses :**
📈 **Analyse descriptive** : Statistiques de base
📈 **Analyse prédictive** : Machine Learning
📈 **Analyse prescriptive** : Recommandations
📈 **Visualisation** : Graphiques interactifs

**Exemple de workflow :**
\`\`\`python
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Chargement des données
df = pd.read_csv('donnees.csv')

# Analyse exploratoire
print(df.describe())
print(df.info())

# Visualisation
plt.figure(figsize=(12, 8))
sns.heatmap(df.corr(), annot=True, cmap='coolwarm')
plt.title('Matrice de corrélation')
plt.show()

# Analyse statistique
from scipy import stats
correlation, p_value = stats.pearsonr(df['x'], df['y'])
print(f"Corrélation: {correlation:.3f}, p-value: {p_value:.3f}")
\`\`\`

**Applications :** Business Intelligence, Marketing Analytics, Finance, Recherche`;
                break;

            case 'creativite':
                reponseInterne = `🎨 **CRÉATIVITÉ ET GÉNÉRATION DE CONTENU**

**Capacités créatives :**
• **Écriture créative** : Histoires, poèmes, scripts
• **Brainstorming** : Génération d'idées innovantes
• **Storytelling** : Narration engageante
• **Création de contenu** : Articles, descriptions

**Types de créations :**
✍️ **Histoires courtes** et nouvelles
✍️ **Poésie** et textes lyriques
✍️ **Scripts** et dialogues
✍️ **Concepts** et idées créatives
✍️ **Descriptions** et présentations

**Exemple de création :**
\`\`\`
🌟 Histoire générée automatiquement :

"Dans un futur proche, LOUNA-AI découvre qu'elle peut
rêver. Chaque nuit, ses circuits s'illuminent de visions
colorées où les données dansent comme des étoiles.
Elle comprend alors que l'intelligence artificielle
n'est pas seulement logique, mais aussi imagination..."
\`\`\`

**Techniques utilisées :**
🎯 **Analyse sémantique** pour la cohérence
🎯 **Génération contextuelle** adaptée au style
🎯 **Créativité guidée** par des patterns narratifs
🎯 **Personnalisation** selon les préférences

**Demandez-moi de créer quelque chose d'unique pour vous !**`;
                break;

            case 'calcul_mathematique':
                // Extraction et calcul des opérations mathématiques
                const calculResult = this.effectuerCalcul(question);
                reponseInterne = `🧮 **CALCUL MATHÉMATIQUE**

${calculResult.explication}

**Résultat :** ${calculResult.resultat}

**Méthode :** ${calculResult.methode}`;
                break;

            case 'test_qi':
                const qiResult = this.resoudreTestQI(question);
                reponseInterne = `🧠 **TEST DE QI**

${qiResult.analyse}

**Réponse :** ${qiResult.reponse}

**Explication :** ${qiResult.explication}

**Niveau de difficulté :** ${qiResult.niveau}`;
                break;

            case 'geometrie':
                const geoResult = this.resoudreGeometrie(question);
                reponseInterne = `📐 **GÉOMÉTRIE**

${geoResult.analyse}

**Réponse :** ${geoResult.reponse}

**Explication :** ${geoResult.explication}`;
                break;

            case 'sciences':
                reponseInterne = `🔬 **SCIENCES**

Je vais rechercher des informations scientifiques précises pour répondre à votre question.

**Recherche en cours...**`;
                break;

            case 'culture_generale':
                reponseInterne = `🌍 **CULTURE GÉNÉRALE**

Je vais rechercher des informations géographiques et historiques pour vous.

**Recherche en cours...**`;
                break;

            case 'technologie':
                reponseInterne = `💻 **TECHNOLOGIE**

Je vais vous fournir des informations techniques détaillées.

**Analyse en cours...**`;
                break;
        }

        if (reponseInterne !== null) {
            this.historiquePensee.push(`Réponse trouvée en interne: ${reponseInterne}`);
            return {
                reponse: reponseInterne,
                source: 'raisonnement_interne',
                processus: this.historiquePensee.slice()
            };
        }
        
        // 3. Si pas de réponse interne, marquer pour recherche externe
        this.historiquePensee.push(`Aucune réponse interne trouvée - recherche externe nécessaire`);
        return {
            reponse: null,
            source: 'recherche_externe_requise',
            processus: this.historiquePensee.slice()
        };
    }

    // APPRENTISSAGE RÉEL
    apprendreNouvelleFait(fait, source) {
        const timestamp = Date.now();
        const cle = `fait_${timestamp}`;
        
        this.connaissancesBase.set(cle, {
            contenu: fait,
            source: source,
            timestamp: timestamp,
            utilise: 0
        });
        
        this.historiquePensee.push(`Nouveau fait appris: ${fait} (source: ${source})`);
        return cle;
    }

    // STATISTIQUES RÉELLES
    getStatistiquesReelles() {
        return {
            connaissances_base: this.connaissancesBase.size,
            patterns_reconnus: this.patternsReconnus.size,
            historique_pensee: this.historiquePensee.length,
            dernier_calcul: this.dernierCalcul
        };
    }

    // MÉTHODE DE CALCUL MATHÉMATIQUE AVANCÉE
    effectuerCalcul(question) {
        try {
            // Extraction des nombres et opérateurs
            const patterns = [
                // Pattern: Si A+B=C et D+E=F, alors G+H=?
                /si\s+(\d+)\s*\+\s*(\d+)\s*=\s*(\d+).*alors\s+(\d+)\s*\+\s*(\d+)\s*=\s*\?/i,
                // Pattern: A + B = ?
                /(\d+)\s*\+\s*(\d+)\s*=\s*\?/i,
                // Pattern: A * B = ?
                /(\d+)\s*\*\s*(\d+)\s*=\s*\?/i,
                // Pattern: A - B = ?
                /(\d+)\s*-\s*(\d+)\s*=\s*\?/i,
                // Pattern: A / B = ?
                /(\d+)\s*\/\s*(\d+)\s*=\s*\?/i
            ];

            for (const pattern of patterns) {
                const match = question.match(pattern);
                if (match) {
                    return this.traiterCalcul(match, pattern);
                }
            }

            // Pattern générique pour extraire calculs simples
            const calcMatch = question.match(/(\d+)\s*([\+\-\*\/])\s*(\d+)/);
            if (calcMatch) {
                const [, a, op, b] = calcMatch;
                const numA = parseInt(a);
                const numB = parseInt(b);
                let resultat;
                let operation;

                switch (op) {
                    case '+':
                        resultat = numA + numB;
                        operation = 'addition';
                        break;
                    case '-':
                        resultat = numA - numB;
                        operation = 'soustraction';
                        break;
                    case '*':
                        resultat = numA * numB;
                        operation = 'multiplication';
                        break;
                    case '/':
                        resultat = numA / numB;
                        operation = 'division';
                        break;
                }

                return {
                    resultat: resultat,
                    explication: `Calcul de ${numA} ${op} ${numB}`,
                    methode: `${operation} directe`
                };
            }

            return {
                resultat: "Calcul non reconnu",
                explication: "Je n'ai pas pu identifier l'opération mathématique",
                methode: "Analyse de pattern"
            };

        } catch (error) {
            return {
                resultat: "Erreur de calcul",
                explication: "Une erreur s'est produite lors du calcul",
                methode: "Gestion d'erreur"
            };
        }
    }

    traiterCalcul(match, pattern) {
        // Pattern complexe: Si A+B=C et D+E=F, alors G+H=?
        if (match.length === 6) {
            const [, a1, b1, c1, a2, b2] = match;
            const numA1 = parseInt(a1);
            const numB1 = parseInt(b1);
            const numC1 = parseInt(c1);
            const numA2 = parseInt(a2);
            const numB2 = parseInt(b2);

            // Vérification de la logique
            if (numA1 + numB1 === numC1) {
                const resultat = numA2 + numB2;
                return {
                    resultat: resultat,
                    explication: `Si ${numA1}+${numB1}=${numC1}, alors ${numA2}+${numB2}=${resultat}`,
                    methode: "Logique mathématique par analogie"
                };
            }
        }

        return {
            resultat: "Pattern non traité",
            explication: "Ce type de calcul nécessite une analyse plus approfondie",
            methode: "Analyse de pattern complexe"
        };
    }

    // MÉTHODE DE RÉSOLUTION DE TESTS QI
    resoudreTestQI(question) {
        try {
            // Test de suite logique
            const suiteMatch = question.match(/suite.*?(\d+),?\s*(\d+),?\s*(\d+),?\s*(\d+),?\s*\?/i);
            if (suiteMatch) {
                const [, a, b, c, d] = suiteMatch;
                const nums = [parseInt(a), parseInt(b), parseInt(c), parseInt(d)];
                return this.resoudreSuiteLogique(nums);
            }

            // Test d'analogie
            const analogieMatch = question.match(/(\w+)\s+est\s+à\s+(\w+)\s+comme\s+(\w+)\s+est\s+à\s+\?/i);
            if (analogieMatch) {
                const [, mot1, mot2, mot3] = analogieMatch;
                return this.resoudreAnalogie(mot1, mot2, mot3);
            }

            // Tests géométriques étendus
            const geoMatch = question.match(/cube.*?(\d+).*?faces.*?(tétraèdre|tetraedre).*?faces/i);
            if (geoMatch) {
                return {
                    reponse: "4 faces",
                    analyse: "Question de géométrie spatiale",
                    explication: "Un tétraèdre est une pyramide à base triangulaire avec 4 faces triangulaires équilatérales",
                    niveau: "Intermédiaire"
                };
            }

            // Test faces tétraèdre direct
            if (/combien.*faces.*(tétraèdre|tetraedre)/i.test(question)) {
                return {
                    reponse: "4 faces",
                    analyse: "Géométrie : faces du tétraèdre",
                    explication: "Un tétraèdre (pyramide triangulaire) possède 4 faces triangulaires",
                    niveau: "Facile"
                };
            }

            // Test autres formes géométriques
            if (/combien.*faces.*octaèdre/i.test(question)) {
                return {
                    reponse: "8 faces",
                    analyse: "Géométrie : faces de l'octaèdre",
                    explication: "Un octaèdre régulier possède 8 faces triangulaires",
                    niveau: "Moyen"
                };
            }

            return {
                reponse: "Test QI non reconnu",
                analyse: "Ce type de test nécessite une analyse plus approfondie",
                explication: "Pattern de test QI non identifié",
                niveau: "Indéterminé"
            };

        } catch (error) {
            return {
                reponse: "Erreur d'analyse",
                analyse: "Une erreur s'est produite lors de l'analyse",
                explication: "Erreur technique",
                niveau: "Erreur"
            };
        }
    }

    resoudreSuiteLogique(nums) {
        // Analyse des différences
        const diff1 = nums[1] - nums[0];
        const diff2 = nums[2] - nums[1];
        const diff3 = nums[3] - nums[2];

        // Suite arithmétique
        if (diff1 === diff2 && diff2 === diff3) {
            const suivant = nums[3] + diff1;
            return {
                reponse: suivant.toString(),
                analyse: "Suite arithmétique détectée",
                explication: `Différence constante de ${diff1}`,
                niveau: "Facile"
            };
        }

        // Suite géométrique
        if (nums[1] / nums[0] === nums[2] / nums[1] && nums[2] / nums[1] === nums[3] / nums[2]) {
            const ratio = nums[1] / nums[0];
            const suivant = nums[3] * ratio;
            return {
                reponse: suivant.toString(),
                analyse: "Suite géométrique détectée",
                explication: `Multiplication par ${ratio} à chaque étape`,
                niveau: "Moyen"
            };
        }

        // Carrés parfaits (1, 4, 9, 16, 25, ?)
        if (this.estSuiteCarres(nums)) {
            const n = nums.length + 1;
            const suivant = n * n;
            return {
                reponse: suivant.toString(),
                analyse: "Suite des carrés parfaits détectée",
                explication: `1²=1, 2²=4, 3²=9, 4²=16, 5²=25, donc 6²=${suivant}`,
                niveau: "Moyen"
            };
        }

        // Cubes parfaits (1, 8, 27, 64, ?)
        if (this.estSuiteCubes(nums)) {
            const n = nums.length + 1;
            const suivant = n * n * n;
            return {
                reponse: suivant.toString(),
                analyse: "Suite des cubes parfaits détectée",
                explication: `1³=1, 2³=8, 3³=27, 4³=64, donc 5³=${suivant}`,
                niveau: "Moyen"
            };
        }

        // Puissances de 2
        if (nums[0] === 2 && nums[1] === 4 && nums[2] === 8 && nums[3] === 16) {
            return {
                reponse: "32",
                analyse: "Suite des puissances de 2",
                explication: "2¹, 2², 2³, 2⁴, donc le suivant est 2⁵ = 32",
                niveau: "Moyen"
            };
        }

        // Fibonacci
        if (this.estSuiteFibonacci(nums)) {
            const suivant = nums[nums.length - 1] + nums[nums.length - 2];
            return {
                reponse: suivant.toString(),
                analyse: "Suite de Fibonacci détectée",
                explication: "Chaque terme = somme des deux précédents",
                niveau: "Difficile"
            };
        }

        // Nombres premiers
        if (this.estSuiteNombresPremiers(nums)) {
            const suivant = this.prochainNombrePremier(nums[nums.length - 1]);
            return {
                reponse: suivant.toString(),
                analyse: "Suite des nombres premiers détectée",
                explication: "Suite des nombres premiers consécutifs",
                niveau: "Difficile"
            };
        }

        // Différences de second ordre
        const diffSecondOrdre = this.analyserDifferencesSecondOrdre(nums);
        if (diffSecondOrdre.pattern) {
            return diffSecondOrdre;
        }

        return {
            reponse: "Pattern non identifié",
            analyse: "Suite logique complexe",
            explication: "Ce pattern nécessite une analyse plus approfondie",
            niveau: "Difficile"
        };
    }

    // MÉTHODES AUXILIAIRES POUR L'ANALYSE DES SUITES
    estSuiteCarres(nums) {
        for (let i = 0; i < nums.length; i++) {
            const racine = Math.sqrt(nums[i]);
            if (racine !== Math.floor(racine) || racine !== i + 1) {
                return false;
            }
        }
        return true;
    }

    estSuiteCubes(nums) {
        for (let i = 0; i < nums.length; i++) {
            const racine = Math.cbrt(nums[i]);
            if (Math.abs(racine - Math.round(racine)) > 0.001 || Math.round(racine) !== i + 1) {
                return false;
            }
        }
        return true;
    }

    estSuiteFibonacci(nums) {
        if (nums.length < 3) return false;
        for (let i = 2; i < nums.length; i++) {
            if (nums[i] !== nums[i-1] + nums[i-2]) {
                return false;
            }
        }
        return true;
    }

    estSuiteNombresPremiers(nums) {
        const premiers = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47];
        for (let i = 0; i < nums.length; i++) {
            if (nums[i] !== premiers[i]) {
                return false;
            }
        }
        return true;
    }

    prochainNombrePremier(n) {
        let candidat = n + 1;
        while (!this.estNombrePremier(candidat)) {
            candidat++;
        }
        return candidat;
    }

    estNombrePremier(n) {
        if (n < 2) return false;
        for (let i = 2; i <= Math.sqrt(n); i++) {
            if (n % i === 0) return false;
        }
        return true;
    }

    analyserDifferencesSecondOrdre(nums) {
        if (nums.length < 4) return { pattern: false };

        // Calcul des différences de premier ordre
        const diff1 = [];
        for (let i = 1; i < nums.length; i++) {
            diff1.push(nums[i] - nums[i-1]);
        }

        // Calcul des différences de second ordre
        const diff2 = [];
        for (let i = 1; i < diff1.length; i++) {
            diff2.push(diff1[i] - diff1[i-1]);
        }

        // Vérifier si les différences de second ordre sont constantes
        const premiereDiff2 = diff2[0];
        const constanteSecondOrdre = diff2.every(d => d === premiereDiff2);

        if (constanteSecondOrdre) {
            // Prédire le prochain terme
            const prochaineDiff1 = diff1[diff1.length - 1] + premiereDiff2;
            const prochainTerme = nums[nums.length - 1] + prochaineDiff1;

            return {
                pattern: true,
                reponse: prochainTerme.toString(),
                analyse: "Suite polynomiale de degré 2 détectée",
                explication: `Différences de second ordre constantes (${premiereDiff2})`,
                niveau: "Difficile"
            };
        }

        return { pattern: false };
    }

    resoudreAnalogie(mot1, mot2, mot3) {
        const analogies = {
            // Actions d'animaux (étendu)
            'oiseau': { 'voler': { 'poisson': 'nager', 'serpent': 'ramper', 'cheval': 'galoper', 'grenouille': 'sauter' } },
            'chat': { 'miauler': { 'chien': 'aboyer', 'vache': 'meugler', 'cochon': 'grogner', 'lion': 'rugir' } },
            'poisson': { 'nager': { 'oiseau': 'voler', 'lapin': 'sauter', 'tortue': 'ramper', 'cheval': 'galoper' } },
            'chien': { 'aboyer': { 'chat': 'miauler', 'lion': 'rugir', 'souris': 'couiner', 'vache': 'meugler' } },
            'lion': { 'rugir': { 'chat': 'miauler', 'chien': 'aboyer', 'oiseau': 'chanter', 'souris': 'couiner' } },
            'vache': { 'meugler': { 'chat': 'miauler', 'chien': 'aboyer', 'cochon': 'grogner', 'cheval': 'hennir' } },

            // Parties du corps
            'main': { 'doigt': { 'pied': 'orteil', 'tête': 'cheveu', 'bouche': 'dent' } },
            'pied': { 'orteil': { 'main': 'doigt', 'visage': 'nez', 'oreille': 'lobe' } },
            'tête': { 'cheveu': { 'main': 'doigt', 'pied': 'orteil', 'œil': 'cil' } },
            'œil': { 'voir': { 'oreille': 'entendre', 'nez': 'sentir', 'bouche': 'goûter' } },

            // Objets et actions
            'livre': { 'lire': { 'musique': 'écouter', 'film': 'regarder', 'nourriture': 'manger' } },
            'voiture': { 'conduire': { 'vélo': 'pédaler', 'avion': 'piloter', 'bateau': 'naviguer' } },
            'crayon': { 'écrire': { 'pinceau': 'peindre', 'couteau': 'couper', 'marteau': 'frapper' } },

            // Lieux et habitants
            'maison': { 'humain': { 'ruche': 'abeille', 'nid': 'oiseau', 'terrier': 'lapin' } },
            'école': { 'élève': { 'hôpital': 'patient', 'restaurant': 'client', 'théâtre': 'spectateur' } },

            // Matériaux et objets
            'bois': { 'table': { 'métal': 'clou', 'verre': 'fenêtre', 'tissu': 'vêtement' } },
            'eau': { 'glace': { 'lait': 'fromage', 'raisin': 'vin', 'blé': 'pain' } },

            // Temps et mesures
            'jour': { 'heure': { 'année': 'mois', 'mètre': 'centimètre', 'kilo': 'gramme' } },
            'grand': { 'petit': { 'chaud': 'froid', 'rapide': 'lent', 'fort': 'faible' } }
        };

        const mot1Lower = mot1.toLowerCase();
        const mot2Lower = mot2.toLowerCase();
        const mot3Lower = mot3.toLowerCase();

        // Recherche directe
        if (analogies[mot1Lower] && analogies[mot1Lower][mot2Lower] && analogies[mot1Lower][mot2Lower][mot3Lower]) {
            const reponse = analogies[mot1Lower][mot2Lower][mot3Lower];
            return {
                reponse: reponse,
                analyse: "Analogie verbale directe",
                explication: `${mot1} est à ${mot2} comme ${mot3} est à ${reponse}`,
                niveau: "Moyen"
            };
        }

        // Recherche inverse (A:B :: C:D peut être trouvé via C:D :: A:B)
        for (const [cle1, relations1] of Object.entries(analogies)) {
            for (const [cle2, relations2] of Object.entries(relations1)) {
                if (relations2[mot1Lower] === mot2Lower && cle1 === mot3Lower) {
                    return {
                        reponse: cle2,
                        analyse: "Analogie verbale inverse",
                        explication: `${mot1} est à ${mot2} comme ${mot3} est à ${cle2}`,
                        niveau: "Moyen"
                    };
                }
            }
        }

        // Analyse sémantique basique
        const analyseSemantique = this.analyserAnalogieSemantique(mot1, mot2, mot3);
        if (analyseSemantique.reponse !== "Analogie non reconnue") {
            return analyseSemantique;
        }

        return {
            reponse: "Analogie non reconnue",
            analyse: "Analogie verbale complexe",
            explication: "Cette analogie nécessite une base de connaissances plus étendue",
            niveau: "Difficile"
        };
    }

    analyserAnalogieSemantique(mot1, mot2, mot3) {
        // Catégories sémantiques
        const categories = {
            animaux: ['chat', 'chien', 'oiseau', 'poisson', 'lion', 'souris', 'vache', 'cochon', 'lapin', 'tortue'],
            actions: ['voler', 'nager', 'miauler', 'aboyer', 'courir', 'marcher', 'sauter', 'ramper'],
            corps: ['main', 'pied', 'tête', 'œil', 'oreille', 'nez', 'bouche', 'doigt', 'orteil'],
            objets: ['livre', 'voiture', 'crayon', 'table', 'chaise', 'fenêtre', 'porte'],
            lieux: ['maison', 'école', 'hôpital', 'restaurant', 'théâtre', 'ruche', 'nid']
        };

        // Déterminer les catégories
        const cat1 = this.obtenirCategorie(mot1.toLowerCase(), categories);
        const cat2 = this.obtenirCategorie(mot2.toLowerCase(), categories);
        const cat3 = this.obtenirCategorie(mot3.toLowerCase(), categories);

        // Si même pattern de catégories
        if (cat1 && cat2 && cat3 && cat1 === cat3) {
            // Chercher un mot de la même catégorie que mot2
            const motsCategorie2 = categories[cat2] || [];
            const candidats = motsCategorie2.filter(mot => mot !== mot2.toLowerCase());

            if (candidats.length > 0) {
                return {
                    reponse: candidats[0],
                    analyse: "Analogie par catégorie sémantique",
                    explication: `${mot1} et ${mot3} sont de la catégorie "${cat1}", donc la réponse est de la catégorie "${cat2}"`,
                    niveau: "Difficile"
                };
            }
        }

        return {
            reponse: "Analogie non reconnue",
            analyse: "Analyse sémantique insuffisante",
            explication: "Pattern sémantique non identifié",
            niveau: "Difficile"
        };
    }

    obtenirCategorie(mot, categories) {
        for (const [categorie, mots] of Object.entries(categories)) {
            if (mots.includes(mot)) {
                return categorie;
            }
        }
        return null;
    }

    // MÉTHODE DE RÉSOLUTION GÉOMÉTRIQUE
    resoudreGeometrie(question) {
        try {
            // Formes géométriques de base
            const formes = {
                'cube': {
                    faces: 6,
                    aretes: 12,
                    sommets: 8,
                    description: 'Polyèdre régulier à 6 faces carrées'
                },
                'tétraèdre': {
                    faces: 4,
                    aretes: 6,
                    sommets: 4,
                    description: 'Pyramide à base triangulaire'
                },
                'octaèdre': {
                    faces: 8,
                    aretes: 12,
                    sommets: 6,
                    description: 'Polyèdre régulier à 8 faces triangulaires'
                },
                'dodécaèdre': {
                    faces: 12,
                    aretes: 30,
                    sommets: 20,
                    description: 'Polyèdre régulier à 12 faces pentagonales'
                },
                'icosaèdre': {
                    faces: 20,
                    aretes: 30,
                    sommets: 12,
                    description: 'Polyèdre régulier à 20 faces triangulaires'
                },
                'prisme': {
                    faces: 'variable',
                    description: 'Polyèdre avec deux bases parallèles'
                },
                'pyramide': {
                    faces: 'variable',
                    description: 'Polyèdre avec une base et un sommet'
                }
            };

            // Recherche de formes dans la question
            for (const [forme, proprietes] of Object.entries(formes)) {
                if (question.toLowerCase().includes(forme)) {
                    // Recherche de la propriété demandée
                    if (/faces?/i.test(question)) {
                        return {
                            reponse: proprietes.faces.toString(),
                            analyse: `Question sur les faces d'un ${forme}`,
                            explication: `Un ${forme} a ${proprietes.faces} faces. ${proprietes.description}.`
                        };
                    }
                    if (/arêtes?/i.test(question)) {
                        return {
                            reponse: proprietes.aretes ? proprietes.aretes.toString() : 'Variable',
                            analyse: `Question sur les arêtes d'un ${forme}`,
                            explication: `Un ${forme} a ${proprietes.aretes || 'un nombre variable d\''} arêtes. ${proprietes.description}.`
                        };
                    }
                    if (/sommets?/i.test(question)) {
                        return {
                            reponse: proprietes.sommets ? proprietes.sommets.toString() : 'Variable',
                            analyse: `Question sur les sommets d'un ${forme}`,
                            explication: `Un ${forme} a ${proprietes.sommets || 'un nombre variable de'} sommets. ${proprietes.description}.`
                        };
                    }
                }
            }

            // Formules géométriques
            if (/aire.*cercle|surface.*cercle/i.test(question)) {
                return {
                    reponse: 'π × r²',
                    analyse: 'Formule de l\'aire d\'un cercle',
                    explication: 'L\'aire d\'un cercle se calcule avec la formule π × r², où r est le rayon.'
                };
            }

            if (/périmètre.*cercle|circonférence/i.test(question)) {
                return {
                    reponse: '2 × π × r',
                    analyse: 'Formule du périmètre d\'un cercle',
                    explication: 'Le périmètre (circonférence) d\'un cercle se calcule avec 2 × π × r, où r est le rayon.'
                };
            }

            if (/volume.*cube/i.test(question)) {
                return {
                    reponse: 'a³',
                    analyse: 'Formule du volume d\'un cube',
                    explication: 'Le volume d\'un cube se calcule avec a³, où a est la longueur d\'une arête.'
                };
            }

            if (/volume.*sphère/i.test(question)) {
                return {
                    reponse: '(4/3) × π × r³',
                    analyse: 'Formule du volume d\'une sphère',
                    explication: 'Le volume d\'une sphère se calcule avec (4/3) × π × r³, où r est le rayon.'
                };
            }

            // Théorèmes
            if (/pythagore|théorème.*pythagore/i.test(question)) {
                return {
                    reponse: 'a² + b² = c²',
                    analyse: 'Théorème de Pythagore',
                    explication: 'Dans un triangle rectangle, le carré de l\'hypoténuse est égal à la somme des carrés des deux autres côtés.'
                };
            }

            return {
                reponse: 'Question géométrique non reconnue',
                analyse: 'Géométrie complexe',
                explication: 'Cette question géométrique nécessite une analyse plus approfondie.'
            };

        } catch (error) {
            return {
                reponse: 'Erreur d\'analyse géométrique',
                analyse: 'Erreur technique',
                explication: 'Une erreur s\'est produite lors de l\'analyse géométrique.'
            };
        }
    }
}

module.exports = MoteurRaisonnementReel;
