/**
 * Luna Interface - Scripts côté client pour l'interface cognitive Luna
 * Gère les connexions WebSocket, interactions utilisateur et périphériques
 */

// Initialiser la connexion Socket.IO
const socket = io();

// Fonction pour obtenir la date et l'heure actuelles formatées
function getCurrentDateTime() {
  const now = new Date();

  // Options pour formater la date
  const dateOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };

  // Options pour formater l'heure
  const timeOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  };

  // Formater la date et l'heure en français
  const formattedDate = now.toLocaleDateString('fr-FR', dateOptions);
  const formattedTime = now.toLocaleTimeString('fr-FR', timeOptions);

  // Première lettre en majuscule
  const capitalizedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

  return {
    date: capitalizedDate,
    time: formattedTime,
    full: `${capitalizedDate} à ${formattedTime}`,
    timestamp: now.getTime()
  };
}

// État global des fonctionnalités
const lunaState = {
  systemActive: false,
  microphoneActive: false,
  visionActive: false,
  speechActive: false,
  thermalSleepMode: false,
  recordingAudio: false,
  videoStream: null,
  availableModels: [],
  activeModel: 'deepseek-r1:7b',
  isModelConnected: false,
  currentDateTime: getCurrentDateTime()
};

// Fonction pour sauvegarder l'état des périphériques
function saveDeviceState() {
  const deviceState = {
    microphoneActive: lunaState.microphoneActive,
    visionActive: lunaState.visionActive,
    speechActive: lunaState.speechActive,
    lastSaved: new Date().toISOString()
  };

  try {
    localStorage.setItem('visionUltraDeviceState', JSON.stringify(deviceState));
    console.log('État des périphériques sauvegardé:', deviceState);
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de l\'état des périphériques:', error);
  }
}

// Fonction pour charger l'état des périphériques
function loadDeviceState() {
  try {
    const savedState = localStorage.getItem('visionUltraDeviceState');
    if (savedState) {
      const deviceState = JSON.parse(savedState);
      console.log('État des périphériques chargé:', deviceState);

      // Mettre à jour l'état global
      lunaState.microphoneActive = deviceState.microphoneActive || false;
      lunaState.visionActive = deviceState.visionActive || false;
      lunaState.speechActive = deviceState.speechActive || false;

      return deviceState;
    }
  } catch (error) {
    console.error('Erreur lors du chargement de l\'état des périphériques:', error);
  }

  return null;
}

// Événements Socket.IO
socket.on('connect', () => {
  console.log('🔌 Connecté au serveur Luna');
  updateConnectionStatus(true);

  // Vérifier le statut d'Ollama au démarrage
  setTimeout(() => {
    checkOllamaStatus();
  }, 1000);

  // Démarrer la mise à jour de l'heure
  startClockUpdate();
});

socket.on('disconnect', () => {
  console.log('🔌 Déconnecté du serveur Luna');
  updateConnectionStatus(false);
  deactivateAllPeripherals();

  // Afficher un message de déconnexion
  showConnectionStatus('Déconnecté du serveur Luna. Tentative de reconnexion...', 'error');
});

socket.on('luna response', (data) => {
  // Supprimer l'indicateur de frappe
  const typingIndicator = document.getElementById('typing-indicator');
  if (typingIndicator) typingIndicator.remove();

  // Masquer le statut de connexion éventuel
  hideConnectionStatus();

  // Traiter la réponse pour séparer la réflexion du contenu
  let message = data.message;
  let thinking = '';

  // Vérifier si la réponse contient une partie <think>
  if (message.includes('<think>') && message.includes('</think>')) {
    // Extraire la partie réflexion
    thinking = message.substring(
      message.indexOf('<think>') + 7,
      message.indexOf('</think>')
    ).trim();

    // Supprimer la partie réflexion de la réponse
    message = message.replace(/<think>[\s\S]*?<\/think>/, '').trim();

    // Traduire les mots anglais courants en français dans la réflexion
    thinking = translateCommonWords(thinking);
  }

  // Ajouter la réponse de l'agent avec animation de frappe
  setTimeout(() => {
    // Si une réflexion est présente, l'ajouter d'abord (masquée par défaut)
    if (thinking) {
      addThinking(thinking);
    }

    // Ajouter la réponse principale
    addMessage(message, 'agent');

    // Synthétiser vocalement si la synthèse vocale est active
    if (lunaState.speechActive && 'speechSynthesis' in window) {
      const msg = new SpeechSynthesisUtterance(message);
      msg.lang = 'fr-FR';
      window.speechSynthesis.speak(msg);
    }

    // Réactiver l'interface utilisateur
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');

    if (userInput) {
      userInput.disabled = false;
      userInput.focus();
    }

    if (sendButton) {
      sendButton.disabled = false;
    }

    // Mettre à jour le statut de l'agent
    updateAgentStatus(false);
  }, 500); // Léger délai pour simuler la frappe
});

// Gestion des états de connexion à Ollama
let ollamaConnectionAttempts = 0;
let ollamaReconnectTimer = null;
let ollamaLastConnected = false; // Éviter les messages redondants
let ollamaLastDisconnected = false; // Éviter les messages redondants

// Écouter les mises à jour d'état d'Ollama
socket.on('ollama status', (data) => {
  console.log('Réponse de statut d\'Ollama reçue:', data);
  const previousState = lunaState.isOllamaConnected;
  lunaState.isOllamaConnected = data.connected;

  // Arrêter la tentative de reconnexion précédente si elle existe
  if (ollamaReconnectTimer) {
    clearTimeout(ollamaReconnectTimer);
    ollamaReconnectTimer = null;
  }

  if (data.connected) {
    // Si Ollama vient juste d'être connecté (état précédent = déconnecté)
    if (!previousState || !ollamaLastConnected) {
      hideConnectionStatus();
      updateSystemDashboard(`Connexion établie avec Ollama (${data.version || 'version inconnue'}). Modèle actif: ${data.model || 'inconnu'}`);
      showNotification('Connexion établie avec Ollama', 'success');
      ollamaLastConnected = true;
      ollamaLastDisconnected = false;
      ollamaConnectionAttempts = 0; // Réinitialiser le compteur de tentatives
    }

    // Mettre à jour le modèle et charger la liste
    lunaState.activeModel = data.model;
    loadAvailableModels();

    // Mettre à jour visuellement l'interface
    updateOllamaStatusUI(true);
  } else {
    // Si Ollama vient juste d'être déconnecté (état précédent = connecté)
    if (previousState || !ollamaLastDisconnected) {
      ollamaConnectionAttempts++;
      const errorMsg = data.error ?
        `Erreur de connexion à Ollama: ${data.error}` :
        "Ollama n'est pas détecté. Vérification en cours...";

      console.error(`${errorMsg} (Tentative ${ollamaConnectionAttempts})`);
      showConnectionStatus(errorMsg, 'error');

      if (!ollamaLastDisconnected) {
        updateSystemDashboard(errorMsg);
        ollamaLastDisconnected = true;
        ollamaLastConnected = false;
        showNotification('Connexion à Ollama perdue', 'error');
      }

      // Mettre à jour l'interface
      updateOllamaStatusUI(false);

      // Programmer une nouvelle tentative
      const reconnectDelay = Math.min(2000 * Math.pow(1.5, ollamaConnectionAttempts-1), 15000); // Backoff exponentiel

      if (ollamaConnectionAttempts <= 10) { // Limiter à 10 tentatives
        showConnectionStatus(`Tentative de reconnexion dans ${Math.round(reconnectDelay/1000)} secondes...`, 'warning');

        ollamaReconnectTimer = setTimeout(() => {
          showConnectionStatus('Tentative de reconnexion en cours...', 'info');
          checkOllamaStatus();
        }, reconnectDelay);
      } else if (ollamaConnectionAttempts === 11) {
        // Après plusieurs tentatives échouées, afficher des instructions détaillées
        updateSystemDashboard("Plusieurs tentatives de connexion ont échoué. Pour résoudre les problèmes de connexion à Ollama:\n" +
          "1. Vérifiez qu'Ollama est installé et en cours d'exécution\n" +
          "2. Exécutez 'ollama serve' dans un terminal\n" +
          "3. Vérifiez que l'API Ollama est disponible sur http://localhost:11434\n" +
          "4. Utilisez le script start-ollama.sh dans le dossier de l'application\n" +
          "5. Cliquez sur '⟳' pour réessayer la connexion manuellement");

        showConnectionStatus('Connexion à Ollama impossible. Cliquez sur \'⟳\' pour réessayer.', 'error');
        $('#reconnect-button').show();
      }
    }
  }
});

// Fonction pour mettre à jour le tableau de bord système avec les messages système
function updateSystemDashboard(message) {
  // Trouver ou créer le conteneur de messages système
  let systemMessagesContainer = document.getElementById('system-messages');

  if (!systemMessagesContainer) {
    // Si le conteneur n'existe pas, le créer dans la carte MCP
    const mcpCard = document.querySelector('.luna-card h4 i.bi-cpu')?.closest('.luna-card');

    if (mcpCard) {
      // Créer une nouvelle section pour les messages système
      const systemSection = document.createElement('div');
      systemSection.className = 'mt-3 pt-2 border-top border-secondary';
      systemSection.innerHTML = `
        <h6 class="mb-2"><i class="bi bi-info-circle me-2"></i> Messages système</h6>
        <div id="system-messages" class="small" style="max-height: 150px; overflow-y: auto; background: rgba(0,0,0,0.2); border-radius: 5px; padding: 8px;"></div>
      `;

      mcpCard.appendChild(systemSection);
      systemMessagesContainer = document.getElementById('system-messages');
    }
  }

  if (systemMessagesContainer) {
    // Ajouter le message au conteneur
    const messageElement = document.createElement('div');
    messageElement.className = 'system-message-item mb-1';

    // Formater le message avec l'heure
    const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    messageElement.innerHTML = `
      <small class="text-muted">${timestamp}</small>
      <span class="ms-1">${message}</span>
    `;

    // Ajouter au début pour que les messages les plus récents soient en haut
    systemMessagesContainer.insertBefore(messageElement, systemMessagesContainer.firstChild);

    // Limiter le nombre de messages (garder les 20 derniers)
    const messages = systemMessagesContainer.querySelectorAll('.system-message-item');
    if (messages.length > 20) {
      for (let i = 20; i < messages.length; i++) {
        systemMessagesContainer.removeChild(messages[i]);
      }
    }
  } else {
    // Si on ne peut pas afficher dans l'interface, au moins logger dans la console
    console.log('Message système:', message);
  }
}

// Fonction pour mettre à jour l'interface en fonction du statut Ollama
function updateOllamaStatusUI(connected) {
  const statusIndicator = $('#agentStatus .status-indicator');
  const statusText = $('#agentStatus span:not(.status-indicator)');
  const reconnectButton = $('#reconnect-button');

  // Vérifier si l'élément 'reconnect-button' existe, sinon le créer
  if (reconnectButton.length === 0) {
    const buttonHtml = '<button id="reconnect-button" class="btn btn-sm btn-outline-secondary ms-2" style="display:none;">⟳</button>';
    $('#agentStatus').append(buttonHtml);

    // Ajouter l'event listener
    $(document).on('click', '#reconnect-button', function() {
      showConnectionStatus('Tentative de reconnexion en cours...', 'info');
      $(this).prop('disabled', true).text('⟳ ...');
      setTimeout(() => $(this).prop('disabled', false).text('⟳'), 5000);
      ollamaConnectionAttempts = 0; // Réinitialiser le compteur
      checkOllamaStatus();
    });
  }

  if (connected) {
    statusIndicator.removeClass('status-inactive').addClass('status-active');
    statusText.text('Connecté');
    $('#send-button').prop('disabled', false);
    $('#user-input').prop('disabled', false).attr('placeholder', 'Écrivez votre message ici...');
    $('#reconnect-button').hide();

    // Activer les fonctionnalités de caméra et microphone si disponibles
    if (lunaState.hasCamera) $('#toggleVisionBtn').prop('disabled', false);
    if (lunaState.hasMicrophone) $('#toggleListenBtn').prop('disabled', false);

    // Mettre à jour la sélection du modèle
    if (lunaState.activeModel) {
      $('#model-selector').val(lunaState.activeModel);
    }
  } else {
    statusIndicator.removeClass('status-active').addClass('status-inactive');
    statusText.text('Déconnecté');
    $('#send-button').prop('disabled', true);
    $('#user-input').prop('disabled', true).attr('placeholder', 'Connexion à Ollama requise...');

    // Désactiver les fonctionnalités de caméra et microphone
    $('#toggleVisionBtn').prop('disabled', true);
    $('#toggleListenBtn').prop('disabled', true);

    // Si plus de 5 tentatives, montrer le bouton de reconnexion manuelle
    if (ollamaConnectionAttempts > 5) {
      $('#reconnect-button').show();
    }
  }
}

// Fonction pour démarrer la mise à jour de l'horloge
function startClockUpdate() {
  // Mettre à jour l'heure immédiatement
  updateClock();

  // Mettre à jour l'heure toutes les secondes
  setInterval(updateClock, 1000);
}

// Fonction pour mettre à jour l'horloge
function updateClock() {
  // Mettre à jour l'état global
  lunaState.currentDateTime = getCurrentDateTime();

  // Mettre à jour l'affichage de l'horloge dans l'interface
  const clockElement = document.getElementById('current-time');
  if (clockElement) {
    clockElement.textContent = lunaState.currentDateTime.time;
  }

  const dateElement = document.getElementById('current-date');
  if (dateElement) {
    dateElement.textContent = lunaState.currentDateTime.date;
  }

  // Mettre à jour l'élément current-datetime dans le pied de page
  const datetimeElement = document.getElementById('current-datetime');
  if (datetimeElement) {
    datetimeElement.textContent = new Date().toLocaleString('fr-FR');
  }
}

// Fonction pour montrer des notifications
function showNotification(message, type = 'info') {
  const notificationArea = $('#notification-area');
  if (!notificationArea.length) {
    $('body').append('<div id="notification-area"></div>');
  }

  const notification = $(`<div class="notification notification-${type}">${message}</div>`);
  $('#notification-area').append(notification);

  setTimeout(() => {
    notification.addClass('show');

    setTimeout(() => {
      notification.removeClass('show');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }, 100);
}

// Fonction pour copier le contenu d'un message
function copyMessageContent(button) {
  // Trouver le conteneur du message
  const messageBubble = button.closest('.message-bubble');
  const messageContent = messageBubble.querySelector('.message-content');

  // Extraire le texte du message
  const textToCopy = messageContent.innerText || messageContent.textContent;

  // Copier le texte dans le presse-papier
  navigator.clipboard.writeText(textToCopy)
    .then(() => {
      // Changer l'icône et la classe pour indiquer que le texte a été copié
      button.innerHTML = '<i class="bi bi-check"></i>';
      button.classList.add('copied');

      // Rétablir l'icône après 2 secondes
      setTimeout(() => {
        button.innerHTML = '<i class="bi bi-clipboard"></i>';
        button.classList.remove('copied');
      }, 2000);

      // Afficher une notification
      showNotification('Message copié dans le presse-papier', 'success');
    })
    .catch(err => {
      console.error('Erreur lors de la copie du texte:', err);
      // Afficher une notification d'erreur
      showNotification('Impossible de copier le texte', 'error');
    });
}

function checkOllamaStatus() {
  console.log('Vérification du statut d\'Ollama...');
  socket.emit('check ollama');

  // Ajouter une indication visuelle que la vérification est en cours
  showConnectionStatus('Vérification de la connexion à Ollama...', 'info');

  // Programmer une vérification régulière du statut
  if (!lunaState.ollamaStatusInterval) {
    lunaState.ollamaStatusInterval = setInterval(() => {
      socket.emit('check ollama');
    }, 10000); // Vérifier toutes les 10 secondes
  }
}

// Afficher un message de statut de connexion
function showConnectionStatus(message, type = 'info') {
  const statusElement = document.getElementById('connection-status');
  if (statusElement) {
    const content = statusElement.querySelector('.message-content p');
    if (content) {
      let icon = 'bi-info-circle';
      if (type === 'error') icon = 'bi-exclamation-triangle';
      else if (type === 'success') icon = 'bi-check-circle';

      content.innerHTML = `<i class="bi ${icon} me-2"></i> ${message}`;
    }
    statusElement.style.display = 'flex';
  }
}

// Masquer le message de statut de connexion
function hideConnectionStatus() {
  const statusElement = document.getElementById('connection-status');
  if (statusElement) {
    statusElement.style.display = 'none';
  }
}

// ==== GESTION DU SYSTÈME ====

function updateSystemStatus(active) {
  lunaState.systemActive = active;

  const statusIndicator = $('#agentStatus .status-indicator');
  const statusText = $('#agentStatus span:not(.status-indicator)');

  if (active) {
    statusIndicator.removeClass('status-inactive').addClass('status-active');
    statusText.text('Actif');
  } else {
    statusIndicator.removeClass('status-active').addClass('status-inactive');
    statusText.text('Inactif');

    // Désactiver également tous les périphériques
    deactivateAllPeripherals();
  }
}

function updateConnectionStatus(connected) {
  const statusIndicator = $('#connectionStatus .status-indicator');

  if (connected) {
    statusIndicator.removeClass('status-inactive').addClass('status-active');
  } else {
    statusIndicator.removeClass('status-active').addClass('status-inactive');
  }
}

function deactivateAllPeripherals() {
  // Désactiver le microphone
  if (lunaState.microphoneActive) {
    toggleMicrophone(false);
  }

  // Désactiver la caméra
  if (lunaState.visionActive) {
    toggleCamera(false);
  }

  // Désactiver la synthèse vocale
  if (lunaState.speechActive) {
    toggleSpeech(false);
  }
}

// ==== GESTION DES PÉRIPHÉRIQUES ====

// Microphone
function toggleMicrophone(enable = null) {
  // Vérifier la connexion à Ollama
  if (!lunaState.isOllamaConnected && enable !== false) {
    showNotification("Le microphone nécessite une connexion à Ollama", 'error');
    return;
  }

  // Si enable n'est pas spécifié, inverser l'état actuel
  const shouldActivate = enable !== null ? enable : !lunaState.microphoneActive;

  const micButton = document.getElementById('toggleListenBtn');
  const statusIndicator = document.querySelector('#microphoneStatus .status-indicator');

  // Force l'activation quelle que soit la valeur actuelle
  lunaState.microphoneActive = shouldActivate;

  if (shouldActivate && lunaState.systemActive) {
    // Activer le microphone
    statusIndicator.classList.remove('status-inactive');
    statusIndicator.classList.add('status-active');
    micButton.innerHTML = '<i class="bi bi-mic-mute"></i>';
    updateSystemDashboard('Microphone activé. Je vous écoute...');

    try {
      // Message explicite pour l'utilisateur
      updateSystemDashboard('Activation du microphone en cours... Veuillez accepter la demande d\'autorisation.');

      // Définir des contraintes audio explicites
      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      };

      // Demander les permissions d'accès au microphone avec ces contraintes
      navigator.mediaDevices.getUserMedia(constraints)
        .then(stream => {
          lunaState.audioStream = stream;

          // Créer un AudioContext pour traiter l'audio
          let audioContext;
          try {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
          } catch(e) {
            updateSystemDashboard('Votre navigateur ne prend pas en charge l\'API Web Audio. Le microphone fonctionnera sans visualisation.');
            return;
          }

          try {
            // Créer la source audio
            const source = audioContext.createMediaStreamSource(stream);

            // Créer un analyseur pour visualiser l'audio avec des paramètres plus robustes
            const analyser = audioContext.createAnalyser();
            analyser.fftSize = 1024; // Valeur plus élevée pour une meilleure précision
            analyser.smoothingTimeConstant = 0.8;
            source.connect(analyser);

            // Afficher un indicateur de niveau sonore visuel
            const audioLevelContainer = document.createElement('div');
            audioLevelContainer.id = 'audio-level';
            audioLevelContainer.className = 'message system-message';
            audioLevelContainer.innerHTML = `
              <div class="message-content">
                <p>Microphone actif - Niveau sonore:</p>
                <div class="audio-meter" style="height: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; overflow: hidden; margin-top: 5px;">
                  <div id="audio-level-bar" style="height: 100%; width: 0%; background: linear-gradient(to right, #9c89b8, #f0a6ca); transition: width 0.1s;"></div>
                </div>
                <p class="mt-2"><small>Le microphone est maintenant actif et enregistre votre voix.</small></p>
              </div>
            `;
            document.getElementById('conversation-container').appendChild(audioLevelContainer);

            // Mettre à jour le niveau audio en temps réel
            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            const updateAudioLevel = () => {
              if (!lunaState.microphoneActive) return;

              analyser.getByteFrequencyData(dataArray);
              let sum = 0;
              for (let i = 0; i < dataArray.length; i++) {
                sum += dataArray[i];
              }
              const average = sum / dataArray.length;
              const level = Math.min(100, Math.max(0, average * 100 / 256));

              const levelBar = document.getElementById('audio-level-bar');
              if (levelBar) levelBar.style.width = level + '%';

              requestAnimationFrame(updateAudioLevel);
            };

            updateAudioLevel();
            updateSystemDashboard('Microphone activé avec succès');
          } catch(e) {
            console.error('Erreur lors de la création de l\'analyseur audio:', e);
            updateSystemDashboard(`Microphone actif mais sans visualisation: ${e.toString()}`);
          }
        })
        .catch(err => {
          console.error('Erreur d\'accès au microphone:', err);
          updateSystemDashboard(`Erreur d'accès au microphone: ${err.toString()}`);
          statusIndicator.removeClass('status-active').addClass('status-inactive');
          micButton.html('<i class="bi bi-mic"></i>');
          lunaState.microphoneActive = false;
        });
    } catch(e) {
      console.error('Exception lors de l\'activation du microphone:', e);
      updateSystemDashboard(`Exception: ${e.toString()}`);
      statusIndicator.removeClass('status-active').addClass('status-inactive');
      micButton.html('<i class="bi bi-mic"></i>');
      lunaState.microphoneActive = false;
    }
  } else {
    // Désactiver le microphone
    lunaState.microphoneActive = false;
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');
    micButton.innerHTML = '<i class="bi bi-mic"></i>';
    updateSystemDashboard('Microphone désactivé.');
    showNotification('Microphone désactivé', 'info');

    // Arrêter la capture audio
    stopAudioCapture();

    // Informer le serveur
    socket.emit('luna audio toggle', { active: false });
  }
}

// Système audio réel (API Web Audio)
let audioContext = null;
let mediaStream = null;

function startAudioCapture() {
  // Arrêter toute capture existante
  stopAudioCapture();

  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
    navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        mediaStream = stream;
        audioContext = new (window.AudioContext || window.webkitAudioContext)();

        if (lunaState.microphoneActive) {
          addMessage('Microphone activé - Écoute en cours...', 'system');

          // Analyser l'audio réel
          const analyser = audioContext.createAnalyser();
          const source = audioContext.createMediaStreamSource(stream);
          source.connect(analyser);

          // Détecter les niveaux audio
          const dataArray = new Uint8Array(analyser.frequencyBinCount);

          function detectAudio() {
            if (lunaState.microphoneActive) {
              analyser.getByteFrequencyData(dataArray);
              const average = dataArray.reduce((a, b) => a + b) / dataArray.length;

              if (average > 20) { // Seuil de détection
                addMessage('Audio détecté - Niveau: ' + Math.round(average), 'system');
              }

              setTimeout(detectAudio, 100);
            }
          }

          detectAudio();
        }
      })
      .catch(error => {
        console.error('Erreur accès microphone:', error);
        addMessage('Erreur: Impossible d\'accéder au microphone', 'system');
      });
  } else {
    addMessage('Erreur: API audio non supportée par ce navigateur', 'system');
  }
}

function stopAudioCapture() {
  if (mediaStream) {
    mediaStream.getTracks().forEach(track => track.stop());
    mediaStream = null;
  }
  if (audioContext) {
    audioContext.close();
    audioContext = null;
  }
}

// Caméra
function toggleCamera() {
  const cameraButton = document.getElementById('toggleVisionBtn');
  const statusIndicator = document.querySelector('#visionStatus .status-indicator');

  if (lunaState.cameraActive) {
    // Désactiver la caméra si elle est active
    stopCamera();
  } else {
    // Activer la caméra si elle est inactive
    startCamera();
  }
}

function startCamera() {
  // Vérifier la connexion à Ollama
  if (!lunaState.isOllamaConnected) {
    showNotification("La caméra nécessite une connexion à Ollama", 'error');
    return;
  }

  // Mise à jour de l'état et des indicateurs visuels
  const cameraButton = document.getElementById('toggleVisionBtn');
  const statusIndicator = document.querySelector('#visionStatus .status-indicator');

  lunaState.cameraActive = true;
  statusIndicator.classList.remove('status-inactive');
  statusIndicator.classList.add('status-active');
  cameraButton.innerHTML = '<i class="bi bi-camera-fill"></i>';

  // Message système
  updateSystemDashboard("Activation de la caméra...");

  // Créer le conteneur vidéo s'il n'existe pas
  let videoContainer = document.getElementById('video-container');
  if (!videoContainer) {
    videoContainer = document.createElement('div');
    videoContainer.id = 'video-container';
    videoContainer.style.position = 'fixed';
    videoContainer.style.bottom = '80px';
    videoContainer.style.right = '20px';
    videoContainer.style.zIndex = '1000';
    videoContainer.style.transition = 'opacity 0.3s ease';
    videoContainer.style.opacity = '0';

    // Créer l'élément vidéo avec les contrôles
    videoContainer.innerHTML = `
      <div style="position: relative; width: 320px; height: 240px; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
        <video id="video-preview" autoplay playsinline style="width: 100%; height: 100%; object-fit: cover;"></video>
        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.5); color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; cursor: pointer;" onclick="toggleCamera()">
          <i class="bi bi-x"></i>
        </div>
      </div>
    `;

    // Ajouter au DOM
    document.getElementById('conversation-container').appendChild(videoContainer);
  } else {
    // Réinitialiser le conteneur existant
    videoContainer.style.display = 'block';
  }

  // Demander les permissions de caméra avec des options plus précises
  try {
    navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: 640 },
        height: { ideal: 480 },
        frameRate: { ideal: 30 }
      }
    }).then(stream => {
      lunaState.videoStream = stream;

      // Sélectionner l'élément vidéo
      const videoElement = document.getElementById('video-preview');
      if (videoElement) {
        videoElement.srcObject = stream;

        // Afficher le conteneur avec une animation fluide
        setTimeout(() => {
          videoContainer.style.opacity = '1';
        }, 100);

        // Confirmer que la vidéo est chargée
        videoElement.onloadedmetadata = () => {
          videoElement.play();
          updateSystemDashboard('Caméra activée avec succès');
          showNotification('Caméra activée', 'success');

          // Informer le serveur
          socket.emit('luna vision toggle', { active: true });
        };
      }
    }).catch(err => {
      console.error("Erreur d'accès à la caméra:", err);
      updateSystemDashboard(`Erreur d'accès à la caméra: ${err.message || err}. Vérifiez les permissions de votre navigateur.`);
      showNotification("Impossible d'accéder à la caméra", 'error');
      lunaState.cameraActive = false;
      stopCamera(false);
    });
  } catch (e) {
    console.error('Exception lors de l\'accès à la caméra:', e);
    updateSystemDashboard(`Exception: ${e.toString()}`);
    lunaState.cameraActive = false;
    stopCamera(false);
  }
}

function stopCamera(showMessages = true) {
  // Mise à jour de l'état et des indicateurs visuels
  lunaState.cameraActive = false;

  const cameraButton = document.getElementById('toggleVisionBtn');
  if (cameraButton) {
    const statusIndicator = document.querySelector('#visionStatus .status-indicator');
    if (statusIndicator) {
      statusIndicator.classList.remove('status-active');
      statusIndicator.classList.add('status-inactive');
    }
    cameraButton.innerHTML = '<i class="bi bi-camera"></i>';
  }

  // Arrêter le flux vidéo
  if (lunaState.videoStream) {
    lunaState.videoStream.getTracks().forEach(track => {
      track.stop();
      console.log('Track vidéo arrêté:', track.id);
    });
    lunaState.videoStream = null;
  }

  // Masquer le conteneur vidéo avec transition
  const videoContainer = document.getElementById('video-container');
  if (videoContainer) {
    videoContainer.style.opacity = '0';
    setTimeout(() => {
      videoContainer.style.display = 'none';
    }, 300);
  }

  // Afficher des messages si nécessaire
  if (showMessages) {
    updateSystemDashboard('Caméra désactivée.');
    showNotification('Caméra désactivée', 'info');
  }

  // Informer le serveur
  socket.emit('luna vision toggle', { active: false });
  socket.emit('camera status', { active: false });
}

// Fonction pour mettre à jour le tableau de bord système avec les messages système
function updateSystemDashboard(message) {
  // Trouver ou créer le conteneur de messages système
  let systemMessagesContainer = document.getElementById('system-messages');

  if (!systemMessagesContainer) {
    // Si le conteneur n'existe pas, le créer dans la carte MCP
    const mcpCard = document.querySelector('.luna-card h4 i.bi-cpu').closest('.luna-card');

    if (mcpCard) {
      // Créer une nouvelle section pour les messages système
      const systemSection = document.createElement('div');
      systemSection.className = 'mt-3 pt-2 border-top border-secondary';
      systemSection.innerHTML = `
        <h6 class="mb-2"><i class="bi bi-info-circle me-2"></i> Messages système</h6>
        <div id="system-messages" class="small" style="max-height: 150px; overflow-y: auto; background: rgba(0,0,0,0.2); border-radius: 5px; padding: 8px;"></div>
      `;

      mcpCard.appendChild(systemSection);
      systemMessagesContainer = document.getElementById('system-messages');
    }
  }

  if (systemMessagesContainer) {
    // Ajouter le message au conteneur
    const messageElement = document.createElement('div');
    messageElement.className = 'system-message-item mb-1';

    // Formater le message avec l'heure
    const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    messageElement.innerHTML = `
      <small class="text-muted">${timestamp}</small>
      <span class="ms-1">${message}</span>
    `;

    // Ajouter au début pour que les messages les plus récents soient en haut
    systemMessagesContainer.insertBefore(messageElement, systemMessagesContainer.firstChild);

    // Limiter le nombre de messages (garder les 20 derniers)
    const messages = systemMessagesContainer.querySelectorAll('.system-message-item');
    if (messages.length > 20) {
      for (let i = 20; i < messages.length; i++) {
        systemMessagesContainer.removeChild(messages[i]);
      }
    }
  } else {
    // Si on ne peut pas afficher dans l'interface, au moins logger dans la console
    console.log('Message système:', message);
  }
}

// Fonction pour initialiser les styles pour les notifications
function initStyles() {
  // S'assurer que le style n'est ajouté qu'une fois
  if (document.getElementById('luna-notification-styles')) {
    return;
  }

  // Créer un élément de style pour les notifications
  const style = document.createElement('style');
  style.id = 'luna-notification-styles';
  style.innerHTML = `
    #notification-area {
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 9999;
      max-width: 300px;
    }

    .notification {
      margin-bottom: 10px;
      padding: 12px 15px;
      border-radius: 6px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      color: white;
      transform: translateX(120%);
      opacity: 0;
      transition: transform 0.3s ease, opacity 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
      opacity: 1;
    }

    .notification-info {
      background: linear-gradient(135deg, #6bacea, #3f7cda);
    }

    .notification-success {
      background: linear-gradient(135deg, #4caf50, #2e7d32);
    }

    .notification-warning {
      background: linear-gradient(135deg, #ffa726, #f57c00);
    }

    .notification-error {
      background: linear-gradient(135deg, #f44336, #d32f2f);
    }
  `;

  document.head.appendChild(style);

  // Initialiser la zone de notifications si elle n'existe pas déjà
  if (!document.getElementById('notification-area')) {
    const notificationArea = document.createElement('div');
    notificationArea.id = 'notification-area';
    document.body.appendChild(notificationArea);
  }
}

// Synthèse vocale
function toggleSpeech(enable = null) {
  // Vérifier la connexion Ollama
  if (!lunaState.isOllamaConnected && enable !== false) {
    showNotification("La synthèse vocale nécessite une connexion à Ollama", 'error');
    return;
  }

  // Si enable n'est pas spécifié, inverser l'état actuel
  const shouldActivate = enable !== null ? enable : !lunaState.speechActive;

  const speechButton = document.getElementById('testSpeechBtn');
  const statusIndicator = document.querySelector('#speechStatus .status-indicator');

  // Ne rien faire si l'état actuel est déjà celui souhaité
  if (shouldActivate === lunaState.speechActive) return;

  if (shouldActivate && lunaState.systemActive) {
    // Activer la synthèse vocale
    lunaState.speechActive = true;
    statusIndicator.classList.remove('status-inactive');
    statusIndicator.classList.add('status-active');
    speechButton.innerHTML = '<i class="bi bi-volume-mute"></i>';

    const message = 'Synthèse vocale activée. Je vais désormais parler.';
    updateSystemDashboard(message);
    showNotification('Synthèse vocale activée', 'success');

    // Utiliser l'API Web Speech pour parler
    if ('speechSynthesis' in window) {
      const msg = new SpeechSynthesisUtterance(message);
      msg.lang = 'fr-FR';
      window.speechSynthesis.speak(msg);
    }

    // Informer le serveur
    socket.emit('luna speech toggle', { active: true });
  } else {
    // Désactiver la synthèse vocale
    lunaState.speechActive = false;
    statusIndicator.classList.remove('status-active');
    statusIndicator.classList.add('status-inactive');
    speechButton.innerHTML = '<i class="bi bi-chat-quote"></i>';

    // Arrêter toute synthèse vocale en cours
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }

    updateSystemDashboard('Synthèse vocale désactivée.');
    showNotification('Synthèse vocale désactivée', 'info');

    // Informer le serveur
    socket.emit('luna speech toggle', { active: false });
  }
}

// ==== GESTION DES MESSAGES ====

function addMessage(text, role) {
  // Ne pas afficher les messages système dans la conversation
  if (role === 'system') {
    // Au lieu d'afficher dans la conversation, envoyer au tableau de bord des paramètres
    updateSystemDashboard(text);
    return;
  }

  const container = document.getElementById('conversation-container');
  const messageDiv = document.createElement('div');

  messageDiv.className = `message ${role}-message`;

  // Ajouter un avatar et des détails visuels selon le rôle
  let avatar = '';
  let timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

  if (role === 'user') {
    avatar = '<div class="message-avatar user-avatar"><i class="bi bi-person-circle"></i></div>';
  } else if (role === 'agent') {
    avatar = '<div class="message-avatar agent-avatar"><i class="bi bi-robot"></i></div>';
  }

  messageDiv.innerHTML = `
    ${role !== 'user' ? avatar : ''}
    <div class="message-bubble ${role}-bubble">
      <div class="message-content">
        ${formatMessage(text)}
      </div>
      <div class="message-timestamp">${timestamp}</div>
    </div>
    ${role === 'user' ? avatar : ''}
  `;

  container.appendChild(messageDiv);

  // Faire défiler jusqu'au bas
  container.scrollTop = container.scrollHeight;

  // Ajouter des styles dynamiques au document
  addMessageStyles();
}

// Ajouter des styles CSS pour les messages si absents
function addMessageStyles() {
  if (!document.getElementById('luna-chat-styles')) {
    const style = document.createElement('style');
    style.id = 'luna-chat-styles';
    style.textContent = `
      .message {
        display: flex;
        margin-bottom: 1.5rem;
        align-items: flex-end;
        clear: both;
        position: relative;
      }

      .user-message {
        justify-content: flex-end;
        padding-left: 15%;
      }

      .agent-message, .system-message {
        justify-content: flex-start;
        padding-right: 15%;
      }

      .message-bubble {
        border-radius: 18px;
        padding: 0.2rem;
        max-width: 85%;
        position: relative;
      }

      .user-bubble {
        background: linear-gradient(135deg, #9c89b8, #f0a6ca);
        color: white;
        border-radius: 18px 18px 3px 18px;
        margin-right: 10px;
        box-shadow: 0 3px 10px rgba(240, 166, 202, 0.4);
      }

      .agent-bubble {
        background: rgba(184, 190, 221, 0.15);
        color: var(--luna-light);
        border-radius: 18px 18px 18px 3px;
        margin-left: 10px;
        border-left: 3px solid var(--luna-secondary);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
      }

      .system-bubble {
        background: rgba(0, 0, 0, 0.3);
        color: #aaa;
        font-style: italic;
        border-radius: 12px;
        margin-left: 10px;
        max-width: 90%;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
      }

      .message-content {
        padding: 0.75rem 1rem;
        overflow-wrap: break-word;
      }

      .message-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .user-avatar {
        background: linear-gradient(135deg, #9c89b8, #b8bedd);
        color: white;
        font-size: 1.2rem;
      }

      .agent-avatar {
        background: linear-gradient(135deg, #b8bedd, #f0a6ca);
        color: var(--luna-dark);
        font-size: 1.2rem;
      }

      .system-avatar {
        background: rgba(0, 0, 0, 0.3);
        color: #aaa;
        font-size: 1rem;
      }

      .thinking-message {
        justify-content: flex-start;
        padding-right: 15%;
        margin-bottom: 0.5rem;
      }

      .thinking-avatar {
        background: linear-gradient(135deg, #f0a6ca, #9c89b8);
        color: var(--luna-dark);
        font-size: 1rem;
      }

      .thinking-bubble {
        background: rgba(156, 137, 184, 0.1);
        color: var(--luna-light);
        border-radius: 18px 18px 18px 3px;
        margin-left: 10px;
        border-left: 3px solid #9c89b8;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
      }

      .thinking-header {
        padding: 0.5rem 1rem;
        border-bottom: 1px solid rgba(156, 137, 184, 0.2);
      }

      .thinking-content {
        padding: 0.75rem 1rem;
      }

      .thinking-text {
        font-style: italic;
        color: #ccc;
      }

      .message-timestamp {
        font-size: 0.7rem;
        opacity: 0.7;
        text-align: right;
        margin-top: -5px;
        padding-right: 0.5rem;
        padding-bottom: 0.2rem;
      }

      /* Animation des points de suspension pour l'indication de frappe */
      .typing-dots {
        display: flex;
        justify-content: center;
        padding: 0.5rem;
      }

      .typing-dots span {
        width: 8px;
        height: 8px;
        margin: 0 3px;
        background-color: var(--luna-light);
        border-radius: 50%;
        display: inline-block;
        animation: typing-dot 1.5s infinite ease-in-out;
      }

      .typing-dots span:nth-child(1) { animation-delay: 0s; }
      .typing-dots span:nth-child(2) { animation-delay: 0.3s; }
      .typing-dots span:nth-child(3) { animation-delay: 0.6s; }

      @keyframes typing-dot {
        0%, 60%, 100% { transform: translateY(0); }
        30% { transform: translateY(-5px); }
      }
    `;
    document.head.appendChild(style);
  }
}

function formatMessage(text) {
  // Convertir les liens en balises <a>
  text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');

  // Convertir les sauts de ligne en balises <br>
  text = text.replace(/\n/g, '<br>');

  // Conserver les paragraphes
  if (!text.startsWith('<p>')) {
    const paragraphs = text.split('\n\n');
    if (paragraphs.length > 1) {
      text = paragraphs.map(p => `<p>${p}</p>`).join('');
    }
  }

  return text;
}

function showTypingIndicator() {
  const container = document.getElementById('conversation-container');
  const typingDiv = document.createElement('div');
  typingDiv.id = 'typing-indicator';
  typingDiv.className = 'message agent-message';
  typingDiv.innerHTML = `
    <div class="message-content">
      <div class="typing-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  `;

  container.appendChild(typingDiv);
  container.scrollTop = container.scrollHeight;
}

// ==== GESTION DES MODÈLES ====

// Charger la liste des modèles disponibles
function loadAvailableModels() {
  // Utiliser fetch au lieu de jQuery Ajax
  console.log('Chargement des modèles disponibles...');

  // D'abord vérifier si Ollama est connecté
  if (!lunaState.isOllamaConnected) {
    console.warn('Impossible de charger les modèles car Ollama n\'est pas connecté');
    return;
  }

  // Récupérer directement les modèles depuis Ollama avec fetch
  fetch('/luna/models', {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Cache-Control': 'no-cache'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('Modèles reçus:', data);

    // Vérifier que la réponse contient les données attendues
    if (data.success && data.models && data.models.length > 0) {
      const modelSelect = document.getElementById('model-select');
      if (!modelSelect) {
        console.error("L'élément de sélection de modèle n'a pas été trouvé");
        return;
      }

      // Mettre à jour l'état
      lunaState.availableModels = data.models;
      lunaState.activeModel = data.activeModel || 'deepseek-r1:7b';
      lunaState.isModelConnected = true;

      // Vider le sélecteur de modèles
      modelSelect.innerHTML = '';

      // Ajouter les options pour chaque modèle disponible
      data.models.forEach(model => {
        const option = document.createElement('option');
        option.value = model;
        option.textContent = model;

        // Sélectionner le modèle actif
        if (model === lunaState.activeModel) {
          option.selected = true;
        }

        modelSelect.appendChild(option);
      });

      console.log(`${data.models.length} modèles chargés avec succès`);
      showNotification(`${data.models.length} modèles disponibles`, 'success');

      // Si l'interface affiche un message d'erreur, le masquer
      hideConnectionStatus();
    } else {
      console.error('Aucun modèle disponible ou format de réponse incorrect:', data);
      lunaState.isModelConnected = false;
      showConnectionStatus('Aucun modèle disponible dans Ollama. Vérifiez l\'installation des modèles.', 'error');

      // Tenter de recharger les modèles après un délai
      setTimeout(checkOllamaStatus, 3000);
    }
  })
  .catch(error => {
    console.error('Erreur lors du chargement des modèles:', error);
    lunaState.isModelConnected = false;
    showConnectionStatus(`Erreur lors du chargement des modèles: ${error.message}`, 'error');

    // Afficher un message explicatif dans le tableau de bord système
    updateSystemDashboard('Impossible de récupérer la liste des modèles. Vérifiez la connexion à Ollama.');
    updateSystemDashboard("Assurez-vous que le service Ollama est en cours d'exécution avec la commande 'ollama serve'.");
  });
}

// Installer un nouveau modèle
function installNewModel() {
  const modelName = prompt("Entrez le nom du modèle à installer depuis Ollama (exemple: llama3:8b):");
  if (!modelName) return;

  // Notifier l'utilisateur
  updateSystemDashboard(`Installation du modèle ${modelName} en cours. Cela peut prendre plusieurs minutes...`);

  $.ajax({
    url: '/luna/models/install',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({ model: modelName }),
    success: function(response) {
      if (response.success) {
        updateSystemDashboard(`Le modèle ${modelName} a été installé avec succès!`);
        // Recharger la liste des modèles
        loadAvailableModels();
      } else {
        updateSystemDashboard(`Erreur lors de l'installation du modèle: ${response.error || 'Erreur inconnue'}`);
      }
    },
    error: function(xhr, status, error) {
      updateSystemDashboard(`Erreur lors de l'installation du modèle ${modelName}: ${error}`);
      console.error('Échec de l\'installation:', status, error);
    }
  });
}

// Fonction pour installer un nouveau modèle
function installNewModel() {
  // Réinitialiser l'input à chaque ouverture
  $('#model-name-input').val('');

  // Afficher la modale
  const modalElement = $('#model-modal');
  modalElement.modal('show');

  // S'assurer que la touche Escape ferme la modale
  $(document).off('keydown.modelModal').on('keydown.modelModal', function(e) {
    if (e.key === 'Escape') {
      modalElement.modal('hide');
    }
  });

  // Confirmer l'installation au clic sur le bouton
  $('#confirm-install').off('click').on('click', function() {
    const modelName = $('#model-name-input').val().trim();

    if (modelName) {
      // Masquer la modale
      $('#model-modal').modal('hide');

      // Afficher un message de progression
      updateSystemDashboard(`Installation du modèle ${modelName} en cours... Cela peut prendre plusieurs minutes.`);

      // Appeler l'API pour installer le modèle
      $.ajax({
        url: '/models/install',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ model: modelName }),
        success: function(response) {
          if (response.success) {
            updateSystemDashboard(`Modèle ${modelName} installé avec succès.`);
            loadAvailableModels(); // Actualiser la liste des modèles
          } else {
            updateSystemDashboard(`Erreur lors de l'installation: ${response.error}`);
          }
        },
        error: function() {
          updateSystemDashboard('Erreur de communication avec le serveur');
        }
      });
    }
  });
}

// Fonction pour supprimer le modèle sélectionné
function removeSelectedModel() {
  const selectedModel = $('#model-select').val();

  if (selectedModel) {
    // Demander confirmation
    if (confirm(`Êtes-vous sûr de vouloir supprimer le modèle "${selectedModel}" ?
Cette action est irréversible.`)) {

      // Afficher un message de progression
      updateSystemDashboard(`Suppression du modèle ${selectedModel} en cours...`);

      // Appeler l'API pour supprimer le modèle
      $.ajax({
        url: '/models/remove',
        type: 'DELETE',
        contentType: 'application/json',
        data: JSON.stringify({ model: selectedModel }),
        success: function(response) {
          if (response.success) {
            updateSystemDashboard(`Modèle ${selectedModel} supprimé avec succès.`);
            loadAvailableModels(); // Actualiser la liste des modèles
          } else {
            updateSystemDashboard(`Erreur lors de la suppression: ${response.error}`);
          }
        },
        error: function(xhr) {
          let errorMsg = 'Erreur de communication avec le serveur';
          if (xhr.responseJSON && xhr.responseJSON.error) {
            errorMsg = xhr.responseJSON.error;
          }
          updateSystemDashboard(`Erreur: ${errorMsg}`);
        }
      });
    }
  } else {
    updateSystemDashboard('Aucun modèle sélectionné');
  }
}

// Sélectionner un modèle
function selectModel(modelName) {
  if (!modelName) return;

  $.ajax({
    url: '/luna/models/select',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({ model: modelName }),
    success: function(response) {
      if (response.success) {
        lunaState.activeModel = modelName;
        updateSystemDashboard(`Modèle changé pour ${modelName}`);
      } else {
        updateSystemDashboard(`Erreur lors du changement de modèle: ${response.error || 'Erreur inconnue'}`);
        // Recharger la liste pour avoir l'état correct
        loadAvailableModels();
      }
    },
    error: function() {
      updateSystemDashboard(`Erreur lors du changement de modèle pour ${modelName}.`);
    }
  });
}

// ==== INITIALISATION ====

// Attendre que le document soit chargé
// Fonction pour vérifier la disponibilité des périphériques (caméra, microphone)
function checkDevicesAvailability() {
  // Vérifier si la caméra et le microphone sont disponibles
  navigator.mediaDevices.enumerateDevices()
    .then(devices => {
      const hasCamera = devices.some(device => device.kind === 'videoinput');
      const hasMicrophone = devices.some(device => device.kind === 'audioinput');

      console.log('Périphériques disponibles - Caméra:', hasCamera, 'Microphone:', hasMicrophone);

      // Mettre à jour l'état des boutons en fonction de la disponibilité
      const cameraBtn = document.getElementById('camera-button');
      const micBtn = document.getElementById('mic-button');

      if (cameraBtn) cameraBtn.disabled = !hasCamera;
      if (micBtn) micBtn.disabled = !hasMicrophone;

      // Stocker l'information dans l'objet d'état
      lunaState.hasCamera = hasCamera;
      lunaState.hasMicrophone = hasMicrophone;
    })
    .catch(err => {
      console.error('Erreur lors de la vérification des périphériques:', err);
      updateSystemDashboard('Impossible de vérifier les périphériques audio/vidéo. Vérifiez les permissions de votre navigateur.');
    });
}

$(document).ready(function() {
  // Initialiser les styles pour les notifications
  initStyles();

  // Initialiser l'état de l'interface
  initializeState();

  // Vérifier la connexion au système
  checkSystemStatus();

  // Vérifier la connexion à Ollama
  checkOllamaStatus();

  // Configurer les gestionnaires d'événements
  setupEventHandlers();

  // Afficher message de bienvenue
  addMessage("Bienvenue sur l'interface Luna. Comment puis-je vous aider ?", 'agent');

  // Vérifier les modèles disponibles
  loadAvailableModels();

  // Vérifier l'état des périphériques (caméra, microphone)
  checkDevicesAvailability();

  // Message de bienvenue
  showNotification('Interface Luna prête à l\'utilisation', 'info');

  console.log('🌙 Interface Luna initialisée');

  // Vérifier la disponibilité des périphériques
  checkDevicesAvailability();

  // Fonction pour effacer les messages de l'interface uniquement
  function clearConversationMessages() {
    // Effacer tous les messages sauf le message de bienvenue initial
    $("#conversation-container").html(`
      <div class="message agent-message">
        <div class="message-avatar agent-avatar"><i class="bi bi-robot"></i></div>
        <div class="message-bubble agent-bubble">
          <div class="message-content">
            <p>Bienvenue dans l'interface Luna. Je suis Louna, votre assistant cognitif avancé.</p>
            <p>Comment puis-je vous aider aujourd'hui ?</p>
          </div>
          <div class="message-timestamp">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
        </div>
      </div>
    `);

    // Ajouter une confirmation
    updateSystemDashboard('Historique des messages effacé. La mémoire thermique reste intacte.');
  }

  // Charger la liste des modèles disponibles
  loadAvailableModels();

  // Initialiser les boutons d'interaction avec le système

  // Bouton d'activation système
  $('#toggleSystem').on('click', function() {
    const isSystemActive = $('#systemStatus .status-indicator').hasClass('status-active');
    updateSystemStatus(!isSystemActive);

    if (!isSystemActive) {
      addMessage("Système Luna activé. Bonjour, je suis Louna. Comment puis-je vous aider ?", 'agent');
    } else {
      updateSystemDashboard("Système Luna désactivé.");
    }

    // Envoyer l'état au serveur
    socket.emit('luna system toggle', { active: !isSystemActive });
  });

  // Bouton d'envoi de message
  $('#send-button').on('click', sendMessage);

  // Permettre l'envoi par la touche Entrée
  $('#user-input').on('keypress', function(e) {
    if (e.which === 13) {
      sendMessage();
    }
  });

  // Bouton de microphone
  $('#toggleListenBtn').on('click', function() {
    toggleMicrophone();
  });

  // Bouton de caméra
  $('#toggleVisionBtn').on('click', function() {
    toggleCamera();
  });

  // Bouton pour effacer l'historique des messages - DÉSACTIVÉ
  // Le bouton a été supprimé pour éviter l'effacement accidentel de l'historique
  // $('#clearMessagesBtn').on('click', function() {
  //   clearConversationMessages();
  // });

  // Bouton de synthèse vocale
  $('#testSpeechBtn').on('click', function() {
    toggleSpeech();
  });

  // === Gestion des modèles ===

  // Actualiser la liste des modèles
  $('#refresh-models').on('click', function() {
    loadAvailableModels();
  });

  // Installer un nouveau modèle
  $('#install-model').on('click', function() {
    installNewModel();
  });

  // Supprimer un modèle
  $('#remove-model').on('click', function() {
    removeSelectedModel();
  });

  // Changer de modèle
  $('#model-select').on('change', function() {
    const selectedModel = $(this).val();
    selectModel(selectedModel);
  });

  // Fonction pour envoyer un message
  function sendMessage() {
    const inputField = $('#user-input');
    const message = inputField.val().trim();

    if (message && lunaState.systemActive) {
      // Ajouter le message utilisateur
      addMessage(message, 'user');

      // Vider le champ de saisie
      inputField.val('');

      // Vérifier si l'utilisateur demande la date ou l'heure
      const lowerMessage = message.toLowerCase();
      if (
        lowerMessage.includes('quelle heure') ||
        lowerMessage.includes('heure est-il') ||
        lowerMessage.includes('heure actuelle') ||
        lowerMessage.includes('l\'heure') ||
        (lowerMessage.includes('heure') && lowerMessage.includes('?'))
      ) {
        // Répondre avec l'heure actuelle
        const dateTime = getCurrentDateTime();
        setTimeout(() => {
          addMessage(`Il est actuellement ${dateTime.time}.`, 'agent');
        }, 1000);
        return;
      } else if (
        lowerMessage.includes('quelle date') ||
        lowerMessage.includes('quel jour') ||
        lowerMessage.includes('date d\'aujourd\'hui') ||
        lowerMessage.includes('aujourd\'hui') ||
        (lowerMessage.includes('date') && lowerMessage.includes('?'))
      ) {
        // Répondre avec la date actuelle
        const dateTime = getCurrentDateTime();
        setTimeout(() => {
          addMessage(`Nous sommes le ${dateTime.date}.`, 'agent');
        }, 1000);
        return;
      } else if (
        lowerMessage.includes('date et heure') ||
        lowerMessage.includes('heure et date') ||
        lowerMessage.includes('jour et heure')
      ) {
        // Répondre avec la date et l'heure actuelles
        const dateTime = getCurrentDateTime();
        setTimeout(() => {
          addMessage(`Nous sommes le ${dateTime.date} et il est ${dateTime.time}.`, 'agent');
        }, 1000);
        return;
      }

      // Afficher l'indicateur de frappe
      showTypingIndicator();

      // Envoyer le message au serveur
      socket.emit('luna message', { message });
    }
  }

  // Activer le système au démarrage après un court délai
  setTimeout(() => {
    updateSystemStatus(true);
    const dateTime = getCurrentDateTime();
    addMessage(`Système Luna activé. Bonjour, je suis Vision Ultra, votre assistant cognitif créé par Jean Passave à Sainte-Anne, Guadeloupe (97180). Nous sommes le ${dateTime.date} et il est ${dateTime.time}. Comment puis-je vous aider ?`, 'agent');

    // Mises à jour périodiques simulées pour les métriques système
    setInterval(() => {
      // Métriques CPU et RAM
      $('#cpu-usage').text(`${Math.floor(20 + Math.random() * 15)}%`);
      $('#ram-usage').text(`${(2 + Math.random() * 0.8).toFixed(1)} GB`);
      $('#latency').text(`${Math.floor(20 + Math.random() * 10)}ms`);
      $('#thermal-index').text(`${Math.floor(68 + Math.random() * 8)}°C`);

      // Progression de la mémoire
      updateMemoryProgress();
    }, 5000);
  }, 1000);

  // Fonctions auxiliaires
  function updateMemoryProgress() {
    const memProgress = $('#memory-progress');
    const currentValue = parseInt(memProgress.attr('aria-valuenow'));
    const newValue = currentValue + (Math.random() > 0.5 ? 1 : -1);

    if (newValue >= 0 && newValue <= 100) {
      memProgress.css('width', `${newValue}%`);
      memProgress.attr('aria-valuenow', newValue);
      memProgress.text(`${newValue}%`);
    }
  }
});

// Ajouter un message à la conversation
function addMessage(message, sender) {
  // Ne pas afficher les messages système dans la conversation
  if (sender === 'system') {
    // Au lieu d'afficher dans la conversation, envoyer au tableau de bord des paramètres
    updateSystemDashboard(message);
    return;
  }

  const conversationContainer = document.getElementById('conversation-container');

  // Créer l'élément de message
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${sender}-message`;

  // Déterminer l'avatar en fonction de l'expéditeur
  let avatarIcon = '';
  if (sender === 'user') {
    avatarIcon = '<i class="bi bi-person"></i>';
  } else if (sender === 'agent') {
    avatarIcon = '<i class="bi bi-robot"></i>';
  }

  // Formater le contenu du message
  let formattedMessage = message;

  // Convertir les liens en éléments cliquables avec une meilleure détection
  formattedMessage = makeLinksClickable(formattedMessage);

  // Convertir les sauts de ligne en balises <br>
  formattedMessage = formattedMessage.replace(/\n/g, '<br>');

  // Ajouter le contenu HTML
  if (sender === 'agent') {
    messageDiv.innerHTML = `
      <div class="message-avatar ${sender}-avatar">${avatarIcon}</div>
      <div class="message-bubble ${sender}-bubble">
        <button class="copy-button" onclick="copyMessageContent(this)" title="Copier le message">
          <i class="bi bi-clipboard"></i>
        </button>
        <div class="message-content">${formattedMessage}</div>
      </div>
    `;
  } else {
    messageDiv.innerHTML = `
      <div class="message-avatar ${sender}-avatar">${avatarIcon}</div>
      <div class="message-bubble ${sender}-bubble">
        <div class="message-content">${formattedMessage}</div>
      </div>
    `;
  }

  // Fonction pour rendre les liens cliquables avec une meilleure détection
  function makeLinksClickable(text) {
    // Regex pour détecter les URLs
    const urlRegex = /(https?:\/\/[^\s]+)/g;

    // Remplacer les URLs par des liens cliquables
    return text.replace(urlRegex, function(url) {
      // Nettoyer l'URL si elle se termine par une ponctuation
      let cleanUrl = url;
      const lastChar = url.charAt(url.length - 1);
      if (['.', ',', ';', ':', '!', '?', ')'].includes(lastChar)) {
        cleanUrl = url.substring(0, url.length - 1);
      }

      return `<a href="${cleanUrl}" target="_blank" rel="noopener noreferrer">${url}</a>`;
    });
  }

  // Ajouter le message au conteneur
  conversationContainer.appendChild(messageDiv);

  // Faire défiler vers le bas
  conversationContainer.scrollTop = conversationContainer.scrollHeight;

  // Si c'est une réponse de l'agent, réactiver l'interface
  if (sender === 'agent') {
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');

    if (userInput) userInput.disabled = false;
    if (sendButton) sendButton.disabled = false;

    // Mettre à jour le statut de l'agent
    updateAgentStatus(false);

    // Réinitialiser l'état d'attente
    window.awaitingResponse = false;
  }
}

// Ajouter une réflexion (thinking) à la conversation
function addThinking(thinking) {
  const conversationContainer = document.getElementById('conversation-container');

  // Créer l'élément de réflexion
  const thinkingDiv = document.createElement('div');
  thinkingDiv.className = 'message thinking-message';

  // Formater le contenu de la réflexion
  let formattedThinking = thinking;

  // Convertir les sauts de ligne en balises <br>
  formattedThinking = formattedThinking.replace(/\n/g, '<br>');

  // Ajouter le contenu HTML avec un bouton pour afficher/masquer
  thinkingDiv.innerHTML = `
    <div class="message-avatar thinking-avatar"><i class="bi bi-lightbulb"></i></div>
    <div class="message-bubble thinking-bubble">
      <div class="thinking-header">
        <button class="btn btn-sm btn-outline-secondary toggle-thinking">
          <i class="bi bi-chevron-down"></i> Réflexion
        </button>
      </div>
      <div class="thinking-content" style="display: none;">
        <div class="thinking-text">${formattedThinking}</div>
      </div>
    </div>
  `;

  // Ajouter la réflexion au conteneur
  conversationContainer.appendChild(thinkingDiv);

  // Ajouter un gestionnaire d'événement pour le bouton d'affichage/masquage
  const toggleButton = thinkingDiv.querySelector('.toggle-thinking');
  const thinkingContent = thinkingDiv.querySelector('.thinking-content');

  toggleButton.addEventListener('click', function() {
    const isVisible = thinkingContent.style.display !== 'none';
    thinkingContent.style.display = isVisible ? 'none' : 'block';
    toggleButton.querySelector('i').className = isVisible ? 'bi bi-chevron-down' : 'bi bi-chevron-up';

    // Faire défiler vers le bas si la réflexion est affichée
    if (!isVisible) {
      conversationContainer.scrollTop = conversationContainer.scrollHeight;
    }
  });

  // Faire défiler vers le bas
  conversationContainer.scrollTop = conversationContainer.scrollHeight;
}

// Fonction pour charger les prompts favoris
function loadFavoritePrompts() {
  console.log('Chargement des prompts favoris...');

  // Émettre une demande au serveur pour charger les prompts
  socket.emit('load prompts');

  // Écouter la réponse du serveur
  socket.on('prompts loaded', (data) => {
    if (data.success) {
      // Filtrer les prompts favoris
      const favoritePrompts = data.prompts.filter(p => p.favorite);
      const container = $('#quick-prompts-container');
      const noPromptsMessage = $('#no-quick-prompts');

      console.log(`${favoritePrompts.length} prompts favoris chargés`);

      if (favoritePrompts.length === 0) {
        // Afficher le message "aucun prompt favori"
        noPromptsMessage.show();
      } else {
        // Masquer le message "aucun prompt favori"
        noPromptsMessage.hide();

        // Vider le conteneur
        container.find('.quick-prompt-item').remove();

        // Ajouter chaque prompt favori
        favoritePrompts.forEach(prompt => {
          const promptItem = $(`
            <div class="quick-prompt-item mb-2">
              <button class="btn btn-sm btn-luna-outline w-100 text-start" data-prompt="${encodeURIComponent(prompt.content)}">
                <i class="bi bi-lightning me-2"></i> ${prompt.title}
              </button>
            </div>
          `);

          // Ajouter le gestionnaire d'événement pour insérer le prompt dans le champ de saisie
          promptItem.find('button').on('click', function() {
            const promptContent = decodeURIComponent($(this).data('prompt'));
            $('#user-input').val(promptContent);
            $('#prompt-selector').slideUp(300);
            $('#user-input').focus();
          });

          container.append(promptItem);
        });
      }
    } else {
      console.error('Erreur lors du chargement des prompts:', data.error);
      updateSystemDashboard('Erreur lors du chargement des prompts favoris');
    }
  });
}

// Traduire les mots anglais courants en français
function translateCommonWords(text) {
  const translations = {
    'I': 'Je',
    'need': 'dois',
    'to': 'à',
    'answer': 'répondre',
    'the': 'la',
    'question': 'question',
    'about': 'concernant',
    'what': 'quelle',
    'is': 'est',
    'capital': 'capitale',
    'of': 'de',
    'Italy': 'l\'Italie',
    'The': 'La',
    'capital': 'capitale',
    'Rome': 'Rome',
    'So': 'Donc',
    'will': 'vais',
    'provide': 'fournir',
    'this': 'cette',
    'information': 'information',
    'user': 'utilisateur',
    'asking': 'demande',
    'for': 'pour',
    'Okay': 'D\'accord',
    'Let': 'Laissez',
    'me': 'moi',
    'think': 'réfléchir',
    'about': 'à propos',
    'this': 'ceci',
    'First': 'Premièrement',
    'Second': 'Deuxièmement',
    'Third': 'Troisièmement',
    'Finally': 'Finalement',
    'In': 'En',
    'conclusion': 'conclusion',
    'summary': 'résumé'
  };

  // Remplacer les mots anglais par leurs équivalents français
  let translatedText = text;
  for (const [english, french] of Object.entries(translations)) {
    // Utiliser une expression régulière pour remplacer uniquement les mots entiers
    const regex = new RegExp(`\\b${english}\\b`, 'g');
    translatedText = translatedText.replace(regex, french);
  }

  return translatedText;
}