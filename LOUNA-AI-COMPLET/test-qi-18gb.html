<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Test QI LOUNA-AI - Modèle 18GB</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }

        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .model-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 255, 0.2);
            text-align: center;
        }

        .info-card h3 {
            color: #00ffff;
            margin-bottom: 10px;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(0, 255, 255, 0.3);
            margin-bottom: 20px;
        }

        .test-question {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #00ffff;
        }

        .test-question h4 {
            color: #00ffff;
            margin-bottom: 15px;
        }

        .test-button {
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 255, 255, 0.3);
        }

        .result-area {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 200px;
        }

        .loading {
            text-align: center;
            color: #00ffff;
            font-style: italic;
        }

        .qi-score {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ffff;
            color: #00ffff;
            padding: 12px 18px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-btn:hover {
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            color: #ffffff;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="nav-buttons">
        <button class="nav-btn" onclick="window.location.href='interface-louna-complete.html'">🏠 Accueil</button>
    </div>

    <div class="container">
        <div class="header">
            <h1>🧠 Test QI LOUNA-AI</h1>
            <p>Évaluation des capacités intellectuelles du modèle 18GB</p>
        </div>

        <div class="model-info">
            <div class="info-card">
                <h3>🤖 Modèle</h3>
                <p id="model-name">Llama2 13B (18GB)</p>
            </div>
            <div class="info-card">
                <h3>🧠 QI Estimé</h3>
                <p id="qi-estimate">En cours d'évaluation...</p>
            </div>
            <div class="info-card">
                <h3>⚡ Statut</h3>
                <p id="model-status">Connexion...</p>
            </div>
            <div class="info-card">
                <h3>🎯 Précision</h3>
                <p id="accuracy">À déterminer</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Tests de QI Avancés</h2>
            
            <div class="test-question">
                <h4>Test 1: Raisonnement Logique Complexe</h4>
                <p>Problème de trains avec calculs de vitesse et temps de croisement</p>
                <button class="test-button" onclick="testRaisonnementLogique()">🚂 Lancer Test</button>
            </div>

            <div class="test-question">
                <h4>Test 2: Analyse Mathématique Avancée</h4>
                <p>Résolution d'équations différentielles et optimisation</p>
                <button class="test-button" onclick="testMathematiques()">📊 Lancer Test</button>
            </div>

            <div class="test-question">
                <h4>Test 3: Créativité et Innovation</h4>
                <p>Génération d'idées créatives et solutions innovantes</p>
                <button class="test-button" onclick="testCreativite()">🎨 Lancer Test</button>
            </div>

            <div class="test-question">
                <h4>Test 4: Compréhension Contextuelle</h4>
                <p>Analyse de textes complexes et nuances linguistiques</p>
                <button class="test-button" onclick="testComprehension()">📚 Lancer Test</button>
            </div>

            <div class="test-question">
                <h4>Test 5: Raisonnement Spatial</h4>
                <p>Visualisation 3D et géométrie complexe</p>
                <button class="test-button" onclick="testSpatial()">🔷 Lancer Test</button>
            </div>

            <button class="test-button" onclick="testComplet()" style="font-size: 1.3em; padding: 20px 40px;">
                🧠 LANCER TEST COMPLET DE QI
            </button>
        </div>

        <div class="result-area" id="resultArea">
            <h3>📊 Résultats des Tests</h3>
            <p>Cliquez sur un test pour commencer l'évaluation...</p>
        </div>

        <div class="qi-score" id="qiScore" style="display: none;">
            QI FINAL: <span id="finalScore">---</span>
        </div>
    </div>

    <script>
        let testResults = {};
        let currentTest = 0;

        // Vérifier le statut du modèle au chargement
        window.onload = function() {
            verifierStatutModele();
        };

        async function verifierStatutModele() {
            try {
                const response = await fetch('/api/agent-message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'Test de connexion' })
                });
                
                if (response.ok) {
                    document.getElementById('model-status').textContent = '✅ Connecté';
                    document.getElementById('model-status').style.color = '#00ff00';
                } else {
                    document.getElementById('model-status').textContent = '❌ Déconnecté';
                    document.getElementById('model-status').style.color = '#ff0000';
                }
            } catch (error) {
                document.getElementById('model-status').textContent = '⚠️ Erreur';
                document.getElementById('model-status').style.color = '#ffff00';
            }
        }

        async function testRaisonnementLogique() {
            const question = `Test de QI - Raisonnement Logique:

Un train part de Paris à 14h30 à 320 km/h vers Lyon (distance 462 km).
Un autre train part de Lyon à 15h15 à 280 km/h vers Paris.
À quelle heure et à quelle distance de Paris vont-ils se croiser ?

Explique ton raisonnement étape par étape et donne la solution précise.`;

            await executerTest('Raisonnement Logique', question, 1);
        }

        async function testMathematiques() {
            const question = `Test de QI - Mathématiques Avancées:

Résous cette équation différentielle: dy/dx = 2x + 3y
Avec la condition initiale y(0) = 1

Puis trouve le maximum de la fonction f(x) = x³ - 6x² + 9x + 2 sur l'intervalle [0, 5].

Explique chaque étape de résolution.`;

            await executerTest('Mathématiques', question, 2);
        }

        async function testCreativite() {
            const question = `Test de QI - Créativité:

Imagine 5 utilisations innovantes et créatives pour un objet du quotidien : une cuillère.
Les solutions doivent être originales, pratiques et détaillées.

Puis crée une courte histoire de science-fiction (100 mots) incluant ces 5 utilisations.`;

            await executerTest('Créativité', question, 3);
        }

        async function testComprehension() {
            const question = `Test de QI - Compréhension Contextuelle:

Analyse ce texte et explique les nuances, sous-entendus et implications:

"Bien sûr, c'est une excellente idée", dit Marie en regardant par la fenêtre, ses doigts tapotant nerveusement sur la table. "Je suis certaine que tout se passera parfaitement bien."

Que révèle ce passage sur l'état d'esprit de Marie ? Justifie ton analyse.`;

            await executerTest('Compréhension', question, 4);
        }

        async function testSpatial() {
            const question = `Test de QI - Raisonnement Spatial:

Un cube de 4x4x4 est peint en rouge sur toutes ses faces externes.
Puis il est découpé en petits cubes de 1x1x1.

Combien de petits cubes ont:
1. 3 faces rouges ?
2. 2 faces rouges ?
3. 1 face rouge ?
4. 0 face rouge ?

Explique ton raisonnement spatial.`;

            await executerTest('Raisonnement Spatial', question, 5);
        }

        async function executerTest(nomTest, question, numeroTest) {
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = `
                <h3>🧠 Test en cours: ${nomTest}</h3>
                <div class="loading">⏳ LOUNA-AI réfléchit avec le modèle 18GB...</div>
                <div style="margin-top: 15px; font-style: italic; color: #888;">
                    Question: ${question.substring(0, 100)}...
                </div>
            `;

            try {
                const response = await fetch('/api/agent-message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: question })
                });

                const data = await response.json();
                
                if (data.success) {
                    const score = evaluerReponse(data.reponse, numeroTest);
                    testResults[nomTest] = score;
                    
                    resultArea.innerHTML = `
                        <h3>✅ Test ${nomTest} Terminé</h3>
                        <div style="background: rgba(0,255,0,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <strong>Score: ${score}/100</strong>
                        </div>
                        <div style="background: rgba(255,255,255,0.05); padding: 15px; border-radius: 8px; max-height: 300px; overflow-y: auto;">
                            <strong>Réponse de LOUNA-AI:</strong><br>
                            ${data.reponse.replace(/\n/g, '<br>')}
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: #888;">
                            Modèle utilisé: ${data.modele || 'Inconnu'} | Agent: ${data.agent || 'Inconnu'}
                        </div>
                    `;
                    
                    calculerQIFinal();
                } else {
                    resultArea.innerHTML = `
                        <h3>❌ Erreur Test ${nomTest}</h3>
                        <p style="color: #ff4444;">${data.error || 'Erreur inconnue'}</p>
                    `;
                }
            } catch (error) {
                resultArea.innerHTML = `
                    <h3>❌ Erreur de Connexion</h3>
                    <p style="color: #ff4444;">Impossible de communiquer avec LOUNA-AI: ${error.message}</p>
                `;
            }
        }

        function evaluerReponse(reponse, numeroTest) {
            // Évaluation basique basée sur la longueur, la structure et le contenu
            let score = 0;
            
            // Critères généraux
            if (reponse.length > 100) score += 20;
            if (reponse.length > 300) score += 20;
            if (reponse.includes('étape') || reponse.includes('donc') || reponse.includes('ainsi')) score += 15;
            if (reponse.match(/\d+/g)) score += 15; // Contient des chiffres
            
            // Critères spécifiques par test
            switch(numeroTest) {
                case 1: // Raisonnement logique
                    if (reponse.includes('15h') || reponse.includes('16h')) score += 15;
                    if (reponse.includes('km') && reponse.includes('vitesse')) score += 15;
                    break;
                case 2: // Mathématiques
                    if (reponse.includes('dy/dx') || reponse.includes('dérivée')) score += 15;
                    if (reponse.includes('maximum') || reponse.includes('f\'(x)')) score += 15;
                    break;
                case 3: // Créativité
                    if (reponse.split('1.').length > 1 || reponse.split('-').length > 3) score += 15;
                    if (reponse.includes('histoire') || reponse.includes('science-fiction')) score += 15;
                    break;
                case 4: // Compréhension
                    if (reponse.includes('nerveux') || reponse.includes('inquiet')) score += 15;
                    if (reponse.includes('ironie') || reponse.includes('sarcasme')) score += 15;
                    break;
                case 5: // Spatial
                    if (reponse.includes('8') || reponse.includes('coin')) score += 15;
                    if (reponse.includes('arête') || reponse.includes('face')) score += 15;
                    break;
            }
            
            return Math.min(score, 100);
        }

        function calculerQIFinal() {
            const scores = Object.values(testResults);
            if (scores.length > 0) {
                const moyenne = scores.reduce((a, b) => a + b, 0) / scores.length;
                const qiFinal = Math.round(80 + (moyenne * 1.2)); // QI entre 80 et 200
                
                document.getElementById('qi-estimate').textContent = qiFinal;
                document.getElementById('finalScore').textContent = qiFinal;
                document.getElementById('qiScore').style.display = 'block';
                
                // Mise à jour de la précision
                if (qiFinal > 150) {
                    document.getElementById('accuracy').textContent = 'Excellente';
                    document.getElementById('accuracy').style.color = '#00ff00';
                } else if (qiFinal > 120) {
                    document.getElementById('accuracy').textContent = 'Bonne';
                    document.getElementById('accuracy').style.color = '#ffff00';
                } else {
                    document.getElementById('accuracy').textContent = 'Moyenne';
                    document.getElementById('accuracy').style.color = '#ff8800';
                }
            }
        }

        async function testComplet() {
            document.getElementById('resultArea').innerHTML = `
                <h3>🚀 LANCEMENT DU TEST COMPLET DE QI</h3>
                <div class="loading">⏳ Exécution de tous les tests en séquence...</div>
            `;
            
            await testRaisonnementLogique();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await testMathematiques();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await testCreativite();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await testComprehension();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await testSpatial();
            
            document.getElementById('resultArea').innerHTML += `
                <div style="margin-top: 20px; padding: 20px; background: linear-gradient(45deg, rgba(0,255,255,0.1), rgba(255,0,255,0.1)); border-radius: 10px;">
                    <h3>🎉 TEST COMPLET TERMINÉ !</h3>
                    <p>Tous les tests ont été exécutés avec le modèle 18GB.</p>
                </div>
            `;
        }
    </script>
</body>
</html>
