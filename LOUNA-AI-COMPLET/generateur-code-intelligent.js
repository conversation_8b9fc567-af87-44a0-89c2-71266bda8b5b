/**
 * 🚀 GÉNÉRATEUR DE CODE INTELLIGENT POUR LOUNA-AI
 * 
 * Module avancé de génération de code avec templates intelligents
 * et adaptation automatique selon le contexte
 */

class GenerateurCodeIntelligent {
    constructor() {
        this.templates = new Map();
        this.langages = new Set(['javascript', 'python', 'html', 'css', 'sql', 'bash']);
        this.niveaux = new Set(['debutant', 'intermediaire', 'avance', 'expert']);
        this.initTemplates();
    }

    initTemplates() {
        // Templates JavaScript
        this.templates.set('javascript_api', {
            nom: 'API REST avec Express.js',
            niveau: 'intermediaire',
            code: `const express = require('express');
const cors = require('cors');
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.get('/api/users', async (req, res) => {
    try {
        // Logique métier ici
        const users = await getUsersFromDB();
        res.json({ success: true, data: users });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/users', async (req, res) => {
    try {
        const newUser = await createUser(req.body);
        res.status(201).json({ success: true, data: newUser });
    } catch (error) {
        res.status(400).json({ success: false, error: error.message });
    }
});

// Démarrage serveur
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(\`🚀 Serveur démarré sur le port \${PORT}\`);
});`
        });

        this.templates.set('python_ml', {
            nom: 'Machine Learning avec scikit-learn',
            niveau: 'avance',
            code: `import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import matplotlib.pyplot as plt
import seaborn as sns

class AnalyseurML:
    def __init__(self):
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.is_trained = False
    
    def charger_donnees(self, fichier_csv):
        """Charge et prépare les données"""
        self.df = pd.read_csv(fichier_csv)
        print(f"📊 Données chargées: {self.df.shape}")
        return self.df.head()
    
    def preparer_donnees(self, colonne_cible):
        """Prépare les données pour l'entraînement"""
        X = self.df.drop(colonne_cible, axis=1)
        y = self.df[colonne_cible]
        
        # Division train/test
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        print(f"✅ Données préparées: {self.X_train.shape[0]} échantillons d'entraînement")
    
    def entrainer(self):
        """Entraîne le modèle"""
        self.model.fit(self.X_train, self.y_train)
        self.is_trained = True
        print("🎯 Modèle entraîné avec succès!")
    
    def evaluer(self):
        """Évalue les performances"""
        if not self.is_trained:
            raise ValueError("Le modèle doit être entraîné d'abord")
        
        predictions = self.model.predict(self.X_test)
        accuracy = accuracy_score(self.y_test, predictions)
        
        print(f"📈 Précision: {accuracy:.2%}")
        print("\\n📋 Rapport détaillé:")
        print(classification_report(self.y_test, predictions))
        
        return accuracy

# Utilisation
if __name__ == "__main__":
    analyseur = AnalyseurML()
    # analyseur.charger_donnees('donnees.csv')
    # analyseur.preparer_donnees('target')
    # analyseur.entrainer()
    # analyseur.evaluer()`
        });

        this.templates.set('html_moderne', {
            nom: 'Page Web Moderne avec CSS Grid',
            niveau: 'intermediaire',
            code: `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Moderne</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            display: grid;
            grid-template-areas: 
                "header header"
                "sidebar main"
                "footer footer";
            grid-template-rows: auto 1fr auto;
            grid-template-columns: 250px 1fr;
            min-height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            grid-area: header;
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar {
            grid-area: sidebar;
            background: #34495e;
            color: white;
            padding: 2rem 1rem;
        }

        .main {
            grid-area: main;
            padding: 2rem;
            background: #ecf0f1;
        }

        .footer {
            grid-area: footer;
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 1rem;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        @media (max-width: 768px) {
            .container {
                grid-template-areas: 
                    "header"
                    "main"
                    "footer";
                grid-template-columns: 1fr;
            }
            .sidebar { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 Mon Application</h1>
            <nav>
                <button>Connexion</button>
            </nav>
        </header>
        
        <aside class="sidebar">
            <h3>Navigation</h3>
            <ul>
                <li>Accueil</li>
                <li>Projets</li>
                <li>Contact</li>
            </ul>
        </aside>
        
        <main class="main">
            <div class="card">
                <h2>Bienvenue</h2>
                <p>Cette application utilise CSS Grid pour un layout moderne et responsive.</p>
            </div>
            
            <div class="card">
                <h3>Fonctionnalités</h3>
                <ul>
                    <li>✅ Design responsive</li>
                    <li>✅ CSS Grid moderne</li>
                    <li>✅ Animations fluides</li>
                </ul>
            </div>
        </main>
        
        <footer class="footer">
            <p>&copy; 2024 - Application créée avec LOUNA-AI</p>
        </footer>
    </div>
</body>
</html>`
        });
    }

    genererCode(type, langage, niveau = 'intermediaire', options = {}) {
        const cle = `${langage}_${type}`;
        const template = this.templates.get(cle);
        
        if (!template) {
            return {
                success: false,
                error: `Template non trouvé pour ${langage} - ${type}`,
                suggestions: this.getSuggestions(langage)
            };
        }

        // Adaptation selon le niveau
        let codeAdapte = this.adapterNiveau(template.code, niveau);
        
        // Application des options personnalisées
        if (options.nom_projet) {
            codeAdapte = codeAdapte.replace(/Mon Application|Application/g, options.nom_projet);
        }

        return {
            success: true,
            nom: template.nom,
            niveau: template.niveau,
            code: codeAdapte,
            langage: langage,
            instructions: this.genererInstructions(langage, type),
            fichiers_requis: this.getFichiersRequis(langage, type)
        };
    }

    adapterNiveau(code, niveau) {
        switch (niveau) {
            case 'debutant':
                // Ajouter plus de commentaires
                return code.replace(/\/\/ /g, '// 📝 EXPLICATION: ');
            case 'expert':
                // Retirer les commentaires basiques
                return code.replace(/\/\/ .+\n/g, '');
            default:
                return code;
        }
    }

    genererInstructions(langage, type) {
        const instructions = {
            javascript: {
                api: [
                    "1. Installer les dépendances: npm install express cors",
                    "2. Créer le fichier server.js avec le code généré",
                    "3. Lancer avec: node server.js",
                    "4. Tester l'API sur http://localhost:3000"
                ]
            },
            python: {
                ml: [
                    "1. Installer: pip install pandas scikit-learn matplotlib seaborn",
                    "2. Préparer vos données au format CSV",
                    "3. Exécuter le script: python analyseur_ml.py",
                    "4. Analyser les résultats affichés"
                ]
            }
        };

        return instructions[langage]?.[type] || ["Instructions génériques disponibles"];
    }

    getFichiersRequis(langage, type) {
        const fichiers = {
            javascript: {
                api: ['package.json', 'server.js', '.env']
            },
            python: {
                ml: ['requirements.txt', 'analyseur_ml.py', 'donnees.csv']
            },
            html: {
                moderne: ['index.html', 'style.css', 'script.js']
            }
        };

        return fichiers[langage]?.[type] || [];
    }

    getSuggestions(langage) {
        const suggestions = Array.from(this.templates.keys())
            .filter(cle => cle.startsWith(langage))
            .map(cle => cle.replace(`${langage}_`, ''));
        
        return suggestions.length > 0 ? suggestions : ['Aucune suggestion disponible'];
    }

    listerTemplates() {
        const liste = [];
        for (const [cle, template] of this.templates) {
            const [langage, type] = cle.split('_');
            liste.push({
                langage,
                type,
                nom: template.nom,
                niveau: template.niveau
            });
        }
        return liste;
    }
}

module.exports = { GenerateurCodeIntelligent };
