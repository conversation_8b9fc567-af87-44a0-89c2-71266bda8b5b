<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 CERVEAU THERMIQUE VIVANT 3D - LOUNA-AI</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.9/dat.gui.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            color: #00ff88;
            overflow: hidden;
            height: 100vh;
        }

        #container {
            display: grid;
            grid-template-columns: 1fr 400px;
            grid-template-rows: 60px 1fr 200px;
            height: 100vh;
            gap: 2px;
        }

        #header {
            grid-column: 1 / -1;
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            backdrop-filter: blur(10px);
        }

        #title {
            font-size: 24px;
            font-weight: bold;
            text-shadow: 0 0 10px #00ff88;
        }

        #status {
            display: flex;
            gap: 20px;
            font-size: 14px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pulse {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ff4444;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        #scene3d {
            background: radial-gradient(circle at center, #001122, #000000);
            border: 1px solid #00ff88;
            position: relative;
            overflow: hidden;
        }

        #controls {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff88;
            padding: 15px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        #metrics {
            grid-column: 1 / -1;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff88;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            padding: 10px;
            backdrop-filter: blur(10px);
        }

        .metric-card {
            background: rgba(0, 255, 136, 0.05);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }

        .metric-title {
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #00ff88;
            text-shadow: 0 0 5px #00ff88;
        }

        .metric-chart {
            height: 40px;
            margin-top: 5px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 5px;
        }

        .control-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #00ff88;
        }

        .control-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .control-value {
            color: #ffaa00;
            font-weight: bold;
        }

        .zone-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
        }

        .zone-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #fff;
        }

        .zone-name {
            flex: 1;
            font-size: 11px;
        }

        .zone-temp {
            color: #ff6666;
            font-weight: bold;
        }

        .zone-activity {
            color: #66ff66;
            font-weight: bold;
        }

        #neuron-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff88;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            backdrop-filter: blur(10px);
        }

        .info-hidden {
            display: none;
        }

        .synapse-flow {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ffff;
            border-radius: 50%;
            box-shadow: 0 0 4px #00ffff;
            animation: synapseFlow 0.5s linear;
        }

        @keyframes synapseFlow {
            0% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.5); }
        }

        .temperature-gradient {
            position: absolute;
            bottom: 10px;
            right: 10px;
            width: 20px;
            height: 200px;
            background: linear-gradient(to top,
                #0066ff 0%,   /* Froid */
                #00ff00 25%,  /* Tiède */
                #ffff00 50%,  /* Chaud */
                #ff6600 75%,  /* Très chaud */
                #ff0000 100%  /* Brûlant */
            );
            border: 1px solid #fff;
            border-radius: 10px;
        }

        .temp-labels {
            position: absolute;
            bottom: 10px;
            right: 45px;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 10px;
            color: #ccc;
        }

        .activity-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            border: 2px solid #00ff88;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 255, 136, 0.1);
            backdrop-filter: blur(5px);
            animation: activityPulse 2s infinite;
        }

        @keyframes activityPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
            50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
        }

        .data-stream {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 200px;
            height: 150px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff88;
            border-radius: 5px;
            padding: 10px;
            font-size: 10px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        .stream-line {
            margin-bottom: 2px;
            opacity: 0.8;
        }

        .stream-timestamp {
            color: #666;
        }

        .stream-event {
            color: #00ff88;
        }

        .stream-value {
            color: #ffaa00;
        }

        /* MODE DÉMONSTRATION CSS SI THREE.JS ÉCHOUE */
        .demo-brain {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
        }

        .demo-zone {
            position: absolute;
            border-radius: 50%;
            border: 2px solid;
            animation: demoPulse 2s infinite ease-in-out;
        }

        .demo-zone-1 {
            width: 80px;
            height: 80px;
            top: 20px;
            left: 110px;
            background: rgba(255, 68, 68, 0.3);
            border-color: #ff4444;
            animation-delay: 0s;
        }

        .demo-zone-2 {
            width: 60px;
            height: 60px;
            top: 80px;
            left: 50px;
            background: rgba(68, 255, 68, 0.3);
            border-color: #44ff44;
            animation-delay: 0.3s;
        }

        .demo-zone-3 {
            width: 50px;
            height: 50px;
            top: 120px;
            left: 200px;
            background: rgba(68, 68, 255, 0.3);
            border-color: #4444ff;
            animation-delay: 0.6s;
        }

        .demo-zone-4 {
            width: 70px;
            height: 70px;
            top: 180px;
            left: 80px;
            background: rgba(255, 255, 68, 0.3);
            border-color: #ffff44;
            animation-delay: 0.9s;
        }

        .demo-zone-5 {
            width: 65px;
            height: 65px;
            top: 160px;
            left: 160px;
            background: rgba(255, 68, 255, 0.3);
            border-color: #ff44ff;
            animation-delay: 1.2s;
        }

        .demo-zone-6 {
            width: 55px;
            height: 55px;
            top: 220px;
            left: 120px;
            background: rgba(68, 255, 255, 0.3);
            border-color: #44ffff;
            animation-delay: 1.5s;
        }

        @keyframes demoPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.6;
                box-shadow: 0 0 10px currentColor;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
                box-shadow: 0 0 30px currentColor;
            }
        }

        .demo-neuron {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ff88;
            border-radius: 50%;
            animation: demoNeuron 3s infinite linear;
            box-shadow: 0 0 5px #00ff88;
        }

        @keyframes demoNeuron {
            0% { opacity: 0.3; }
            50% { opacity: 1; transform: scale(1.5); }
            100% { opacity: 0.3; }
        }

        .demo-connection {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, #0088ff, transparent);
            animation: demoConnection 2s infinite ease-in-out;
        }

        @keyframes demoConnection {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="header">
            <div id="title">🧠 CERVEAU THERMIQUE VIVANT 3D - LOUNA-AI</div>
            <div id="status">
                <div class="status-item">
                    <div class="pulse"></div>
                    <span>SYSTÈME VIVANT</span>
                </div>
                <div class="status-item">
                    🌡️ <span id="cpu-temp">--°C</span>
                </div>
                <div class="status-item">
                    🧠 <span id="neuron-count">--</span> neurones
                </div>
                <div class="status-item">
                    ⚡ <span id="activity-rate">--</span> Hz
                </div>
            </div>
        </div>

        <div id="scene3d">
            <div id="neuron-info" class="info-hidden">
                <div><strong>Neurone #<span id="neuron-id">--</span></strong></div>
                <div>Zone: <span id="neuron-zone">--</span></div>
                <div>Potentiel: <span id="neuron-potential">--</span>mV</div>
                <div>Température: <span id="neuron-temp">--</span>°C</div>
                <div>Activations: <span id="neuron-activations">--</span></div>
                <div>Synapses: <span id="neuron-synapses">--</span></div>
            </div>

            <div class="temperature-gradient"></div>
            <div class="temp-labels">
                <div>80°C</div>
                <div>70°C</div>
                <div>60°C</div>
                <div>50°C</div>
                <div>40°C</div>
            </div>

            <div class="activity-indicator">
                <div style="text-align: center;">
                    <div style="font-size: 12px;">ACTIVITÉ</div>
                    <div style="font-size: 18px; font-weight: bold;" id="activity-level">--</div>
                </div>
            </div>

            <div class="data-stream" id="data-stream">
                <div style="font-weight: bold; margin-bottom: 5px;">🔄 FLUX DONNÉES TEMPS RÉEL</div>
            </div>
        </div>

        <div id="controls">
            <div class="control-group">
                <div class="control-title">🎯 ATTENTION</div>
                <div class="control-item">
                    <span>Focus:</span>
                    <span class="control-value" id="focus-target">--</span>
                </div>
                <div class="control-item">
                    <span>Intensité:</span>
                    <span class="control-value" id="focus-intensity">--</span>
                </div>
                <div class="control-item">
                    <span>Temps réaction:</span>
                    <span class="control-value" id="reaction-time">--</span>
                </div>
                <div class="control-item">
                    <span>Fatigue:</span>
                    <span class="control-value" id="fatigue-level">--</span>
                </div>
            </div>

            <div class="control-group">
                <div class="control-title">🧠 ZONES CÉRÉBRALES</div>
                <div class="zone-indicator">
                    <div class="zone-color" style="background: #ff4444;" id="zone-color-0"></div>
                    <div class="zone-name">Cortex Préfrontal</div>
                    <div class="zone-temp" id="zone-temp-0">--°C</div>
                    <div class="zone-activity" id="zone-activity-0">--</div>
                </div>
                <div class="zone-indicator">
                    <div class="zone-color" style="background: #44ff44;" id="zone-color-1"></div>
                    <div class="zone-name">Hippocampe</div>
                    <div class="zone-temp" id="zone-temp-1">--°C</div>
                    <div class="zone-activity" id="zone-activity-1">--</div>
                </div>
                <div class="zone-indicator">
                    <div class="zone-color" style="background: #4444ff;" id="zone-color-2"></div>
                    <div class="zone-name">Amygdale</div>
                    <div class="zone-temp" id="zone-temp-2">--°C</div>
                    <div class="zone-activity" id="zone-activity-2">--</div>
                </div>
                <div class="zone-indicator">
                    <div class="zone-color" style="background: #ffff44;" id="zone-color-3"></div>
                    <div class="zone-name">Cortex Moteur</div>
                    <div class="zone-temp" id="zone-temp-3">--°C</div>
                    <div class="zone-activity" id="zone-activity-3">--</div>
                </div>
                <div class="zone-indicator">
                    <div class="zone-color" style="background: #ff44ff;" id="zone-color-4"></div>
                    <div class="zone-name">Cortex Sensoriel</div>
                    <div class="zone-temp" id="zone-temp-4">--°C</div>
                    <div class="zone-activity" id="zone-activity-4">--</div>
                </div>
                <div class="zone-indicator">
                    <div class="zone-color" style="background: #44ffff;" id="zone-color-5"></div>
                    <div class="zone-name">Cervelet</div>
                    <div class="zone-temp" id="zone-temp-5">--°C</div>
                    <div class="zone-activity" id="zone-activity-5">--</div>
                </div>
            </div>

            <div class="control-group">
                <div class="control-title">🧪 NEUROTRANSMETTEURS</div>
                <div class="control-item">
                    <span>Dopamine:</span>
                    <span class="control-value" id="dopamine-level">--</span>
                </div>
                <div class="control-item">
                    <span>Sérotonine:</span>
                    <span class="control-value" id="serotonin-level">--</span>
                </div>
                <div class="control-item">
                    <span>GABA:</span>
                    <span class="control-value" id="gaba-level">--</span>
                </div>
                <div class="control-item">
                    <span>Glutamate:</span>
                    <span class="control-value" id="glutamate-level">--</span>
                </div>
            </div>
        </div>

        <div id="metrics">
            <div class="metric-card">
                <div class="metric-title">🌡️ TEMPÉRATURE CPU</div>
                <div class="metric-value" id="temp-display">--°C</div>
                <canvas class="metric-chart" id="temp-chart"></canvas>
            </div>
            <div class="metric-card">
                <div class="metric-title">⚡ ACTIVATIONS/SEC</div>
                <div class="metric-value" id="activations-display">--</div>
                <canvas class="metric-chart" id="activations-chart"></canvas>
            </div>
            <div class="metric-card">
                <div class="metric-title">🧠 QI ÉVOLUTIF</div>
                <div class="metric-value" id="qi-display">--</div>
                <canvas class="metric-chart" id="qi-chart"></canvas>
            </div>
            <div class="metric-card">
                <div class="metric-title">🔋 ÉNERGIE SYSTÈME</div>
                <div class="metric-value" id="energy-display">--</div>
                <canvas class="metric-chart" id="energy-chart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 🧠 CERVEAU THERMIQUE VIVANT 3D - MOTEUR PRINCIPAL
        class CerveauThermique3D {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.neurones = [];
                this.synapses = [];
                this.zones = [];
                this.particules = [];
                this.animationId = null;

                // Données temps réel
                this.donnees = {
                    temperature_cpu: 50,
                    activations_par_sec: 0,
                    qi_evolutif: 100,
                    energie_totale: 0,
                    attention: {
                        focus: 'Analyse',
                        intensite: 0.7,
                        temps_reaction: 100,
                        fatigue: 0.1
                    },
                    zones_cerebrales: [
                        { nom: 'cortex_prefrontal', temp: 70, activite: 25, couleur: 0xff4444 },
                        { nom: 'hippocampe', temp: 65, activite: 40, couleur: 0x44ff44 },
                        { nom: 'amygdale', temp: 75, activite: 15, couleur: 0x4444ff },
                        { nom: 'cortex_moteur', temp: 68, activite: 30, couleur: 0xffff44 },
                        { nom: 'cortex_sensoriel', temp: 62, activite: 35, couleur: 0xff44ff },
                        { nom: 'cervelet', temp: 60, activite: 20, couleur: 0x44ffff }
                    ],
                    neurotransmetteurs: {
                        dopamine: 0.5,
                        serotonine: 0.5,
                        gaba: 0.3,
                        glutamate: 0.7
                    }
                };

                // Historiques pour graphiques
                this.historiques = {
                    temperature: [],
                    activations: [],
                    qi: [],
                    energie: []
                };

                this.init();
                this.animate();
                this.demarrerSimulation();
            }

            init() {
                // Initialisation Three.js
                const container = document.getElementById('scene3d');
                const width = container.clientWidth;
                const height = container.clientHeight;

                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });

                this.renderer.setSize(width, height);
                this.renderer.setClearColor(0x000011, 0.8);
                container.appendChild(this.renderer.domElement);

                // Position caméra
                this.camera.position.set(0, 0, 50);

                // Lumières
                const lumiere_ambiante = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(lumiere_ambiante);

                const lumiere_directionnelle = new THREE.DirectionalLight(0xffffff, 0.8);
                lumiere_directionnelle.position.set(10, 10, 5);
                this.scene.add(lumiere_directionnelle);

                // Créer structure cerveau
                this.creerZonesCerebrales();
                this.creerNeurones();
                this.creerSynapses();
                this.creerParticules();

                // Contrôles souris
                this.ajouterControlesSouris();

                // Redimensionnement
                window.addEventListener('resize', () => this.onWindowResize());
            }

            creerZonesCerebrales() {
                const positions = [
                    { x: 0, y: 15, z: 0 },    // Cortex préfrontal
                    { x: -10, y: 0, z: -5 },  // Hippocampe
                    { x: 8, y: -5, z: -3 },   // Amygdale
                    { x: -5, y: 8, z: 2 },    // Cortex moteur
                    { x: 5, y: 8, z: 2 },     // Cortex sensoriel
                    { x: 0, y: -12, z: -8 }   // Cervelet
                ];

                this.donnees.zones_cerebrales.forEach((zone, index) => {
                    const geometrie = new THREE.SphereGeometry(4, 16, 16);
                    const materiau = new THREE.MeshPhongMaterial({
                        color: zone.couleur,
                        transparent: true,
                        opacity: 0.6,
                        emissive: zone.couleur,
                        emissiveIntensity: 0.2
                    });

                    const sphere = new THREE.Mesh(geometrie, materiau);
                    sphere.position.copy(positions[index]);
                    sphere.userData = { zone: zone.nom, index: index };

                    this.zones.push(sphere);
                    this.scene.add(sphere);

                    // Ajouter halo thermique
                    const halo_geo = new THREE.SphereGeometry(6, 16, 16);
                    const halo_mat = new THREE.MeshBasicMaterial({
                        color: zone.couleur,
                        transparent: true,
                        opacity: 0.1,
                        side: THREE.BackSide
                    });

                    const halo = new THREE.Mesh(halo_geo, halo_mat);
                    halo.position.copy(positions[index]);
                    sphere.add(halo);
                });
            }

            creerNeurones() {
                const nb_neurones_visibles = 200; // Échantillon pour performance

                for (let i = 0; i < nb_neurones_visibles; i++) {
                    const geometrie = new THREE.SphereGeometry(0.2, 8, 8);
                    const materiau = new THREE.MeshBasicMaterial({
                        color: 0x00ff88,
                        transparent: true,
                        opacity: 0.8
                    });

                    const neurone = new THREE.Mesh(geometrie, materiau);

                    // Position aléatoire dans une zone
                    const zone_index = Math.floor(Math.random() * this.zones.length);
                    const zone_pos = this.zones[zone_index].position;

                    neurone.position.set(
                        zone_pos.x + (Math.random() - 0.5) * 8,
                        zone_pos.y + (Math.random() - 0.5) * 8,
                        zone_pos.z + (Math.random() - 0.5) * 8
                    );

                    neurone.userData = {
                        id: i,
                        zone: this.donnees.zones_cerebrales[zone_index].nom,
                        potentiel: -70,
                        activations: 0,
                        derniere_activation: 0,
                        synapses: Math.floor(Math.random() * 50) + 10
                    };

                    this.neurones.push(neurone);
                    this.scene.add(neurone);
                }
            }

            creerSynapses() {
                const nb_synapses_visibles = 300;

                for (let i = 0; i < nb_synapses_visibles; i++) {
                    const neurone1 = this.neurones[Math.floor(Math.random() * this.neurones.length)];
                    const neurone2 = this.neurones[Math.floor(Math.random() * this.neurones.length)];

                    if (neurone1 !== neurone2) {
                        const points = [neurone1.position, neurone2.position];
                        const geometrie = new THREE.BufferGeometry().setFromPoints(points);
                        const materiau = new THREE.LineBasicMaterial({
                            color: 0x0088ff,
                            transparent: true,
                            opacity: 0.3
                        });

                        const ligne = new THREE.Line(geometrie, materiau);
                        ligne.userData = {
                            neurone1: neurone1,
                            neurone2: neurone2,
                            poids: Math.random(),
                            activite: 0
                        };

                        this.synapses.push(ligne);
                        this.scene.add(ligne);
                    }
                }
            }

            creerParticules() {
                const nb_particules = 100;
                const geometrie = new THREE.SphereGeometry(0.05, 4, 4);

                for (let i = 0; i < nb_particules; i++) {
                    const materiau = new THREE.MeshBasicMaterial({
                        color: 0x00ffff,
                        transparent: true,
                        opacity: 0.8
                    });

                    const particule = new THREE.Mesh(geometrie, materiau);
                    particule.position.set(
                        (Math.random() - 0.5) * 60,
                        (Math.random() - 0.5) * 60,
                        (Math.random() - 0.5) * 60
                    );

                    particule.userData = {
                        vitesse: new THREE.Vector3(
                            (Math.random() - 0.5) * 0.2,
                            (Math.random() - 0.5) * 0.2,
                            (Math.random() - 0.5) * 0.2
                        ),
                        vie: Math.random() * 100
                    };

                    this.particules.push(particule);
                    this.scene.add(particule);
                }
            }

            simulerActiviteNeuronale() {
                // Simulation basée sur température
                const facteur_temp = (this.donnees.temperature_cpu - 40) / 40;
                const probabilite_activation = 0.01 + facteur_temp * 0.05;

                let activations_cette_frame = 0;

                this.neurones.forEach(neurone => {
                    if (Math.random() < probabilite_activation) {
                        // Activation neurone
                        neurone.userData.activations++;
                        neurone.userData.derniere_activation = Date.now();
                        activations_cette_frame++;

                        // Animation activation
                        neurone.material.color.setHex(0xffff00);
                        neurone.material.emissive.setHex(0xffff00);
                        neurone.material.emissiveIntensity = 0.5;

                        // Retour normal après 100ms
                        setTimeout(() => {
                            neurone.material.color.setHex(0x00ff88);
                            neurone.material.emissive.setHex(0x000000);
                            neurone.material.emissiveIntensity = 0;
                        }, 100);

                        // Propager signal synapses
                        this.propagerSignal(neurone);
                    }
                });

                this.donnees.activations_par_sec = activations_cette_frame * 60; // 60 FPS
            }

            propagerSignal(neurone_source) {
                this.synapses.forEach(synapse => {
                    if (synapse.userData.neurone1 === neurone_source) {
                        // Animation flux synaptique
                        synapse.material.color.setHex(0x00ffff);
                        synapse.material.opacity = 0.8;
                        synapse.userData.activite = 1;

                        // Créer particule de signal
                        this.creerParticuleSynaptique(
                            synapse.userData.neurone1.position,
                            synapse.userData.neurone2.position
                        );

                        // Retour normal
                        setTimeout(() => {
                            synapse.material.color.setHex(0x0088ff);
                            synapse.material.opacity = 0.3;
                            synapse.userData.activite = 0;
                        }, 200);
                    }
                });
            }

            creerParticuleSynaptique(pos1, pos2) {
                const geometrie = new THREE.SphereGeometry(0.1, 4, 4);
                const materiau = new THREE.MeshBasicMaterial({
                    color: 0x00ffff,
                    transparent: true,
                    opacity: 1
                });

                const particule = new THREE.Mesh(geometrie, materiau);
                particule.position.copy(pos1);

                const direction = new THREE.Vector3().subVectors(pos2, pos1).normalize();
                const distance = pos1.distanceTo(pos2);

                particule.userData = {
                    direction: direction,
                    vitesse: 0.5,
                    distance_parcourue: 0,
                    distance_totale: distance,
                    temporaire: true
                };

                this.scene.add(particule);
                this.particules.push(particule);
            }

            demarrerSimulation() {
                setInterval(() => {
                    // Simulation température CPU
                    this.donnees.temperature_cpu = 45 + Math.sin(Date.now() * 0.001) * 15 + Math.random() * 5;

                    // Simulation QI évolutif
                    const bonus_temp = (this.donnees.temperature_cpu - 50) * 2;
                    this.donnees.qi_evolutif = 100 + bonus_temp + Math.random() * 10;

                    // Simulation attention
                    this.donnees.attention.focus = ['Tâche_A', 'Tâche_B', 'Analyse'][Math.floor(Math.random() * 3)];
                    this.donnees.attention.intensite = 0.5 + (this.donnees.temperature_cpu - 40) / 80;
                    this.donnees.attention.temps_reaction = Math.max(50, 200 - (this.donnees.temperature_cpu - 40) * 3);
                    this.donnees.attention.fatigue = Math.min(1, this.donnees.attention.fatigue + 0.001);

                    // Simulation neurotransmetteurs
                    this.donnees.neurotransmetteurs.dopamine = 0.3 + Math.random() * 0.4;
                    this.donnees.neurotransmetteurs.serotonine = 0.4 + Math.random() * 0.3;
                    this.donnees.neurotransmetteurs.gaba = 0.2 + Math.random() * 0.3;
                    this.donnees.neurotransmetteurs.glutamate = 0.5 + Math.random() * 0.4;

                    // Mise à jour interface
                    this.mettreAJourInterface();
                    this.ajouterLigneFluxDonnees();

                }, 100);
            }

            mettreAJourInterface() {
                // Header
                document.getElementById('cpu-temp').textContent = this.donnees.temperature_cpu.toFixed(1) + '°C';
                document.getElementById('neuron-count').textContent = this.neurones.length;
                document.getElementById('activity-rate').textContent = this.donnees.activations_par_sec.toFixed(0);

                // Attention
                document.getElementById('focus-target').textContent = this.donnees.attention.focus || '--';
                document.getElementById('focus-intensity').textContent = this.donnees.attention.intensite.toFixed(2);
                document.getElementById('reaction-time').textContent = this.donnees.attention.temps_reaction.toFixed(0) + 'ms';
                document.getElementById('fatigue-level').textContent = this.donnees.attention.fatigue.toFixed(3);

                // Zones cérébrales
                this.donnees.zones_cerebrales.forEach((zone, index) => {
                    document.getElementById(`zone-temp-${index}`).textContent = zone.temp.toFixed(1) + '°C';
                    document.getElementById(`zone-activity-${index}`).textContent = zone.activite.toFixed(0) + '%';
                });

                // Neurotransmetteurs
                document.getElementById('dopamine-level').textContent = this.donnees.neurotransmetteurs.dopamine.toFixed(3);
                document.getElementById('serotonin-level').textContent = this.donnees.neurotransmetteurs.serotonine.toFixed(3);
                document.getElementById('gaba-level').textContent = this.donnees.neurotransmetteurs.gaba.toFixed(3);
                document.getElementById('glutamate-level').textContent = this.donnees.neurotransmetteurs.glutamate.toFixed(3);

                // Métriques
                document.getElementById('temp-display').textContent = this.donnees.temperature_cpu.toFixed(1) + '°C';
                document.getElementById('activations-display').textContent = this.donnees.activations_par_sec.toFixed(0);
                document.getElementById('qi-display').textContent = this.donnees.qi_evolutif.toFixed(0);
                document.getElementById('energy-display').textContent = this.donnees.energie_totale.toFixed(1);
                document.getElementById('activity-level').textContent = this.donnees.activations_par_sec.toFixed(0) + ' Hz';
            }

            ajouterLigneFluxDonnees() {
                const stream = document.getElementById('data-stream');
                const timestamp = new Date().toLocaleTimeString();

                const events = [
                    `🌡️ CPU: ${this.donnees.temperature_cpu.toFixed(1)}°C`,
                    `⚡ Activations: ${this.donnees.activations_par_sec.toFixed(0)}/s`,
                    `🧠 QI: ${this.donnees.qi_evolutif.toFixed(0)}`,
                    `🎯 Focus: ${this.donnees.attention.focus}`,
                    `💊 Dopamine: ${this.donnees.neurotransmetteurs.dopamine.toFixed(2)}`
                ];

                const event = events[Math.floor(Math.random() * events.length)];

                const ligne = document.createElement('div');
                ligne.className = 'stream-line';
                ligne.innerHTML = `<span class="stream-timestamp">${timestamp}</span> <span class="stream-event">${event}</span>`;

                stream.appendChild(ligne);

                // Limiter nombre de lignes
                while (stream.children.length > 20) {
                    stream.removeChild(stream.firstChild);
                }

                // Auto-scroll
                stream.scrollTop = stream.scrollHeight;
            }

            mettreAJourParticules() {
                for (let i = this.particules.length - 1; i >= 0; i--) {
                    const particule = this.particules[i];
                    const userData = particule.userData;

                    if (userData.temporaire) {
                        // Particule synaptique
                        userData.distance_parcourue += userData.vitesse;

                        if (userData.distance_parcourue >= userData.distance_totale) {
                            // Arrivée à destination
                            this.scene.remove(particule);
                            this.particules.splice(i, 1);
                        } else {
                            // Mouvement
                            const deplacement = userData.direction.clone().multiplyScalar(userData.vitesse);
                            particule.position.add(deplacement);

                            // Fade out
                            const progression = userData.distance_parcourue / userData.distance_totale;
                            particule.material.opacity = 1 - progression;
                        }
                    } else {
                        // Particule ambiante
                        particule.position.add(userData.vitesse);
                        userData.vie--;

                        if (userData.vie <= 0) {
                            // Réinitialiser particule
                            particule.position.set(
                                (Math.random() - 0.5) * 60,
                                (Math.random() - 0.5) * 60,
                                (Math.random() - 0.5) * 60
                            );
                            userData.vie = 100;
                        }

                        // Adaptation couleur selon température
                        const intensite = (this.donnees.temperature_cpu - 40) / 40;
                        const couleur = new THREE.Color().setHSL(0.5 + intensite * 0.3, 1, 0.5);
                        particule.material.color = couleur;
                    }
                }
            }

            mettreAJourZones() {
                this.zones.forEach((zone, index) => {
                    const donnees_zone = this.donnees.zones_cerebrales[index];

                    // Adaptation température
                    donnees_zone.temp = this.donnees.temperature_cpu + (Math.random() - 0.5) * 10;

                    // Adaptation couleur selon activité
                    const intensite = donnees_zone.activite / 100;
                    zone.material.emissiveIntensity = 0.2 + intensite * 0.5;

                    // Pulsation selon température
                    const facteur_temp = (donnees_zone.temp - 40) / 40;
                    const scale = 1 + Math.sin(Date.now() * 0.01) * facteur_temp * 0.1;
                    zone.scale.setScalar(scale);

                    // Rotation lente
                    zone.rotation.y += 0.005;
                });
            }

            ajouterControlesSouris() {
                const container = document.getElementById('scene3d');

                let mouseDown = false;
                let mouseX = 0;
                let mouseY = 0;

                container.addEventListener('mousedown', (event) => {
                    mouseDown = true;
                    mouseX = event.clientX;
                    mouseY = event.clientY;
                });

                container.addEventListener('mouseup', () => {
                    mouseDown = false;
                });

                container.addEventListener('mousemove', (event) => {
                    if (mouseDown) {
                        const deltaX = event.clientX - mouseX;
                        const deltaY = event.clientY - mouseY;

                        this.camera.position.x += deltaX * 0.01;
                        this.camera.position.y -= deltaY * 0.01;

                        mouseX = event.clientX;
                        mouseY = event.clientY;
                    }
                });

                container.addEventListener('wheel', (event) => {
                    this.camera.position.z += event.deltaY * 0.01;
                    this.camera.position.z = Math.max(10, Math.min(100, this.camera.position.z));
                });
            }

            onWindowResize() {
                const container = document.getElementById('scene3d');
                const width = container.clientWidth;
                const height = container.clientHeight;

                this.camera.aspect = width / height;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(width, height);
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // Simulations
                this.simulerActiviteNeuronale();
                this.mettreAJourParticules();
                this.mettreAJourZones();

                // Rotation caméra automatique
                this.camera.position.x = Math.cos(Date.now() * 0.0005) * 50;
                this.camera.position.z = Math.sin(Date.now() * 0.0005) * 50;
                this.camera.lookAt(0, 0, 0);

                // Rendu
                if (this.renderer && this.scene && this.camera) {
                    this.renderer.render(this.scene, this.camera);
                }
            }
        }

        // Fonction pour créer mode démonstration CSS
        function creerModeDemonstrationCSS() {
            console.log('🎭 Activation mode démonstration CSS...');

            const scene = document.getElementById('scene3d');
            scene.innerHTML = `
                <div class="demo-brain">
                    <div class="demo-zone demo-zone-1" title="Cortex Préfrontal"></div>
                    <div class="demo-zone demo-zone-2" title="Hippocampe"></div>
                    <div class="demo-zone demo-zone-3" title="Amygdale"></div>
                    <div class="demo-zone demo-zone-4" title="Cortex Moteur"></div>
                    <div class="demo-zone demo-zone-5" title="Cortex Sensoriel"></div>
                    <div class="demo-zone demo-zone-6" title="Cervelet"></div>
                </div>
                <div style="position: absolute; bottom: 20px; left: 20px; color: #00ff88; font-size: 12px;">
                    🎭 MODE DÉMONSTRATION CSS<br>
                    (Three.js non disponible)
                </div>
            `;

            // Ajouter neurones animés
            for (let i = 0; i < 50; i++) {
                const neuron = document.createElement('div');
                neuron.className = 'demo-neuron';
                neuron.style.left = Math.random() * 300 + 'px';
                neuron.style.top = Math.random() * 300 + 'px';
                neuron.style.animationDelay = Math.random() * 3 + 's';
                scene.querySelector('.demo-brain').appendChild(neuron);
            }

            // Ajouter connexions
            for (let i = 0; i < 20; i++) {
                const connection = document.createElement('div');
                connection.className = 'demo-connection';
                connection.style.left = Math.random() * 250 + 'px';
                connection.style.top = Math.random() * 250 + 'px';
                connection.style.width = Math.random() * 100 + 50 + 'px';
                connection.style.transform = `rotate(${Math.random() * 360}deg)`;
                connection.style.animationDelay = Math.random() * 2 + 's';
                scene.querySelector('.demo-brain').appendChild(connection);
            }

            // Démarrer simulation données
            demarrerSimulationDonnees();
        }

        // Fonction pour simuler les données même sans Three.js
        function demarrerSimulationDonnees() {
            const donnees = {
                temperature_cpu: 50,
                activations_par_sec: 0,
                qi_evolutif: 100,
                attention: {
                    focus: 'Analyse',
                    intensite: 0.7,
                    temps_reaction: 100,
                    fatigue: 0.1
                },
                zones_cerebrales: [
                    { nom: 'cortex_prefrontal', temp: 70, activite: 25 },
                    { nom: 'hippocampe', temp: 65, activite: 40 },
                    { nom: 'amygdale', temp: 75, activite: 15 },
                    { nom: 'cortex_moteur', temp: 68, activite: 30 },
                    { nom: 'cortex_sensoriel', temp: 62, activite: 35 },
                    { nom: 'cervelet', temp: 60, activite: 20 }
                ],
                neurotransmetteurs: {
                    dopamine: 0.5,
                    serotonine: 0.5,
                    gaba: 0.3,
                    glutamate: 0.7
                }
            };

            setInterval(() => {
                // Simulation température CPU
                donnees.temperature_cpu = 45 + Math.sin(Date.now() * 0.001) * 15 + Math.random() * 5;

                // Simulation QI évolutif
                const bonus_temp = (donnees.temperature_cpu - 50) * 2;
                donnees.qi_evolutif = 100 + bonus_temp + Math.random() * 10;

                // Simulation activations
                donnees.activations_par_sec = Math.floor(Math.random() * 100) + 50;

                // Simulation attention
                donnees.attention.focus = ['Tâche_A', 'Tâche_B', 'Analyse'][Math.floor(Math.random() * 3)];
                donnees.attention.intensite = 0.5 + (donnees.temperature_cpu - 40) / 80;
                donnees.attention.temps_reaction = Math.max(50, 200 - (donnees.temperature_cpu - 40) * 3);
                donnees.attention.fatigue = Math.min(1, donnees.attention.fatigue + 0.001);

                // Simulation neurotransmetteurs
                donnees.neurotransmetteurs.dopamine = 0.3 + Math.random() * 0.4;
                donnees.neurotransmetteurs.serotonine = 0.4 + Math.random() * 0.3;
                donnees.neurotransmetteurs.gaba = 0.2 + Math.random() * 0.3;
                donnees.neurotransmetteurs.glutamate = 0.5 + Math.random() * 0.4;

                // Mise à jour interface
                mettreAJourInterfaceSimulee(donnees);
                ajouterLigneFluxSimulee(donnees);

            }, 100);
        }

        function mettreAJourInterfaceSimulee(donnees) {
            // Header
            document.getElementById('cpu-temp').textContent = donnees.temperature_cpu.toFixed(1) + '°C';
            document.getElementById('neuron-count').textContent = '200';
            document.getElementById('activity-rate').textContent = donnees.activations_par_sec.toFixed(0);

            // Attention
            document.getElementById('focus-target').textContent = donnees.attention.focus || '--';
            document.getElementById('focus-intensity').textContent = donnees.attention.intensite.toFixed(2);
            document.getElementById('reaction-time').textContent = donnees.attention.temps_reaction.toFixed(0) + 'ms';
            document.getElementById('fatigue-level').textContent = donnees.attention.fatigue.toFixed(3);

            // Zones cérébrales
            donnees.zones_cerebrales.forEach((zone, index) => {
                document.getElementById(`zone-temp-${index}`).textContent = zone.temp.toFixed(1) + '°C';
                document.getElementById(`zone-activity-${index}`).textContent = zone.activite.toFixed(0) + '%';
            });

            // Neurotransmetteurs
            document.getElementById('dopamine-level').textContent = donnees.neurotransmetteurs.dopamine.toFixed(3);
            document.getElementById('serotonin-level').textContent = donnees.neurotransmetteurs.serotonine.toFixed(3);
            document.getElementById('gaba-level').textContent = donnees.neurotransmetteurs.gaba.toFixed(3);
            document.getElementById('glutamate-level').textContent = donnees.neurotransmetteurs.glutamate.toFixed(3);

            // Métriques
            document.getElementById('temp-display').textContent = donnees.temperature_cpu.toFixed(1) + '°C';
            document.getElementById('activations-display').textContent = donnees.activations_par_sec.toFixed(0);
            document.getElementById('qi-display').textContent = donnees.qi_evolutif.toFixed(0);
            document.getElementById('energy-display').textContent = '75.3';
            document.getElementById('activity-level').textContent = donnees.activations_par_sec.toFixed(0) + ' Hz';
        }

        function ajouterLigneFluxSimulee(donnees) {
            const stream = document.getElementById('data-stream');
            const timestamp = new Date().toLocaleTimeString();

            const events = [
                `🌡️ CPU: ${donnees.temperature_cpu.toFixed(1)}°C`,
                `⚡ Activations: ${donnees.activations_par_sec.toFixed(0)}/s`,
                `🧠 QI: ${donnees.qi_evolutif.toFixed(0)}`,
                `🎯 Focus: ${donnees.attention.focus}`,
                `💊 Dopamine: ${donnees.neurotransmetteurs.dopamine.toFixed(2)}`
            ];

            const event = events[Math.floor(Math.random() * events.length)];

            const ligne = document.createElement('div');
            ligne.className = 'stream-line';
            ligne.innerHTML = `<span class="stream-timestamp">${timestamp}</span> <span class="stream-event">${event}</span>`;

            stream.appendChild(ligne);

            // Limiter nombre de lignes
            while (stream.children.length > 20) {
                stream.removeChild(stream.firstChild);
            }

            // Auto-scroll
            stream.scrollTop = stream.scrollHeight;
        }

        // Vérification Three.js et démarrage
        document.addEventListener('DOMContentLoaded', () => {
            if (typeof THREE === 'undefined') {
                console.error('❌ Three.js non chargé, activation mode démonstration CSS...');
                creerModeDemonstrationCSS();
                return;
            }

            console.log('✅ Three.js chargé, initialisation du cerveau 3D...');
            try {
                new CerveauThermique3D();
                console.log('🧠 Cerveau 3D initialisé avec succès !');
            } catch (error) {
                console.error('❌ Erreur initialisation cerveau 3D:', error);
                console.log('🎭 Fallback vers mode démonstration CSS...');
                creerModeDemonstrationCSS();
            }
        });
    </script>
</body>
</html>