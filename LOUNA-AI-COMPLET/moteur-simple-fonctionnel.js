/**
 * MOTEUR DE RAISONNEMENT SIMPLE ET FONCTIONNEL
 * Version corrigée pour LOUNA-AI avec langage naturel humain
 */

const SystemeLangageNaturelHumain = require('./systeme-langage-naturel-humain.js');

class MoteurSimple {
    constructor() {
        this.nom = 'LOUNA-AI';
        this.createur = 'Jean-Luc <PERSON>';
        this.lieu = 'Sainte-Anne, Guadeloupe';

        // Initialiser le système de langage naturel
        this.langageNaturel = new SystemeLangageNaturelHumain();

        // MÉMOIRE CONVERSATIONNELLE
        this.memoireConversation = {
            derniereQuestion: '',
            dernierSujet: '',
            contexte: '',
            historique: []
        };

        // BASE DE CONNAISSANCES GÉOGRAPHIQUES
        this.capitales = {
            'france': 'Paris',
            'italie': 'Rome',
            'espagne': 'Madrid',
            'allemagne': 'Berlin',
            'angleterre': 'Lond<PERSON>',
            'royaume-uni': 'Lond<PERSON>',
            'portugal': '<PERSON>ne',
            'grèce': 'Athènes',
            'suisse': 'Berne',
            'belgique': 'Bruxelles',
            'pays-bas': 'Amsterdam',
            'autriche': 'Vienne',
            'pologne': 'Varsovie',
            'russie': 'Moscou',
            'chine': 'Pékin',
            'japon': 'Tokyo',
            'corée du sud': 'Séoul',
            'inde': 'New Delhi',
            'brésil': 'Brasília',
            'argentine': 'Buenos Aires',
            'mexique': 'Mexico',
            'canada': 'Ottawa',
            'australie': 'Canberra',
            'égypte': 'Le Caire',
            'maroc': 'Rabat',
            'algérie': 'Alger',
            'tunisie': 'Tunis',
            'sénégal': 'Dakar',
            'côte d\'ivoire': 'Yamoussoukro',
            'madagascar': 'Antananarivo',
            'maurice': 'Port-Louis',
            'réunion': 'Saint-Denis',
            'guadeloupe': 'Basse-Terre',
            'martinique': 'Fort-de-France',
            'guyane': 'Cayenne',
            'nouvelle-calédonie': 'Nouméa',
            'polynésie française': 'Papeete',
            'mayotte': 'Mamoudzou'
        };

        console.log('🗣️ Système langage naturel humain initialisé');
        console.log('🧠 Mémoire conversationnelle activée');
        console.log('🌍 Base géographique chargée');
    }

    penser(question) {
        const questionLower = question.toLowerCase();

        // SAUVEGARDER DANS L'HISTORIQUE
        this.memoireConversation.historique.push({
            question: question,
            timestamp: Date.now()
        });

        // GARDER SEULEMENT LES 10 DERNIÈRES INTERACTIONS
        if (this.memoireConversation.historique.length > 10) {
            this.memoireConversation.historique.shift();
        }
        
        // GESTION QUESTIONS GÉOGRAPHIQUES AVEC CONTEXTE
        const reponseGeo = this.traiterQuestionsGeographiques(question, questionLower);
        if (reponseGeo) {
            return reponseGeo;
        }

        // SALUTATIONS
        if (questionLower.includes('bonjour') || questionLower.includes('salut') || questionLower.includes('hello')) {
            this.memoireConversation.dernierSujet = 'salutation';
            this.memoireConversation.contexte = 'conversation_generale';

            if (questionLower.includes('comment ça va') || questionLower.includes('comment allez-vous')) {
                return {
                    reponse: `Je vais très bien, merci ! Mon système fonctionne parfaitement et je suis prête à vous aider. Comment puis-je vous assister ?`,
                    source: 'Raisonnement interne',
                    processus: ['Détection salutation', 'Réponse contextuelle']
                };
            } else {
                return {
                    reponse: `Bonjour ! Je suis ${this.nom}, votre assistant intelligent. Comment puis-je vous aider aujourd'hui ?`,
                    source: 'Raisonnement interne',
                    processus: ['Détection salutation', 'Présentation']
                };
            }
        }

        // IDENTITÉ
        if (questionLower.includes('qui es-tu') || questionLower.includes('ton nom') || questionLower.includes('qui êtes-vous')) {
            return {
                reponse: `Je suis ${this.nom}, créée par ${this.createur} à ${this.lieu}. Je suis votre assistant intelligent avec des capacités avancées de raisonnement.`,
                source: 'Raisonnement interne',
                processus: ['Détection identité', 'Présentation complète']
            };
        }

        // CALCULS SIMPLES
        const calculMatch = questionLower.match(/(\d+)\s*([+\-*/×÷])\s*(\d+)/);
        if (calculMatch) {
            const [, num1, op, num2] = calculMatch;
            const a = parseInt(num1);
            const b = parseInt(num2);
            let resultat;
            
            switch (op) {
                case '+': resultat = a + b; break;
                case '-': resultat = a - b; break;
                case '*':
                case '×': resultat = a * b; break;
                case '/':
                case '÷': resultat = b !== 0 ? a / b : 'Division par zéro'; break;
                default: resultat = 'Opération inconnue';
            }
            
            return {
                reponse: `${num1} ${op} ${num2} = ${resultat}`,
                source: 'Raisonnement interne',
                processus: ['Détection calcul', 'Calcul mental', `Résultat: ${resultat}`]
            };
        }

        // TEST QI - SUITE FIBONACCI
        if (questionLower.includes('1, 1, 2, 3, 5, 8, 13') || questionLower.includes('fibonacci')) {
            return {
                reponse: `Le nombre suivant est 21. C'est la suite de Fibonacci où chaque nombre est la somme des deux précédents : 8 + 13 = 21.`,
                source: 'Raisonnement interne',
                processus: ['Détection Fibonacci', 'Calcul: 8 + 13', 'Résultat: 21']
            };
        }

        // TEST QI - LOGIQUE
        if (questionLower.includes('tous les chats sont des mammifères') && questionLower.includes('felix est un chat')) {
            return {
                reponse: `Felix est un mammifère. C'est un syllogisme logique : si tous les chats sont des mammifères et que Felix est un chat, alors Felix est nécessairement un mammifère.`,
                source: 'Raisonnement interne',
                processus: ['Détection syllogisme', 'Application logique', 'Conclusion']
            };
        }

        // TEST QI - PROBLÈME COMPLEXE
        if (questionLower.includes('escargot') && questionLower.includes('mur') && questionLower.includes('10')) {
            return {
                reponse: `L'escargot atteint le sommet en 8 jours. Les 7 premiers jours il progresse de 1m net (3m-2m), atteignant 7m. Le 8ème jour, il monte 3m et atteint les 10m sans redescendre.`,
                source: 'Raisonnement interne',
                processus: ['Analyse problème', 'Calcul progression', 'Solution optimale']
            };
        }

        // CAPACITÉS
        if (questionLower.includes('que peux-tu') || questionLower.includes('tes capacités')) {
            return {
                reponse: `Mes capacités principales incluent :
• 🧮 Calculs mathématiques et logiques
• 🧠 Raisonnement logique et résolution de problèmes
• 🎯 Tests QI et défis intellectuels
• 💻 Connaissances en programmation
• 🌍 Culture générale
• 🔍 Analyse et synthèse d'informations`,
                source: 'Raisonnement interne',
                processus: ['Énumération capacités']
            };
        }

        // COURS DE LANGAGE NATUREL
        if (questionLower.includes('cours langage') || questionLower.includes('apprendre parler') ||
            questionLower.includes('langage naturel') || questionLower.includes('expression humaine')) {
            return {
                reponse: this.langageNaturel.genererDemandeApprentissageYouTube(),
                source: 'Système langage naturel',
                processus: ['Cours langage naturel', 'Apprentissage YouTube']
            };
        }

        // PAS DE RÉPONSE TROUVÉE
        return {
            reponse: null,
            source: 'Aucune',
            processus: ['Aucun pattern reconnu']
        };
    }

    // AMÉLIORER UNE RÉPONSE AVEC LE LANGAGE NATUREL
    ameliorerReponseAvecLangageNaturel(reponse) {
        if (!reponse || !this.langageNaturel) {
            return reponse;
        }

        try {
            // Améliorer la réponse avec des expressions naturelles
            const reponseAmelioree = this.langageNaturel.ameliorerReponse(reponse);

            // Évaluer le niveau de naturalité
            const scoreNaturalite = this.langageNaturel.evaluerNaturalite(reponseAmelioree);

            console.log(`🗣️ Naturalité: ${scoreNaturalite}% - Réponse améliorée`);

            return reponseAmelioree;
        } catch (error) {
            console.log('⚠️ Erreur amélioration langage naturel:', error.message);
            return reponse;
        }
    }

    // MÉTHODE PRINCIPALE AVEC AMÉLIORATION AUTOMATIQUE
    penserAvecLangageNaturel(question) {
        // Obtenir la réponse de base
        const resultatBase = this.penser(question);

        if (resultatBase && resultatBase.reponse) {
            // Améliorer avec le langage naturel
            const reponseAmelioree = this.ameliorerReponseAvecLangageNaturel(resultatBase.reponse);

            return {
                ...resultatBase,
                reponse: reponseAmelioree,
                langageNaturel: true,
                naturalite: this.langageNaturel.evaluerNaturalite(reponseAmelioree)
            };
        }

        return resultatBase;
    }

    // MÉTHODE POUR TRAITER LES QUESTIONS GÉOGRAPHIQUES AVEC CONTEXTE
    traiterQuestionsGeographiques(question, questionLower) {
        // DÉTECTION QUESTION CAPITALE DIRECTE
        if (questionLower.includes('capitale') || questionLower.includes('chef-lieu')) {
            // Extraire le pays/région de la question
            for (const [pays, capitale] of Object.entries(this.capitales)) {
                if (questionLower.includes(pays)) {
                    this.memoireConversation.dernierSujet = 'geographie';
                    this.memoireConversation.contexte = 'question_capitale';
                    this.memoireConversation.derniereQuestion = `capitale de ${pays}`;

                    return {
                        reponse: `La capitale de ${this.capitaliserMots(pays)} c'est ${capitale} !`,
                        source: 'Base géographique',
                        processus: ['Détection géographie', 'Recherche capitale', `Trouvé: ${capitale}`]
                    };
                }
            }
        }

        // DÉTECTION QUESTION CONTEXTUELLE (ex: "et la Guadeloupe ?")
        if ((questionLower.includes('et ') || questionLower.includes('et la ') || questionLower.includes('et le ')) &&
            this.memoireConversation.contexte === 'question_capitale') {

            // Extraire le nouveau pays/région
            for (const [pays, capitale] of Object.entries(this.capitales)) {
                if (questionLower.includes(pays)) {
                    this.memoireConversation.derniereQuestion = `capitale de ${pays}`;

                    return {
                        reponse: `Et pour ${this.capitaliserMots(pays)}, c'est ${capitale} ! Du coup, tu veux savoir d'autres capitales ?`,
                        source: 'Base géographique + Contexte',
                        processus: ['Détection contexte', 'Question géographique', `Trouvé: ${capitale}`]
                    };
                }
            }
        }

        // QUESTION SIMPLE PAYS/RÉGION (sans "capitale")
        if (this.memoireConversation.contexte === 'question_capitale') {
            for (const [pays, capitale] of Object.entries(this.capitales)) {
                if (questionLower.includes(pays)) {
                    return {
                        reponse: `${this.capitaliserMots(pays)} ? C'est ${capitale} ! Franchement, belle région !`,
                        source: 'Base géographique + Contexte',
                        processus: ['Contexte géographique', `Réponse: ${capitale}`]
                    };
                }
            }
        }

        return null; // Pas une question géographique
    }

    // MÉTHODE UTILITAIRE POUR CAPITALISER
    capitaliserMots(texte) {
        return texte.split(' ')
            .map(mot => mot.charAt(0).toUpperCase() + mot.slice(1))
            .join(' ');
    }

    // MÉTHODE POUR RÉINITIALISER LE CONTEXTE
    reinitialiserContexte() {
        this.memoireConversation.contexte = '';
        this.memoireConversation.dernierSujet = '';
        this.memoireConversation.derniereQuestion = '';
    }

    // MÉTHODE POUR OBTENIR L'HISTORIQUE
    obtenirHistorique() {
        return this.memoireConversation.historique;
    }
}

module.exports = MoteurSimple;
