<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 TEST QI ULTRA-POUSSÉ - NIVEAU GÉNIE ABSOLU</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #2a0a2a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 3px solid #ff0066;
            margin-bottom: 30px;
        }

        .title {
            font-size: 36px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff0066, #ff6600, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(255, 0, 102, 0.8);
            margin-bottom: 15px;
        }

        .warning {
            background: linear-gradient(135deg, #ff0000, #cc0000);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid #ff6666;
            text-align: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 0, 0, 0.5); }
            50% { box-shadow: 0 0 40px rgba(255, 0, 0, 0.8); }
        }

        .question-container {
            background: linear-gradient(135deg, #1a1a1a, #2a1a2a);
            padding: 30px;
            border-radius: 20px;
            margin: 20px 0;
            border: 2px solid rgba(255, 0, 102, 0.3);
            position: relative;
        }

        .question-number {
            position: absolute;
            top: -15px;
            left: 30px;
            background: linear-gradient(45deg, #ff0066, #ff6600);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 18px;
        }

        .difficulty {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .extreme { background: linear-gradient(45deg, #ff0000, #cc0000); }
        .impossible { background: linear-gradient(45deg, #8b0000, #4b0000); }
        .transcendant { background: linear-gradient(45deg, #ff6600, #ff0066, #6600ff); }

        .question-text {
            font-size: 18px;
            line-height: 1.6;
            margin: 20px 0;
        }

        .math-formula {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            margin: 15px 0;
            border-left: 4px solid #ff0066;
        }

        .test-button {
            background: linear-gradient(135deg, #ff0066, #ff6600);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            display: block;
            width: 100%;
            max-width: 400px;
            margin: 20px auto;
        }

        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 0, 102, 0.5);
        }

        .response-area {
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 0, 102, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
            font-size: 16px;
            line-height: 1.6;
        }

        .timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff0066, #cc0000);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 18px;
            border: 2px solid #ff6666;
        }

        .score-display {
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #00ff66, #0066ff);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 18px;
            border: 2px solid #66ffff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🧠 TEST QI ULTRA-POUSSÉ</div>
            <div style="font-size: 24px; color: #ff6600;">NIVEAU GÉNIE ABSOLU - QI 300+</div>
        </div>

        <div class="warning">
            ⚠️ ATTENTION EXTRÊME ⚠️<br>
            Ce test contient les questions les plus difficiles jamais conçues<br>
            Niveau requis: QI 250+ | Doctorat en Mathématiques/Physique/Philosophie<br>
            Durée estimée: 2-4 heures | Préparation mentale recommandée
        </div>

        <div class="timer" id="timer">⏱️ 00:00:00</div>
        <div class="score-display" id="score">🧠 QI: Calcul...</div>

        <!-- QUESTION 1: MATHÉMATIQUES TRANSCENDANTES -->
        <div class="question-container">
            <div class="question-number">Q1</div>
            <div class="difficulty transcendant">TRANSCENDANT</div>
            <div class="question-text">
                <strong>🔥 CONJECTURE DE RIEMANN APPLIQUÉE :</strong><br>
                Soit ζ(s) la fonction zêta de Riemann. Démontrez que tous les zéros non-triviaux de ζ(s) ont une partie réelle égale à 1/2, 
                OU trouvez un contre-exemple explicite avec calcul numérique précis à 50 décimales.
            </div>
            <div class="math-formula">
                ζ(s) = Σ(n=1 à ∞) 1/n^s = Π(p premier) 1/(1-p^(-s))<br>
                Hypothèse: Re(ρ) = 1/2 pour tout zéro non-trivial ρ
            </div>
            <button class="test-button" onclick="testerQuestion(1, 'riemann')">🧮 TESTER CETTE QUESTION</button>
        </div>

        <!-- QUESTION 2: PHYSIQUE QUANTIQUE EXTRÊME -->
        <div class="question-container">
            <div class="question-number">Q2</div>
            <div class="difficulty impossible">IMPOSSIBLE</div>
            <div class="question-text">
                <strong>⚛️ PARADOXE QUANTIQUE ULTIME :</strong><br>
                Un système quantique est dans une superposition de 10^23 états. Calculez la probabilité exacte qu'une mesure 
                simultanée de position, impulsion et spin révèle des valeurs cohérentes avec la relativité générale dans un 
                champ gravitationnel variant selon r^(-7/3).
            </div>
            <div class="math-formula">
                |ψ⟩ = Σ(i=1 à 10^23) αᵢ|φᵢ⟩ avec Σ|αᵢ|² = 1<br>
                [x̂,p̂] = iℏ, g_μν = diag(-1,1,1,1) + h_μν(r^(-7/3))
            </div>
            <button class="test-button" onclick="testerQuestion(2, 'quantique')">⚛️ TESTER CETTE QUESTION</button>
        </div>

        <!-- QUESTION 3: LOGIQUE GÖDELIENNE -->
        <div class="question-container">
            <div class="question-number">Q3</div>
            <div class="difficulty transcendant">TRANSCENDANT</div>
            <div class="question-text">
                <strong>🔢 MÉTA-MATHÉMATIQUES GÖDELIENNES :</strong><br>
                Construisez une phrase G dans le langage de l'arithmétique de Peano telle que PA ⊢ G ↔ ¬Prov_PA(⌜G⌝), 
                puis démontrez que cette construction implique l'existence d'une hiérarchie infinie de vérités indécidables.
            </div>
            <div class="math-formula">
                G ≡ ∀x(¬Proof(x, ⌜G⌝))<br>
                Si PA ⊢ G alors PA ⊢ ¬Prov_PA(⌜G⌝) donc PA est inconsistante<br>
                Si PA ⊢ ¬G alors PA ⊢ Prov_PA(⌜G⌝) donc PA ⊢ G, contradiction
            </div>
            <button class="test-button" onclick="testerQuestion(3, 'godel')">🧠 TESTER CETTE QUESTION</button>
        </div>

        <!-- QUESTION 4: THÉORIE DES CORDES -->
        <div class="question-container">
            <div class="question-number">Q4</div>
            <div class="difficulty extreme">EXTRÊME</div>
            <div class="question-text">
                <strong>🌌 UNIFICATION ULTIME :</strong><br>
                Dans la théorie M à 11 dimensions, calculez la constante de couplage effective lorsque 7 dimensions 
                se compactifient sur une variété de Calabi-Yau avec h^(1,1) = 491 et h^(2,1) = 11. Incluez les corrections 
                d'instantons worldsheet et les effets non-perturbatifs.
            </div>
            <div class="math-formula">
                S = (1/2κ²)∫d¹¹x √g [R + (1/12)H_μνρH^μνρ + ...]<br>
                g_eff = g_s × Vol(CY)^(-1/2) × e^(-S_inst)
            </div>
            <button class="test-button" onclick="testerQuestion(4, 'cordes')">🌌 TESTER CETTE QUESTION</button>
        </div>

        <!-- QUESTION 5: CONSCIENCE ET COMPUTATION -->
        <div class="question-container">
            <div class="question-number">Q5</div>
            <div class="difficulty impossible">IMPOSSIBLE</div>
            <div class="question-text">
                <strong>🤖 CONSCIENCE COMPUTATIONNELLE :</strong><br>
                Prouvez ou réfutez: "Une machine de Turing universelle peut simuler parfaitement la conscience humaine 
                si et seulement si P = NP et la conjecture de Church-Turing étendue est vraie dans tous les univers 
                parallèles accessibles via décohérence quantique."
            </div>
            <div class="math-formula">
                Conscience ⊆ Computation ↔ (P = NP) ∧ (CTT_extended) ∧ (∀u ∈ Multiverse: Accessible(u))
            </div>
            <button class="test-button" onclick="testerQuestion(5, 'conscience')">🤖 TESTER CETTE QUESTION</button>
        </div>

        <div class="response-area" id="response-area">
            <strong>🧠 RÉPONSES DE LOUNA-AI APPARAÎTRONT ICI</strong><br>
            Sélectionnez une question pour commencer le test ultime...
        </div>

        <button class="test-button" onclick="lancerTestComplet()" style="background: linear-gradient(135deg, #ff0000, #8b0000); font-size: 20px;">
            🔥 LANCER TEST COMPLET ULTRA-POUSSÉ 🔥
        </button>
    </div>

    <script>
        let startTime = Date.now();
        let currentScore = 0;
        let questionsAnswered = 0;

        // Timer
        setInterval(() => {
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('timer').textContent = 
                `⏱️ ${hours.toString().padStart(2,'0')}:${minutes.toString().padStart(2,'0')}:${seconds.toString().padStart(2,'0')}`;
        }, 1000);

        async function testerQuestion(numero, type) {
            const questions = {
                1: "🔥 CONJECTURE DE RIEMANN ULTIME: Soit ζ(s) la fonction zêta de Riemann. Démontrez que tous les zéros non-triviaux de ζ(s) ont une partie réelle égale à 1/2, OU trouvez un contre-exemple explicite avec calcul numérique précis à 50 décimales. Utilisez la formule ζ(s) = Σ(n=1 à ∞) 1/n^s = Π(p premier) 1/(1-p^(-s)). Hypothèse: Re(ρ) = 1/2 pour tout zéro non-trivial ρ. NIVEAU: PRIX NOBEL DE MATHÉMATIQUES.",
                
                2: "⚛️ PARADOXE QUANTIQUE TRANSCENDANT: Un système quantique est dans une superposition de 10^23 états. Calculez la probabilité exacte qu'une mesure simultanée de position, impulsion et spin révèle des valeurs cohérentes avec la relativité générale dans un champ gravitationnel variant selon r^(-7/3). |ψ⟩ = Σ(i=1 à 10^23) αᵢ|φᵢ⟩ avec Σ|αᵢ|² = 1, [x̂,p̂] = iℏ, g_μν = diag(-1,1,1,1) + h_μν(r^(-7/3)). NIVEAU: GÉNIE QUANTIQUE ABSOLU.",
                
                3: "🔢 MÉTA-MATHÉMATIQUES GÖDELIENNES ULTIMES: Construisez une phrase G dans le langage de l'arithmétique de Peano telle que PA ⊢ G ↔ ¬Prov_PA(⌜G⌝), puis démontrez que cette construction implique l'existence d'une hiérarchie infinie de vérités indécidables. G ≡ ∀x(¬Proof(x, ⌜G⌝)). Si PA ⊢ G alors PA ⊢ ¬Prov_PA(⌜G⌝) donc PA est inconsistante. Si PA ⊢ ¬G alors PA ⊢ Prov_PA(⌜G⌝) donc PA ⊢ G, contradiction. NIVEAU: TRANSCENDANCE LOGIQUE.",
                
                4: "🌌 THÉORIE M UNIFICATION ULTIME: Dans la théorie M à 11 dimensions, calculez la constante de couplage effective lorsque 7 dimensions se compactifient sur une variété de Calabi-Yau avec h^(1,1) = 491 et h^(2,1) = 11. Incluez les corrections d'instantons worldsheet et les effets non-perturbatifs. S = (1/2κ²)∫d¹¹x √g [R + (1/12)H_μνρH^μνρ + ...], g_eff = g_s × Vol(CY)^(-1/2) × e^(-S_inst). NIVEAU: MAÎTRE DE L'UNIVERS.",
                
                5: "🤖 CONSCIENCE COMPUTATIONNELLE ULTIME: Prouvez ou réfutez: 'Une machine de Turing universelle peut simuler parfaitement la conscience humaine si et seulement si P = NP et la conjecture de Church-Turing étendue est vraie dans tous les univers parallèles accessibles via décohérence quantique.' Conscience ⊆ Computation ↔ (P = NP) ∧ (CTT_extended) ∧ (∀u ∈ Multiverse: Accessible(u)). NIVEAU: DIEU DE LA LOGIQUE."
            };

            const responseArea = document.getElementById('response-area');
            responseArea.innerHTML = `<strong>🧠 QUESTION ${numero} EN COURS...</strong><br>Traitement par LOUNA-AI...`;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: questions[numero] })
                });

                const data = await response.json();
                
                if (data.success) {
                    questionsAnswered++;
                    const reponse = data.reponse || data.response;
                    
                    // Calcul du score basé sur la complexité de la réponse
                    let score = calculerScore(reponse, numero);
                    currentScore += score;
                    
                    responseArea.innerHTML = `
                        <strong>🧠 QUESTION ${numero} - RÉPONSE DE LOUNA-AI:</strong><br><br>
                        ${reponse}<br><br>
                        <strong>📊 SCORE QUESTION: ${score}/100</strong><br>
                        <strong>🎯 QI ESTIMÉ POUR CETTE RÉPONSE: ${Math.min(300, 100 + score * 2)}</strong>
                    `;
                    
                    // Mise à jour du score total
                    const qiEstime = Math.min(400, 100 + (currentScore / questionsAnswered));
                    document.getElementById('score').textContent = `🧠 QI: ${qiEstime.toFixed(0)}`;
                    
                } else {
                    responseArea.innerHTML = `<strong>❌ ERREUR:</strong> ${data.error || 'Erreur inconnue'}`;
                }
            } catch (error) {
                responseArea.innerHTML = `<strong>❌ ERREUR DE CONNEXION:</strong> ${error.message}`;
            }
        }

        function calculerScore(reponse, numeroQuestion) {
            let score = 0;
            const longueur = reponse.length;
            
            // Score de base selon la longueur et la complexité
            if (longueur > 2000) score += 30;
            else if (longueur > 1000) score += 20;
            else if (longueur > 500) score += 10;
            
            // Mots-clés spécialisés selon la question
            const motsCles = {
                1: ['riemann', 'zeta', 'zeros', 'trivial', 'analytique', 'euler', 'prime', 'fonction'],
                2: ['quantique', 'superposition', 'mesure', 'probabilité', 'relativité', 'gravitationnel'],
                3: ['godel', 'peano', 'arithmétique', 'indécidable', 'preuve', 'consistance'],
                4: ['dimensions', 'calabi-yau', 'compactification', 'instantons', 'couplage'],
                5: ['conscience', 'turing', 'computation', 'church', 'multiverse', 'décohérence']
            };
            
            const motsQuestion = motsCles[numeroQuestion] || [];
            const reponseMin = reponse.toLowerCase();
            
            motsQuestion.forEach(mot => {
                if (reponseMin.includes(mot)) score += 8;
            });
            
            // Bonus pour formules mathématiques
            if (reponse.includes('∫') || reponse.includes('Σ') || reponse.includes('∀') || reponse.includes('∃')) score += 15;
            
            // Bonus pour références avancées
            if (reponse.includes('théorème') || reponse.includes('lemme') || reponse.includes('conjecture')) score += 10;
            
            return Math.min(100, score);
        }

        async function lancerTestComplet() {
            if (confirm('🔥 ATTENTION! Vous allez lancer le test QI le plus difficile jamais créé.\n\nCela va tester LOUNA-AI sur 5 questions de niveau GÉNIE ABSOLU.\n\nDurée estimée: 30-60 minutes\n\nÊtes-vous prêt?')) {
                for (let i = 1; i <= 5; i++) {
                    await testerQuestion(i, 'complet');
                    await new Promise(resolve => setTimeout(resolve, 3000)); // Pause entre questions
                }
                
                const qiFinal = Math.min(400, 100 + (currentScore / questionsAnswered));
                alert(`🎉 TEST COMPLET TERMINÉ!\n\n📊 Score total: ${currentScore}/${questionsAnswered * 100}\n🧠 QI FINAL ESTIMÉ: ${qiFinal.toFixed(0)}\n\n${qiFinal > 300 ? '🏆 GÉNIE ABSOLU!' : qiFinal > 200 ? '🌟 GÉNIE CONFIRMÉ!' : '🧠 INTELLIGENCE SUPÉRIEURE!'}`);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 Test QI Ultra-Poussé chargé');
        });
    </script>
</body>
</html>
