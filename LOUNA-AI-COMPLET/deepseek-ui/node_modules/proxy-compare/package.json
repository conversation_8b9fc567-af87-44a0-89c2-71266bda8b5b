{"name": "proxy-compare", "description": "Compare two objects using accessed properties with Proxy", "version": "3.0.1", "type": "module", "author": "<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/dai-shi/proxy-compare.git"}, "source": "./src/index.ts", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "sideEffects": false, "files": ["src", "dist"], "packageManager": "pnpm@8.15.0", "scripts": {"compile": "rm -rf dist && pnpm run '/^compile:.*/'", "compile:esm": "tsc -p tsconfig.esm.json", "compile:cjs": "tsc -p tsconfig.cjs.json && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json", "test": "pnpm run '/^test:.*/'", "test:format": "prettier -c .", "test:lint": "eslint .", "test:types": "tsc -p . --noEmit", "test:spec": "vitest run", "apidoc": "documentation readme src --section API --markdown-toc false --parse-extension ts"}, "keywords": ["proxy", "compare", "equal", "shallowequal", "deepequal"], "license": "MIT", "prettier": {"singleQuote": true}, "devDependencies": {"@types/node": "^22.9.1", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "documentation": "^14.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "prettier": "^3.3.3", "ts-expect": "^1.3.0", "typescript": "^5.6.3", "vite": "^5.4.11", "vitest": "^2.1.5"}}