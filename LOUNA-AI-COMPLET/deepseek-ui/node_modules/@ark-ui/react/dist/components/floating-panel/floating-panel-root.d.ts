import { ReactNode } from 'react';
import { UsePresenceProps } from '../presence';
import { UseFloatingPanelProps } from './use-floating-panel';
export interface FloatingPanelRootBaseProps extends UseFloatingPanelProps, UsePresenceProps {
}
export interface FloatingPanelRootProps extends FloatingPanelRootBaseProps {
    children?: ReactNode;
}
export declare const FloatingPanelRoot: (props: FloatingPanelRootProps) => import("react/jsx-runtime").JSX.Element;
