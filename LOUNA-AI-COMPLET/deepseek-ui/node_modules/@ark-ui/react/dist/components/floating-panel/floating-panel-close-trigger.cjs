'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const jsxRuntime = require('react/jsx-runtime');
const react$1 = require('@zag-js/react');
const react = require('react');
const factory = require('../factory.cjs');
const useFloatingPanelContext = require('./use-floating-panel-context.cjs');

const FloatingPanelCloseTrigger = react.forwardRef((props, ref) => {
  const floatingPanel = useFloatingPanelContext.useFloatingPanelContext();
  const mergedProps = react$1.mergeProps(floatingPanel.getCloseTriggerProps(), props);
  return /* @__PURE__ */ jsxRuntime.jsx(factory.ark.button, { ...mergedProps, ref });
});
FloatingPanelCloseTrigger.displayName = "FloatingPanelCloseTrigger";

exports.FloatingPanelCloseTrigger = FloatingPanelCloseTrigger;
