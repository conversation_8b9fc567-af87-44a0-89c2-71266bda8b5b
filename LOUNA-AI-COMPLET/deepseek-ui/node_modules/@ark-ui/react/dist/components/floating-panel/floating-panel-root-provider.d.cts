import { ReactNode } from 'react';
import { UsePresenceProps } from '../presence';
import { UseFloatingPanelReturn } from './use-floating-panel';
interface RootProviderProps {
    value: UseFloatingPanelReturn;
}
export interface FloatingPanelRootProviderBaseProps extends RootProviderProps, Omit<UsePresenceProps, 'present'> {
}
export interface FloatingPanelRootProviderProps extends FloatingPanelRootProviderBaseProps {
    children?: ReactNode;
}
export declare const FloatingPanelRootProvider: (props: FloatingPanelRootProviderProps) => import("react/jsx-runtime").JSX.Element;
export {};
