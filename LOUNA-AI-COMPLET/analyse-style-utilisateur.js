/**
 * ANALYSE DU STYLE DE COMMUNICATION DE L'UTILISATEUR
 * Basé sur nos échanges pour améliorer LOUNA-AI
 */

class AnalyseStyleUtilisateur {
    constructor() {
        this.expressionsUtilisateur = this.analyserNosEchanges();
        this.styleDetecte = this.determinerStyle();
    }

    analyserNosEchanges() {
        return {
            // EXPRESSIONS CARACTÉRISTIQUES DÉTECTÉES
            salutations: [
                "bonjour",
                // Style direct et efficace
            ],
            
            // STYLE DE COMMUNICATION
            caracteristiques: [
                "Direct et précis",
                "Utilise l'impératif : 'regarde', 'fais lui passer', 'corrige'",
                "Phrases courtes et efficaces",
                "Pas de politesses excessives",
                "Va droit au but",
                "Utilise 'lui' pour parler de l'agent (personnification)",
                "Demandes d'action concrètes"
            ],

            // EXPRESSIONS SPÉCIFIQUES
            expressions_typiques: [
                "regarde nos échange",
                "fais lui passer un test",
                "posse lui tout sorte des questions", 
                "corrige si besoin",
                "j'espère que tu as mis",
                "dans ses expressions languis" // faute de frappe naturelle
            ],

            // STYLE TECHNIQUE
            preferences_techniques: [
                "Veut des tests complets",
                "Demande des corrections immédiates",
                "Préfère l'action à la théorie",
                "Veut voir des résultats concrets",
                "Apprécie les rapports détaillés"
            ],

            // NIVEAU DE FAMILIARITÉ
            niveau_familiarite: "Élevé - tutoie, donne des ordres directs",
            
            // ATTENTES
            attentes: [
                "Tests poussés et complets",
                "Corrections automatiques",
                "Validation par l'usage",
                "Amélioration continue",
                "Résultats immédiats"
            ]
        };
    }

    determinerStyle() {
        return {
            type: "DIRECT_EFFICACE",
            description: "Communication directe, orientée action, sans fioritures",
            adaptations_pour_louna: [
                "Réponses concises et directes",
                "Pas de sur-politesse",
                "Actions concrètes proposées",
                "Résultats mesurables",
                "Ton décontracté mais professionnel"
            ]
        };
    }

    genererQuestionsTestPersonnalisees() {
        // Questions basées sur le style de l'utilisateur
        return [
            // STYLE DIRECT
            {
                question: "bonjour",
                attente: "Réponse directe, pas trop longue",
                type: "salutation_directe"
            },
            {
                question: "fais-moi un calcul : 15 × 7",
                attente: "Réponse immédiate avec résultat",
                type: "demande_directe"
            },
            {
                question: "regarde ça et dis-moi ce que tu en penses",
                attente: "Analyse directe sans détours",
                type: "analyse_directe"
            },
            {
                question: "corrige cette phrase : 'Je suis une IA qui calcule'",
                attente: "Correction immédiate en langage naturel",
                type: "correction_directe"
            },
            {
                question: "explique-moi rapidement la photosynthèse",
                attente: "Explication concise, pas de roman",
                type: "explication_concise"
            },
            {
                question: "que peux-tu faire concrètement ?",
                attente: "Liste claire et précise",
                type: "capacites_concretes"
            },
            {
                question: "résous ce problème : un escargot monte un mur",
                attente: "Solution directe avec raisonnement",
                type: "probleme_complexe"
            },
            {
                question: "j'ai besoin d'aide pour programmer",
                attente: "Aide concrète, pas de théorie",
                type: "aide_pratique"
            }
        ];
    }

    evaluerReponseSelonStyle(question, reponse) {
        const criteres = {
            concision: this.evaluerConcision(reponse),
            directe: this.evaluerDirecte(reponse),
            actionnable: this.evaluerActionnable(reponse),
            naturel: this.evaluerNaturel(reponse),
            pertinence: this.evaluerPertinence(question, reponse)
        };

        const score = Object.values(criteres).reduce((sum, val) => sum + val, 0) / 5;
        
        return {
            score: Math.round(score),
            criteres: criteres,
            recommandations: this.genererRecommandations(criteres)
        };
    }

    evaluerConcision(reponse) {
        // Préfère les réponses de 50-200 caractères pour les questions simples
        const longueur = reponse.length;
        if (longueur < 30) return 60; // Trop court
        if (longueur < 100) return 100; // Parfait
        if (longueur < 200) return 90; // Bien
        if (longueur < 300) return 70; // Acceptable
        return 50; // Trop long
    }

    evaluerDirecte(reponse) {
        const motsDirects = ['directement', 'simplement', 'concrètement', 'en fait', 'bon'];
        const motsIndirects = ['peut-être', 'il semblerait', 'on pourrait dire', 'en quelque sorte'];
        
        let score = 80;
        for (const mot of motsDirects) {
            if (reponse.toLowerCase().includes(mot)) score += 5;
        }
        for (const mot of motsIndirects) {
            if (reponse.toLowerCase().includes(mot)) score -= 10;
        }
        
        return Math.max(0, Math.min(100, score));
    }

    evaluerActionnable(reponse) {
        const motsActionnables = ['voici', 'voilà', 'fais', 'utilise', 'essaie', 'regarde'];
        let score = 70;
        
        for (const mot of motsActionnables) {
            if (reponse.toLowerCase().includes(mot)) score += 10;
        }
        
        return Math.min(100, score);
    }

    evaluerNaturel(reponse) {
        const expressionsNaturelles = ['du coup', 'en fait', 'bon', 'alors', 'franchement'];
        const expressionsRobotiques = ['selon mes données', 'je suis une IA', 'mes algorithmes'];
        
        let score = 80;
        for (const expr of expressionsNaturelles) {
            if (reponse.toLowerCase().includes(expr)) score += 5;
        }
        for (const expr of expressionsRobotiques) {
            if (reponse.toLowerCase().includes(expr)) score -= 20;
        }
        
        return Math.max(0, Math.min(100, score));
    }

    evaluerPertinence(question, reponse) {
        // Évaluation basique de la pertinence
        const motsQuestion = question.toLowerCase().split(' ');
        const motsReponse = reponse.toLowerCase();
        
        let motsCommuns = 0;
        for (const mot of motsQuestion) {
            if (mot.length > 3 && motsReponse.includes(mot)) {
                motsCommuns++;
            }
        }
        
        return Math.min(100, (motsCommuns / Math.max(1, motsQuestion.length)) * 100 + 50);
    }

    genererRecommandations(criteres) {
        const recommandations = [];
        
        if (criteres.concision < 80) {
            recommandations.push("Être plus concis dans les réponses");
        }
        if (criteres.directe < 80) {
            recommandations.push("Aller plus directement au but");
        }
        if (criteres.actionnable < 80) {
            recommandations.push("Proposer des actions concrètes");
        }
        if (criteres.naturel < 80) {
            recommandations.push("Utiliser plus d'expressions naturelles");
        }
        
        return recommandations;
    }
}

module.exports = AnalyseStyleUtilisateur;
