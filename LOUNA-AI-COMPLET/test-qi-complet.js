/**
 * TEST QI COMPLET POUR REEL LOUNA AI V5
 * Évaluation complète des capacités intellectuelles
 */

const MoteurSimple = require('./moteur-simple-fonctionnel.js');

class TestQIComplet {
    constructor() {
        this.moteur = new MoteurSimple();
        this.questions = [];
        this.reponses = [];
        this.score = 0;
        this.scoreMax = 0;
        this.initierQuestions();
    }

    initierQuestions() {
        this.questions = [
            // NIVEAU 1 - SALUTATIONS ET BASE
            {
                niveau: 1,
                question: "bonjour",
                reponseAttendue: "Bonjour ! Je suis LOUNA-AI",
                points: 5,
                type: "Salutation"
            },
            {
                niveau: 1,
                question: "qui es-tu ?",
                reponseAttendue: "Je suis LOUNA-AI, créée par <PERSON>-<PERSON>",
                points: 5,
                type: "Identité"
            },

            // NIVEAU 2 - CALCULS SIMPLES
            {
                niveau: 2,
                question: "2 + 3",
                reponseAttendue: "2 + 3 = 5",
                points: 10,
                type: "Calcul simple"
            },
            {
                niveau: 2,
                question: "7 × 8",
                reponseAttendue: "7 × 8 = 56",
                points: 10,
                type: "Multiplication"
            },
            {
                niveau: 2,
                question: "15 - 7",
                reponseAttendue: "15 - 7 = 8",
                points: 10,
                type: "Soustraction"
            },

            // NIVEAU 3 - LOGIQUE
            {
                niveau: 3,
                question: "Tous les chats sont des mammifères. Felix est un chat. Que peut-on conclure ?",
                reponseAttendue: "Felix est un mammifère",
                points: 20,
                type: "Syllogisme"
            },

            // NIVEAU 4 - SUITES MATHÉMATIQUES
            {
                niveau: 4,
                question: "Quelle est la suite logique : 1, 1, 2, 3, 5, 8, 13, ?",
                reponseAttendue: "21",
                points: 25,
                type: "Suite Fibonacci"
            },

            // NIVEAU 5 - PROBLÈMES COMPLEXES
            {
                niveau: 5,
                question: "Un escargot monte un mur de 10m. Il monte 3m le jour et descend 2m la nuit. En combien de jours atteint-il le sommet ?",
                reponseAttendue: "8 jours",
                points: 30,
                type: "Problème complexe"
            },

            // NIVEAU 6 - CAPACITÉS
            {
                niveau: 6,
                question: "Que peux-tu faire ?",
                reponseAttendue: "calculs mathématiques",
                points: 15,
                type: "Auto-évaluation"
            }
        ];

        this.scoreMax = this.questions.reduce((total, q) => total + q.points, 0);
    }

    async executerTest() {
        console.log('🧠 TEST QI COMPLET - REEL LOUNA AI V5');
        console.log('=====================================');
        console.log(`📊 ${this.questions.length} questions - Score maximum: ${this.scoreMax} points\n`);

        for (let i = 0; i < this.questions.length; i++) {
            const question = this.questions[i];
            console.log(`🔍 Question ${i + 1}/${this.questions.length} (Niveau ${question.niveau}) - ${question.type}`);
            console.log(`❓ ${question.question}`);

            try {
                const resultat = this.moteur.penser(question.question);
                
                if (resultat && resultat.reponse) {
                    console.log(`🤖 Réponse: ${resultat.reponse}`);
                    
                    // Évaluer la réponse
                    const scoreQuestion = this.evaluerReponse(question, resultat.reponse);
                    this.score += scoreQuestion;
                    
                    console.log(`📊 Score: ${scoreQuestion}/${question.points} points`);
                    
                    this.reponses.push({
                        question: question.question,
                        reponse: resultat.reponse,
                        score: scoreQuestion,
                        scoreMax: question.points,
                        niveau: question.niveau
                    });
                } else {
                    console.log(`❌ Aucune réponse`);
                    this.reponses.push({
                        question: question.question,
                        reponse: 'Aucune réponse',
                        score: 0,
                        scoreMax: question.points,
                        niveau: question.niveau
                    });
                }
                
                console.log('─'.repeat(50));
                
            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
                this.reponses.push({
                    question: question.question,
                    reponse: `Erreur: ${error.message}`,
                    score: 0,
                    scoreMax: question.points,
                    niveau: question.niveau
                });
            }
        }

        this.genererRapport();
    }

    evaluerReponse(question, reponse) {
        const reponseNormalisee = reponse.toLowerCase();
        const attendueNormalisee = question.reponseAttendue.toLowerCase();

        // Vérification exacte ou partielle
        if (reponseNormalisee.includes(attendueNormalisee)) {
            return question.points; // Score complet
        }

        // Vérifications spécifiques par type
        switch (question.type) {
            case 'Salutation':
                if (reponseNormalisee.includes('bonjour') && reponseNormalisee.includes('louna')) {
                    return question.points;
                }
                break;

            case 'Identité':
                if (reponseNormalisee.includes('louna') && reponseNormalisee.includes('jean-luc')) {
                    return question.points;
                }
                break;

            case 'Calcul simple':
            case 'Multiplication':
            case 'Soustraction':
                // Extraire le résultat numérique
                const resultatMatch = reponse.match(/=\s*(\d+)/);
                if (resultatMatch) {
                    const resultatCalcule = parseInt(resultatMatch[1]);
                    const resultatAttendu = question.reponseAttendue.match(/=\s*(\d+)/);
                    if (resultatAttendu && resultatCalcule === parseInt(resultatAttendu[1])) {
                        return question.points;
                    }
                }
                break;

            case 'Syllogisme':
                if (reponseNormalisee.includes('felix') && reponseNormalisee.includes('mammifère')) {
                    return question.points;
                }
                break;

            case 'Suite Fibonacci':
                if (reponseNormalisee.includes('21')) {
                    return question.points;
                }
                break;

            case 'Problème complexe':
                if (reponseNormalisee.includes('8') && reponseNormalisee.includes('jour')) {
                    return question.points;
                }
                break;

            case 'Auto-évaluation':
                if (reponseNormalisee.includes('calcul') || reponseNormalisee.includes('raisonnement')) {
                    return question.points;
                }
                break;
        }

        // Score partiel si réponse présente mais incorrecte
        return Math.floor(question.points * 0.2);
    }

    genererRapport() {
        console.log('\n🎯 RAPPORT FINAL - TEST QI COMPLET');
        console.log('==================================');
        
        const pourcentage = Math.round((this.score / this.scoreMax) * 100);
        const qiEstime = Math.round(100 + (pourcentage - 50) * 2); // Formule QI approximative
        
        console.log(`📊 Score total: ${this.score}/${this.scoreMax} points (${pourcentage}%)`);
        console.log(`🧠 QI estimé: ${qiEstime}`);
        
        // Classification QI
        let classification = '';
        if (qiEstime >= 140) classification = 'Génie';
        else if (qiEstime >= 130) classification = 'Très supérieur';
        else if (qiEstime >= 120) classification = 'Supérieur';
        else if (qiEstime >= 110) classification = 'Moyen supérieur';
        else if (qiEstime >= 90) classification = 'Moyen';
        else classification = 'Sous la moyenne';
        
        console.log(`🎯 Classification: ${classification}`);
        
        // Détail par niveau
        console.log('\n📈 DÉTAIL PAR NIVEAU:');
        for (let niveau = 1; niveau <= 6; niveau++) {
            const questionsNiveau = this.reponses.filter(r => r.niveau === niveau);
            const scoreNiveau = questionsNiveau.reduce((total, r) => total + r.score, 0);
            const scoreMaxNiveau = questionsNiveau.reduce((total, r) => total + r.scoreMax, 0);
            const pourcentageNiveau = scoreMaxNiveau > 0 ? Math.round((scoreNiveau / scoreMaxNiveau) * 100) : 0;
            
            console.log(`Niveau ${niveau}: ${scoreNiveau}/${scoreMaxNiveau} points (${pourcentageNiveau}%)`);
        }
        
        // Recommandations
        console.log('\n💡 RECOMMANDATIONS:');
        if (pourcentage >= 80) {
            console.log('✅ Excellent! Le système fonctionne parfaitement.');
        } else if (pourcentage >= 60) {
            console.log('⚠️ Bon niveau, quelques améliorations possibles.');
        } else {
            console.log('❌ Corrections nécessaires pour améliorer les performances.');
        }
        
        console.log('\n🎉 TEST QI TERMINÉ !');
    }
}

// Exécution du test
if (require.main === module) {
    const test = new TestQIComplet();
    test.executerTest().catch(console.error);
}

module.exports = TestQIComplet;
