#!/bin/bash

# 🚀 CRÉATEUR D'APPLICATION LOUNA-AI
# Ce script crée une vraie application macOS pour LOUNA-AI

echo "🎨 CRÉATION DE L'APPLICATION LOUNA-AI"
echo "====================================="

APP_NAME="LOUNA-AI Complete"
APP_DIR="$APP_NAME.app"
CONTENTS_DIR="$APP_DIR/Contents"
MACOS_DIR="$CONTENTS_DIR/MacOS"
RESOURCES_DIR="$CONTENTS_DIR/Resources"

# Supprimer l'ancienne app si elle existe
if [ -d "$APP_DIR" ]; then
    echo "🗑️  Suppression de l'ancienne application..."
    rm -rf "$APP_DIR"
fi

# Créer la structure de l'app
echo "📁 Création de la structure de l'application..."
mkdir -p "$MACOS_DIR"
mkdir -p "$RESOURCES_DIR"

# C<PERSON><PERSON> le fichier Info.plist
echo "📄 Création du fichier Info.plist..."
cat > "$CONTENTS_DIR/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>LOUNA-AI Complete</string>
    <key>CFBundleIdentifier</key>
    <string>com.louna-ai.complete</string>
    <key>CFBundleName</key>
    <string>LOUNA-AI Complete</string>
    <key>CFBundleDisplayName</key>
    <string>LOUNA-AI Complete</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>LOUA</string>
    <key>CFBundleIconFile</key>
    <string>louna-icon</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSUIElement</key>
    <false/>
</dict>
</plist>
EOF

# Créer le script exécutable principal
echo "⚙️  Création du script exécutable..."
cat > "$MACOS_DIR/LOUNA-AI Complete" << 'EOF'
#!/bin/bash

# Script principal de LOUNA-AI Complete
export PATH="/usr/local/bin:/opt/homebrew/bin:$PATH"

# Fonction d'affichage
show_notification() {
    osascript -e "display notification \"$1\" with title \"LOUNA-AI\" sound name \"Glass\""
}

# Aller dans le répertoire LOUNA-AI
LOUNA_DIR="/Volumes/ALDO et MIM/LOUNA-AI-COMPLET"
if [ ! -d "$LOUNA_DIR" ]; then
    show_notification "❌ Répertoire LOUNA-AI non trouvé"
    exit 1
fi

cd "$LOUNA_DIR"

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    show_notification "❌ Node.js non installé"
    exit 1
fi

# Notification de démarrage
show_notification "🚀 Démarrage de LOUNA-AI..."

# Arrêter les processus existants
pkill -f "serveur-interface-complete.js" 2>/dev/null
sleep 2

# Démarrer le serveur
node serveur-interface-complete.js &
SERVER_PID=$!

# Attendre le démarrage
sleep 6

# Vérifier que le serveur fonctionne
if curl -s http://localhost:3000/api/status > /dev/null 2>&1; then
    show_notification "✅ LOUNA-AI démarré avec succès"
    # Ouvrir l'interface
    open "http://localhost:3000/interface-louna-complete.html"
else
    show_notification "❌ Échec du démarrage de LOUNA-AI"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

# Créer un fichier PID pour pouvoir arrêter le serveur plus tard
echo $SERVER_PID > /tmp/louna-ai-server.pid

# Notification de succès
show_notification "🎉 LOUNA-AI Interface Complète active !"
EOF

# Rendre le script exécutable
chmod +x "$MACOS_DIR/LOUNA-AI Complete"

# Créer une icône simple (optionnel)
echo "🎨 Création de l'icône..."
# On peut créer une icône basique ou utiliser une existante

echo ""
echo "✅ APPLICATION LOUNA-AI CRÉÉE AVEC SUCCÈS !"
echo ""
echo "📱 Application: $APP_DIR"
echo "🎯 Pour utiliser l'application:"
echo "   1. Double-cliquez sur '$APP_DIR'"
echo "   2. L'interface s'ouvrira automatiquement"
echo "   3. LOUNA-AI sera accessible à http://localhost:3000"
echo ""
echo "📋 Fonctionnalités de l'app:"
echo "   • Démarrage automatique du serveur"
echo "   • Notifications macOS"
echo "   • Ouverture automatique de l'interface"
echo "   • Icône dans le Dock"
echo ""

# Proposer de déplacer l'app vers Applications
read -p "🤔 Voulez-vous déplacer l'app vers le dossier Applications ? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -d "/Applications" ]; then
        echo "📦 Déplacement vers /Applications..."
        cp -R "$APP_DIR" "/Applications/"
        echo "✅ Application installée dans /Applications"
        echo "🎯 Vous pouvez maintenant lancer LOUNA-AI depuis Launchpad !"
    else
        echo "❌ Dossier Applications non accessible"
    fi
fi

echo ""
echo "🎉 LOUNA-AI Complete est prêt à être utilisé !"
