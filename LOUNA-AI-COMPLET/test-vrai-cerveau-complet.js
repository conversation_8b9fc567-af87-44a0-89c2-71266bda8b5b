#!/usr/bin/env node

/**
 * 🧠 TEST VRAI CERVEAU COMPLET
 * Vérification complète : Vraies températures CPU + Agent verrouillé + Mouvement fluide
 * AUCUNE SIMULATION - QUE DU CODE RÉEL ET FONCTIONNEL
 */

const SystemeUnifieFluideReel = require('./systeme-unifie-fluide-reel.js');

console.log('🧠 TEST VRAI CERVEAU COMPLET');
console.log('============================');

async function testerVraiCerveauComplet() {
    try {
        // Initialiser le système unifié complet
        console.log('\n🚀 Initialisation du vrai cerveau complet...');
        const systeme = new SystemeUnifieFluideReel();
        
        // Attendre l'initialisation complète
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // Test 1: Vérifier vraies températures CPU
        console.log('\n🌡️ TEST 1: VRAIES TEMPÉRATURES CPU');
        const stats_initiales = systeme.obtenirStatistiquesCompletes();
        
        if (stats_initiales.memoire.temperature_cpu) {
            console.log(`✅ Température CPU détectée: ${stats_initiales.memoire.temperature_cpu.actuelle.toFixed(2)}°C`);
            console.log(`📊 Variation: ${stats_initiales.memoire.temperature_cpu.variation.toFixed(2)}°C`);
            console.log(`📈 Stabilité: ${(stats_initiales.memoire.temperature_cpu.stabilite * 100).toFixed(1)}%`);
            console.log(`🔄 Historique: ${stats_initiales.memoire.temperature_cpu.historique_taille} mesures`);
            console.log(`⚡ Influence active: ${stats_initiales.memoire.temperature_cpu.influence_active}`);
        } else {
            console.log('❌ Température CPU non détectée');
        }
        
        // Test 2: Vérifier agent verrouillé
        console.log('\n🤖 TEST 2: AGENT VERROUILLÉ');
        console.log(`🔒 Agent verrouillé: ${stats_initiales.agent_19gb.verrouille}`);
        console.log(`✅ Agent actif: ${stats_initiales.agent_19gb.actif}`);
        console.log(`🔄 Tentatives reconnexion: ${stats_initiales.agent_19gb.tentatives_reconnexion}`);
        console.log(`💓 Keep-alive: ${stats_initiales.agent_19gb.keep_alive}`);
        console.log(`⏱️ Timeout: ${stats_initiales.agent_19gb.timeout_requete}ms`);
        
        // Test 3: Interroger agent avec verrouillage
        console.log('\n💬 TEST 3: INTERROGATION AGENT VERROUILLÉ');
        const requete_test = "Explique brièvement ce qu'est l'intelligence artificielle";
        console.log(`📝 Requête: "${requete_test}"`);
        
        const debut_requete = Date.now();
        const reponse_agent = await systeme.interrogerAgent19GB(requete_test);
        const duree_requete = Date.now() - debut_requete;
        
        if (reponse_agent) {
            console.log(`✅ Réponse obtenue en ${duree_requete}ms`);
            console.log(`📄 Longueur: ${reponse_agent.length} caractères`);
            console.log(`📝 Aperçu: "${reponse_agent.substring(0, 100)}..."`);
        } else {
            console.log('❌ Aucune réponse de l\'agent');
        }
        
        // Test 4: Vérifier zones basées sur vraie température CPU
        console.log('\n🌡️ TEST 4: ZONES BASÉES SUR VRAIE TEMPÉRATURE CPU');
        
        // Prendre quelques mémoires échantillon
        const echantillon = Array.from(systeme.memoire.memoires.values()).slice(0, 3);
        
        console.log('📊 Températures des mémoires (basées sur CPU réel):');
        echantillon.forEach((memoire, i) => {
            const zone_cerebrale = systeme.memoire.calculerTemperatureZoneCerebrale(
                systeme.memoire.determinerZoneCerebrale(memoire.contenu, memoire.source)
            );
            
            console.log(`  • Mémoire ${i+1}:`);
            console.log(`    - Température: ${memoire.temperature.toFixed(2)}°C`);
            console.log(`    - Zone: ${zone_cerebrale.nom}`);
            console.log(`    - CPU source: ${zone_cerebrale.temp_cpu_source.toFixed(2)}°C`);
            console.log(`    - Offset appliqué: ${zone_cerebrale.offset_applique}°C`);
            console.log(`    - Temp calculée: ${zone_cerebrale.temperature_base.toFixed(2)}°C`);
        });
        
        // Test 5: Traitement unifié complet
        console.log('\n🌊 TEST 5: TRAITEMENT UNIFIÉ COMPLET');
        const requete_unifiee = "Quelle est la température de mon processeur ?";
        console.log(`📝 Requête unifiée: "${requete_unifiee}"`);
        
        const debut_unifie = Date.now();
        const reponse_unifiee = await systeme.traiterRequeteUnifiee(requete_unifiee, 'test');
        const duree_unifiee = Date.now() - debut_unifie;
        
        if (reponse_unifiee.success) {
            console.log(`✅ Traitement unifié réussi en ${duree_unifiee}ms`);
            console.log(`🧠 Résultats mémoire: ${reponse_unifiee.resultats_memoire.length}`);
            console.log(`🤖 Réponse agent: ${reponse_unifiee.reponse_agent ? 'Oui' : 'Non'}`);
            console.log(`⚡ Accélération: ${reponse_unifiee.acceleration.boost_performance ? 'Oui' : 'Non'}`);
            console.log(`🌊 Fluidité active: ${reponse_unifiee.etat_fluide.fluidite_active}`);
            console.log(`📊 Performance: ${reponse_unifiee.performance_globale.toFixed(1)}%`);
            
            // Afficher température CPU dans la réponse
            if (reponse_unifiee.reponse_agent && reponse_unifiee.reponse_agent.includes('température')) {
                console.log(`🌡️ Agent a mentionné la température !`);
            }
        } else {
            console.log('❌ Traitement unifié échoué');
        }
        
        // Test 6: Mouvement fluide basé sur CPU
        console.log('\n🌊 TEST 6: MOUVEMENT FLUIDE BASÉ SUR CPU');
        
        const mouvement_initial = stats_initiales.memoire.mouvement_vivant;
        console.log(`⚡ Vitesse initiale: ${mouvement_initial.vitesse_curseur.toFixed(6)}`);
        console.log(`🎯 Position initiale: ${stats_initiales.memoire.curseurThermique.toFixed(6)}`);
        
        // Attendre mouvement
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const stats_apres = systeme.obtenirStatistiquesCompletes();
        const mouvement_final = stats_apres.memoire.mouvement_vivant;
        
        console.log(`⚡ Vitesse finale: ${mouvement_final.vitesse_curseur.toFixed(6)}`);
        console.log(`🎯 Position finale: ${stats_apres.memoire.curseurThermique.toFixed(6)}`);
        
        const deplacement = Math.abs(stats_apres.memoire.curseurThermique - stats_initiales.memoire.curseurThermique);
        const variation_vitesse = Math.abs(mouvement_final.vitesse_curseur - mouvement_initial.vitesse_curseur);
        
        console.log(`📏 Déplacement détecté: ${deplacement.toFixed(6)}`);
        console.log(`📈 Variation vitesse: ${variation_vitesse.toFixed(6)}`);
        console.log(`🔄 Cycles exécutés: ${mouvement_final.cycles_automatiques - mouvement_initial.cycles_automatiques}`);
        
        // Test 7: Performance globale
        console.log('\n📊 TEST 7: PERFORMANCE GLOBALE');
        
        const stats_finales = systeme.obtenirStatistiquesCompletes();
        console.log(`🎯 Performance système: ${stats_finales.systeme.performance_globale.toFixed(1)}%`);
        console.log(`🧠 Mémoires: ${stats_finales.memoire.totalEntries}`);
        console.log(`🚀 Accélérateurs: ${stats_finales.accelerateurs.statistiques.actifs}/${stats_finales.accelerateurs.statistiques.total_accelerateurs}`);
        console.log(`🤖 Agent: ${stats_finales.agent_19gb.actif ? 'Actif' : 'Inactif'}`);
        console.log(`🌡️ CPU: ${stats_finales.memoire.temperature_cpu.actuelle.toFixed(2)}°C`);
        console.log(`🌊 Fluidité: ${stats_finales.systeme.etat_fluide.fluidite_active}`);
        
        // Résumé final
        console.log('\n🎯 RÉSUMÉ FINAL');
        console.log('===============');
        
        const tests_reussis = [
            stats_finales.memoire.temperature_cpu && stats_finales.memoire.temperature_cpu.influence_active, // CPU réel
            stats_finales.agent_19gb.verrouille && stats_finales.agent_19gb.actif, // Agent verrouillé
            reponse_agent !== null, // Agent répond
            echantillon.length > 0 && echantillon[0].temperature > 0, // Zones CPU
            reponse_unifiee.success, // Traitement unifié
            deplacement > 0.001, // Mouvement fluide
            stats_finales.systeme.performance_globale > 60 // Performance
        ];
        
        console.log(`• Température CPU réelle: ${tests_reussis[0] ? '✅' : '❌'}`);
        console.log(`• Agent verrouillé: ${tests_reussis[1] ? '✅' : '❌'}`);
        console.log(`• Agent répond: ${tests_reussis[2] ? '✅' : '❌'}`);
        console.log(`• Zones basées CPU: ${tests_reussis[3] ? '✅' : '❌'}`);
        console.log(`• Traitement unifié: ${tests_reussis[4] ? '✅' : '❌'}`);
        console.log(`• Mouvement fluide: ${tests_reussis[5] ? '✅' : '❌'}`);
        console.log(`• Performance globale: ${tests_reussis[6] ? '✅' : '❌'}`);
        
        const score = tests_reussis.filter(t => t).length;
        console.log(`\n🏆 SCORE FINAL: ${score}/7 tests réussis`);
        
        if (score === 7) {
            console.log('🎉 VRAI CERVEAU COMPLET PARFAIT !');
            console.log('🌡️ Toutes les températures sont RÉELLES (CPU)');
            console.log('🤖 Agent VERROUILLÉ et toujours connecté');
            console.log('🌊 Mouvement fluide basé sur CPU réel');
            console.log('🧠 Cerveau artificiel totalement fonctionnel');
        } else if (score >= 5) {
            console.log('✅ Vrai cerveau fonctionnel');
        } else {
            console.log('❌ Vrai cerveau à améliorer');
        }
        
        console.log('\n🧠🌡️ TEST VRAI CERVEAU COMPLET TERMINÉ ! 🌡️🧠');
        
    } catch (error) {
        console.error('❌ Erreur test vrai cerveau:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testerVraiCerveauComplet();
