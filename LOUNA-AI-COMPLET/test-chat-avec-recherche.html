<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Test Chat avec Recherche Automatique</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .chat-container {
            border: 2px solid #333;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(5px);
            max-height: 500px;
            overflow-y: auto;
        }
        
        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .user-message {
            background: linear-gradient(135deg, #0066cc, #004499);
            text-align: right;
            margin-left: 20%;
        }
        
        .ai-message {
            background: linear-gradient(135deg, #006600, #004400);
            text-align: left;
            margin-right: 20%;
        }
        
        .search-message {
            background: linear-gradient(135deg, #cc6600, #994400);
            text-align: center;
            font-style: italic;
        }
        
        .error-message {
            background: linear-gradient(135deg, #cc0000, #990000);
            text-align: center;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        input[type="text"] {
            flex: 1;
            padding: 15px;
            border: 2px solid #333;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            font-size: 16px;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #0066cc;
            box-shadow: 0 0 10px rgba(0, 102, 204, 0.3);
        }
        
        button {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #0066cc, #004499);
            color: #fff;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: linear-gradient(135deg, #0088ff, #0066cc);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 102, 204, 0.3);
        }
        
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            text-align: center;
            backdrop-filter: blur(5px);
        }
        
        .questions-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .question-card {
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        .question-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .search-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #fff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            text-align: center;
            backdrop-filter: blur(5px);
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ff88;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Test Chat avec Recherche Automatique</h1>
        <p>Testez la recherche automatique quand LOUNA-AI ne connaît pas la réponse</p>
    </div>
    
    <div class="status" id="status">
        Prêt à tester la recherche automatique...
    </div>
    
    <div class="stats" id="stats">
        <div class="stat-card">
            <div class="stat-value" id="total-questions">0</div>
            <div>Questions posées</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="recherches-declenchees">0</div>
            <div>Recherches déclenchées</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="reponses-enrichies">0</div>
            <div>Réponses enrichies</div>
        </div>
    </div>
    
    <div class="questions-test">
        <div class="question-card" onclick="poserQuestion('Quelle est la capitale du Bhoutan ?')">
            🏛️ Capitale du Bhoutan
        </div>
        <div class="question-card" onclick="poserQuestion('Comment fonctionne le processeur M4 d\'Apple ?')">
            💻 Processeur M4 Apple
        </div>
        <div class="question-card" onclick="poserQuestion('Qu\'est-ce que le framework Astro 4.0 ?')">
            🚀 Framework Astro 4.0
        </div>
        <div class="question-card" onclick="poserQuestion('Quelles sont les dernières découvertes sur Mars en 2024 ?')">
            🔴 Découvertes Mars 2024
        </div>
        <div class="question-card" onclick="poserQuestion('Comment installer Deno 2.0 ?')">
            🦕 Installation Deno 2.0
        </div>
        <div class="question-card" onclick="poserQuestion('Qu\'est-ce que la physique quantique ?')">
            ⚛️ Physique quantique
        </div>
    </div>
    
    <div class="chat-container" id="chat-container">
        <div class="message ai-message">
            🤖 Bonjour ! Je suis LOUNA-AI avec recherche automatique. Posez-moi une question que je ne connais pas et je chercherai automatiquement sur Internet !
        </div>
    </div>
    
    <div class="input-container">
        <input type="text" id="message-input" placeholder="Posez une question difficile..." onkeypress="handleKeyPress(event)">
        <button onclick="envoyerMessage()">Envoyer</button>
        <button onclick="testerToutesQuestions()">Test Auto</button>
        <button onclick="effacerChat()">Effacer</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const statusDiv = document.getElementById('status');
        
        let stats = {
            totalQuestions: 0,
            recherchesDeclenchees: 0,
            reponsesEnrichies: 0
        };
        
        function ajouterMessage(contenu, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.innerHTML = contenu;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function mettreAJourStatus(message, couleur = 'rgba(255, 255, 255, 0.1)') {
            statusDiv.textContent = message;
            statusDiv.style.background = couleur;
        }
        
        function mettreAJourStats() {
            document.getElementById('total-questions').textContent = stats.totalQuestions;
            document.getElementById('recherches-declenchees').textContent = stats.recherchesDeclenchees;
            document.getElementById('reponses-enrichies').textContent = stats.reponsesEnrichies;
        }
        
        async function envoyerMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            poserQuestion(message);
            messageInput.value = '';
        }
        
        async function poserQuestion(question) {
            // Afficher question utilisateur
            ajouterMessage(`👤 ${question}`, 'user');
            stats.totalQuestions++;
            mettreAJourStats();
            
            mettreAJourStatus('🤖 LOUNA-AI réfléchit...', 'rgba(0, 102, 204, 0.3)');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: question })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.response) {
                    // Vérifier si la réponse contient des informations de recherche
                    const contientRecherche = data.response.includes('🔍') || 
                                            data.response.includes('Informations trouvées sur Internet') ||
                                            data.response.includes('Source:');
                    
                    if (contientRecherche) {
                        stats.recherchesDeclenchees++;
                        stats.reponsesEnrichies++;
                        ajouterMessage(`🔍 Recherche automatique déclenchée !`, 'search');
                        mettreAJourStatus('✅ Réponse enrichie par recherche Internet !', 'rgba(0, 150, 0, 0.3)');
                    } else {
                        mettreAJourStatus('✅ Réponse depuis mémoire/agent', 'rgba(0, 150, 0, 0.3)');
                    }
                    
                    ajouterMessage(`🤖 ${data.response}`, 'ai');
                    mettreAJourStats();
                    
                } else {
                    ajouterMessage(`❌ Erreur: ${data.error || 'Réponse vide'}`, 'error');
                    mettreAJourStatus('❌ Erreur dans la réponse', 'rgba(204, 0, 0, 0.3)');
                }
                
            } catch (error) {
                console.error('Erreur:', error);
                ajouterMessage(`❌ Erreur de connexion: ${error.message}`, 'error');
                mettreAJourStatus('❌ Erreur de connexion', 'rgba(204, 0, 0, 0.3)');
            }
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                envoyerMessage();
            }
        }
        
        async function testerToutesQuestions() {
            const questions = [
                "Quelle est la capitale du Bhoutan ?",
                "Comment fonctionne le processeur M4 d'Apple ?",
                "Qu'est-ce que le framework Astro 4.0 ?",
                "Quelles sont les dernières découvertes sur Mars en 2024 ?",
                "Comment installer Deno 2.0 ?",
                "Qu'est-ce que la physique quantique ?"
            ];
            
            mettreAJourStatus('🧪 Test automatique en cours...', 'rgba(204, 102, 0, 0.3)');
            
            for (let i = 0; i < questions.length; i++) {
                const question = questions[i];
                
                mettreAJourStatus(`🧪 Test ${i+1}/${questions.length}: ${question.substring(0, 30)}...`, 'rgba(204, 102, 0, 0.3)');
                
                await poserQuestion(question);
                
                // Pause entre questions
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
            
            mettreAJourStatus('✅ Test automatique terminé !', 'rgba(0, 150, 0, 0.3)');
            
            // Afficher résumé
            ajouterMessage(`
                📊 <strong>Résumé du test :</strong><br>
                • Questions posées: ${stats.totalQuestions}<br>
                • Recherches déclenchées: ${stats.recherchesDeclenchees}<br>
                • Réponses enrichies: ${stats.reponsesEnrichies}<br>
                • Taux de recherche: ${((stats.recherchesDeclenchees / stats.totalQuestions) * 100).toFixed(1)}%
            `, 'ai');
        }
        
        function effacerChat() {
            chatContainer.innerHTML = '<div class="message ai-message">🤖 Chat effacé. Prêt pour de nouveaux tests !</div>';
            stats = { totalQuestions: 0, recherchesDeclenchees: 0, reponsesEnrichies: 0 };
            mettreAJourStats();
            mettreAJourStatus('Chat effacé', 'rgba(255, 255, 255, 0.1)');
        }
        
        // Test de connexion au démarrage
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/status');
                if (response.ok) {
                    mettreAJourStatus('✅ Connexion au serveur OK - Recherche automatique prête !', 'rgba(0, 150, 0, 0.3)');
                } else {
                    mettreAJourStatus('⚠️ Serveur accessible mais problème', 'rgba(204, 102, 0, 0.3)');
                }
            } catch (error) {
                mettreAJourStatus('❌ Impossible de se connecter au serveur', 'rgba(204, 0, 0, 0.3)');
            }
        });
    </script>
</body>
</html>
