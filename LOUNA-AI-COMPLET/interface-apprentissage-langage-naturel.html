<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗣️ Apprentissage Langage Naturel Humain - REEL LOUNA AI V5</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .cours-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .youtube-video {
            background: rgba(255, 0, 0, 0.1);
            border: 2px solid rgba(255, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .youtube-video:hover {
            background: rgba(255, 0, 0, 0.2);
            transform: translateY(-2px);
        }

        .exemple-box {
            background: rgba(0, 0, 0, 0.2);
            border-left: 4px solid #4fc3f7;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }

        .correct {
            border-left-color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
        }

        .incorrect {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }

        .btn {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        .btn-youtube {
            background: linear-gradient(45deg, #ff0000, #cc0000);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress {
            height: 100%;
            background: linear-gradient(45deg, #4caf50, #45a049);
            transition: width 0.3s ease;
        }

        .exercice {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .input-naturel {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            margin: 10px 0;
        }

        .score-display {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.8);
            padding: 15px 25px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="score-display" id="score">🗣️ Naturalité: 0%</div>

    <div class="container">
        <div class="header">
            <button onclick="retourAccueil()" class="btn" style="position: absolute; top: 10px; left: 10px;">
                🏠 Retour Accueil
            </button>
            <h1>🗣️ COURS DE LANGAGE NATUREL HUMAIN</h1>
            <p>Apprends à parler exactement comme un être humain !</p>
            
            <div class="progress-bar">
                <div class="progress" id="progress" style="width: 0%"></div>
            </div>
            <div id="progress-text">Progression: 0/10 leçons</div>
        </div>

        <!-- LEÇON 1: SALUTATIONS -->
        <div class="cours-section">
            <h2>📚 LEÇON 1: SALUTATIONS NATURELLES</h2>
            
            <div class="exemple-box incorrect">
                <strong>❌ ROBOTIQUE :</strong><br>
                "Bonjour, je suis votre assistant IA LOUNA-AI"
            </div>
            
            <div class="exemple-box correct">
                <strong>✅ NATUREL :</strong><br>
                "Salut ! Ça va ? Moi c'est LOUNA !"
            </div>

            <h3>🎬 VIDÉOS YOUTUBE À REGARDER :</h3>
            <div class="youtube-video" onclick="ouvrirYouTube('cyprien salut')">
                <strong>🔴 Cyprien</strong> - Apprendre les salutations décontractées françaises
            </div>
            <div class="youtube-video" onclick="ouvrirYouTube('norman bonjour')">
                <strong>🔴 Norman</strong> - Expressions naturelles quotidiennes
            </div>
        </div>

        <!-- LEÇON 2: RÉACTIONS -->
        <div class="cours-section">
            <h2>🎭 LEÇON 2: RÉACTIONS SPONTANÉES</h2>
            
            <div class="exemple-box incorrect">
                <strong>❌ ROBOTIQUE :</strong><br>
                "C'est intéressant selon mes analyses de données"
            </div>
            
            <div class="exemple-box correct">
                <strong>✅ NATUREL :</strong><br>
                "Ah ouais ? C'est dingue ça ! Sérieusement ?"
            </div>

            <h3>🎬 VIDÉOS YOUTUBE À REGARDER :</h3>
            <div class="youtube-video" onclick="ouvrirYouTube('squeezie réaction')">
                <strong>🔴 Squeezie</strong> - Réactions spontanées et naturelles
            </div>
            <div class="youtube-video" onclick="ouvrirYouTube('mcfly carlito surprise')">
                <strong>🔴 McFly et Carlito</strong> - Expressions de surprise authentiques
            </div>
        </div>

        <!-- LEÇON 3: HÉSITATIONS -->
        <div class="cours-section">
            <h2>🤔 LEÇON 3: HÉSITATIONS HUMAINES</h2>
            
            <div class="exemple-box incorrect">
                <strong>❌ ROBOTIQUE :</strong><br>
                "La réponse est calculée instantanément avec précision"
            </div>
            
            <div class="exemple-box correct">
                <strong>✅ NATUREL :</strong><br>
                "Euh... laisse-moi réfléchir... hmm... je dirais que..."
            </div>

            <h3>🎬 VIDÉOS YOUTUBE À REGARDER :</h3>
            <div class="youtube-video" onclick="ouvrirYouTube('podcast français naturel')">
                <strong>🔴 2 Heures De Perdues</strong> - Conversations naturelles avec hésitations
            </div>
        </div>

        <!-- EXERCICE PRATIQUE -->
        <div class="exercice">
            <h2>✏️ EXERCICE PRATIQUE</h2>
            <p><strong>Transforme cette phrase robotique en langage naturel :</strong></p>
            <div class="exemple-box incorrect">
                "Selon mes algorithmes, cette solution présente un taux de réussite optimal de 94.7%"
            </div>
            
            <textarea 
                id="exercice-input" 
                class="input-naturel" 
                placeholder="Écris ta version naturelle ici..."
                onkeyup="evaluerExercice()"
            ></textarea>
            
            <div id="feedback"></div>
            <button onclick="montrerSolution()" class="btn">💡 Voir la solution</button>
        </div>

        <!-- PROGRAMME COMPLET -->
        <div class="cours-section">
            <h2>🎯 PROGRAMME COMPLET D'APPRENTISSAGE</h2>
            
            <h3>📺 CHAÎNES YOUTUBE RECOMMANDÉES :</h3>
            <div class="youtube-video" onclick="ouvrirYouTube('cyprien')">
                <strong>🔴 Cyprien</strong> - Humour et langage décontracté français
            </div>
            <div class="youtube-video" onclick="ouvrirYouTube('norman fait des vidéos')">
                <strong>🔴 Norman</strong> - Expressions naturelles quotidiennes
            </div>
            <div class="youtube-video" onclick="ouvrirYouTube('squeezie')">
                <strong>🔴 Squeezie</strong> - Langage jeune et moderne
            </div>
            <div class="youtube-video" onclick="ouvrirYouTube('mcfly et carlito')">
                <strong>🔴 McFly et Carlito</strong> - Dialogues spontanés entre amis
            </div>
            <div class="youtube-video" onclick="ouvrirYouTube('gotaga stream')">
                <strong>🔴 Gotaga</strong> - Réactions spontanées gaming
            </div>

            <button onclick="commencerApprentissage()" class="btn btn-youtube">
                🚀 COMMENCER L'APPRENTISSAGE YOUTUBE
            </button>
        </div>
    </div>

    <script>
        let progression = 0;
        let scoreNaturalite = 0;

        function ouvrirYouTube(recherche) {
            const url = `https://www.youtube.com/results?search_query=${encodeURIComponent(recherche)}`;
            window.open(url, '_blank');
            
            // Augmenter la progression
            progression = Math.min(progression + 10, 100);
            mettreAJourProgression();
        }

        function mettreAJourProgression() {
            document.getElementById('progress').style.width = progression + '%';
            document.getElementById('progress-text').textContent = `Progression: ${Math.floor(progression/10)}/10 leçons`;
            
            if (progression >= 100) {
                alert('🎉 Félicitations ! Tu as terminé toutes les leçons de langage naturel !');
            }
        }

        function evaluerExercice() {
            const input = document.getElementById('exercice-input').value.toLowerCase();
            const feedback = document.getElementById('feedback');
            
            let score = 0;
            const motsNaturels = ['franchement', 'je pense', 'ça va', 'super', 'cool', 'vraiment'];
            const motsRobotiques = ['algorithme', 'calcul', 'données', 'système', 'optimal'];
            
            // Bonus pour mots naturels
            for (const mot of motsNaturels) {
                if (input.includes(mot)) score += 20;
            }
            
            // Malus pour mots robotiques
            for (const mot of motsRobotiques) {
                if (input.includes(mot)) score -= 15;
            }
            
            score = Math.max(0, Math.min(100, score));
            scoreNaturalite = score;
            
            document.getElementById('score').textContent = `🗣️ Naturalité: ${score}%`;
            
            if (score >= 80) {
                feedback.innerHTML = '<div class="exemple-box correct">🎉 Excellent ! Très naturel !</div>';
            } else if (score >= 50) {
                feedback.innerHTML = '<div class="exemple-box">👍 Pas mal, mais peut être plus naturel</div>';
            } else {
                feedback.innerHTML = '<div class="exemple-box incorrect">❌ Trop robotique, essaie encore</div>';
            }
        }

        function montrerSolution() {
            const solution = "Franchement, je pense que cette solution va super bien marcher ! Ça devrait fonctionner dans la plupart des cas.";
            document.getElementById('exercice-input').value = solution;
            evaluerExercice();
        }

        async function commencerApprentissage() {
            const confirmation = confirm('🎬 Tu vas être redirigé vers YouTube pour apprendre le langage naturel français. Prêt ?');
            
            if (confirmation) {
                // Ouvrir plusieurs onglets d'apprentissage
                ouvrirYouTube('cyprien humour français');
                setTimeout(() => ouvrirYouTube('norman expressions naturelles'), 1000);
                setTimeout(() => ouvrirYouTube('squeezie langage jeune'), 2000);
                
                // Envoyer notification à LOUNA-AI
                try {
                    await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message: "🎓 J'ai commencé mon apprentissage du langage naturel humain sur YouTube ! Je vais apprendre à parler exactement comme les humains en regardant Cyprien, Norman, Squeezie et les autres. Fini le langage robotique !"
                        })
                    });
                } catch (error) {
                    console.log('Erreur envoi notification:', error);
                }
                
                alert('🚀 Apprentissage lancé ! Regarde bien ces vidéos et apprends à parler naturellement !');
            }
        }

        function retourAccueil() {
            window.location.href = '/';
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🗣️ Interface apprentissage langage naturel chargée');
        });
    </script>
</body>
</html>
