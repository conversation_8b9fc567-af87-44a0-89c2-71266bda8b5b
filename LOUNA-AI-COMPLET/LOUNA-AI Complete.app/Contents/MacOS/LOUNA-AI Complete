#!/bin/bash

# Script principal de LOUNA-AI Complete
export PATH="/usr/local/bin:/opt/homebrew/bin:$PATH"

# Fonction d'affichage
show_notification() {
    osascript -e "display notification \"$1\" with title \"LOUNA-AI\" sound name \"Glass\""
}

# Aller dans le répertoire LOUNA-AI
LOUNA_DIR="/Volumes/ALDO et MIM/LOUNA-AI-COMPLET"
if [ ! -d "$LOUNA_DIR" ]; then
    show_notification "❌ Répertoire LOUNA-AI non trouvé"
    exit 1
fi

cd "$LOUNA_DIR"

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    show_notification "❌ Node.js non installé"
    exit 1
fi

# Notification de démarrage
show_notification "🚀 Démarrage de LOUNA-AI..."

# Arrêter les processus existants
pkill -f "serveur-interface-complete.js" 2>/dev/null
sleep 2

# Démarrer le vrai serveur LOUNA-AI avec mémoire thermique
node serveur-louna-final.js &
SERVER_PID=$!

# Attendre le démarrage
sleep 8

# Vérifier que le serveur fonctionne
if curl -s http://localhost:8080/stats > /dev/null 2>&1; then
    show_notification "✅ LOUNA-AI avec mémoire thermique démarré"
    # Ouvrir l'interface corrigée
    open "http://localhost:8080/interface-louna-complete.html"
else
    show_notification "❌ Échec du démarrage de LOUNA-AI"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

# Créer un fichier PID pour pouvoir arrêter le serveur plus tard
echo $SERVER_PID > /tmp/louna-ai-server.pid

# Notification de succès
show_notification "🎉 LOUNA-AI Interface Complète active !"
