#!/usr/bin/env node

/**
 * 🌊 TEST SYSTÈME UNIFIÉ FLUIDE RÉEL
 * Vérification complète du déplacement fluide, agent 19GB et accélérateurs automatiques
 * AUCUNE SIMULATION - QUE DU CODE RÉEL ET FONCTIONNEL
 */

const SystemeUnifieFluideReel = require('./systeme-unifie-fluide-reel.js');

console.log('🌊 TEST SYSTÈME UNIFIÉ FLUIDE RÉEL');
console.log('==================================');

async function testerSystemeComplet() {
    try {
        // Initialiser le système unifié
        console.log('\n🚀 Initialisation du système unifié...');
        const systeme = new SystemeUnifieFluideReel();
        
        // Attendre l'initialisation complète
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 1: Vérifier l'état initial
        console.log('\n📊 TEST 1: ÉTAT INITIAL DU SYSTÈME');
        const stats_initiales = systeme.obtenirStatistiquesCompletes();
        console.log(`• Performance globale: ${stats_initiales.systeme.performance_globale.toFixed(1)}%`);
        console.log(`• Fluidité active: ${stats_initiales.systeme.etat_fluide.fluidite_active}`);
        console.log(`• Curseur thermique: ${stats_initiales.memoire.curseurThermique?.toFixed(4) || 'N/A'}`);
        console.log(`• Agent 19GB: ${stats_initiales.agent_19gb.actif ? 'Actif' : 'Inactif'}`);
        console.log(`• Accélérateurs: ${stats_initiales.accelerateurs.statistiques.actifs}/${stats_initiales.accelerateurs.statistiques.total_accelerateurs}`);
        
        // Test 2: Vérifier le déplacement fluide
        console.log('\n🌊 TEST 2: DÉPLACEMENT FLUIDE RÉEL');
        const curseur_initial = stats_initiales.memoire.curseurThermique || 0.5;
        console.log(`Curseur initial: ${curseur_initial.toFixed(4)}`);
        
        // Attendre le mouvement fluide
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const stats_apres_mouvement = systeme.obtenirStatistiquesCompletes();
        const curseur_apres = stats_apres_mouvement.memoire.curseurThermique || 0.5;
        console.log(`Curseur après 2s: ${curseur_apres.toFixed(4)}`);
        
        const mouvement_detecte = Math.abs(curseur_apres - curseur_initial) > 0.001;
        if (mouvement_detecte) {
            console.log('✅ DÉPLACEMENT FLUIDE RÉEL DÉTECTÉ !');
            console.log(`📏 Déplacement: ${Math.abs(curseur_apres - curseur_initial).toFixed(4)}`);
        } else {
            console.log('❌ Aucun déplacement fluide détecté');
        }
        
        // Test 3: Traitement unifié avec mémoire
        console.log('\n🧠 TEST 3: TRAITEMENT UNIFIÉ AVEC MÉMOIRE');
        const requete1 = "Quelle est la capitale de la France ?";
        const reponse1 = await systeme.traiterRequeteUnifiee(requete1, 'test');
        
        if (reponse1.success) {
            console.log('✅ Traitement unifié réussi');
            console.log(`📊 Performance: ${reponse1.performance_globale.toFixed(1)}%`);
            console.log(`🧠 Résultats mémoire: ${reponse1.resultats_memoire.length}`);
            console.log(`🤖 Réponse agent: ${reponse1.reponse_agent ? 'Oui' : 'Non'}`);
            console.log(`⚡ Accélération: ${reponse1.acceleration.boost_performance ? 'Oui' : 'Non'}`);
            
            if (reponse1.resultats_memoire.length > 0) {
                const meilleur = reponse1.resultats_memoire[0];
                console.log(`🎯 Meilleur résultat: ${meilleur.contenu.substring(0, 50)}...`);
                console.log(`🏛️ Zone: ${meilleur.zone_nom || meilleur.zone}`);
                console.log(`🔗 Connexions: ${meilleur.connexions || 0}`);
            }
        } else {
            console.log('❌ Échec traitement unifié');
        }
        
        // Test 4: Agent 19GB direct
        console.log('\n🤖 TEST 4: AGENT 19GB DIRECT');
        if (stats_initiales.agent_19gb.actif) {
            const requete_calcul = "Calcule 25 + 17 et explique le processus";
            const reponse_agent = await systeme.interrogerAgent19GB(requete_calcul);
            
            if (reponse_agent) {
                console.log('✅ Agent 19GB répond');
                console.log(`📝 Réponse: ${reponse_agent.substring(0, 100)}...`);
                systeme.metriques.reponses_agent++;
            } else {
                console.log('❌ Agent 19GB ne répond pas');
            }
        } else {
            console.log('⚠️ Agent 19GB non actif - Test ignoré');
        }
        
        // Test 5: Auto-installation accélérateurs
        console.log('\n🚀 TEST 5: AUTO-INSTALLATION ACCÉLÉRATEURS');
        const stats_acc_avant = systeme.accelerateurs.obtenirStatistiques();
        console.log(`Avant: ${stats_acc_avant.statistiques.actifs} accélérateurs actifs`);
        
        // Forcer auto-installation
        await systeme.autoInstallerAccelerateurs();
        
        const stats_acc_apres = systeme.accelerateurs.obtenirStatistiques();
        console.log(`Après: ${stats_acc_apres.statistiques.actifs} accélérateurs actifs`);
        
        const nouveaux_accelerateurs = stats_acc_apres.statistiques.actifs - stats_acc_avant.statistiques.actifs;
        if (nouveaux_accelerateurs > 0) {
            console.log(`✅ ${nouveaux_accelerateurs} accélérateurs auto-installés`);
        } else {
            console.log('ℹ️ Tous les accélérateurs étaient déjà actifs');
        }
        
        // Test 6: Synchronisation fluide
        console.log('\n🔄 TEST 6: SYNCHRONISATION FLUIDE');
        const coherence_avant = systeme.etat_fluide.coherence_systeme;
        console.log(`Cohérence avant: ${(coherence_avant * 100).toFixed(1)}%`);
        
        // Attendre plusieurs cycles de synchronisation
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const coherence_apres = systeme.etat_fluide.coherence_systeme;
        console.log(`Cohérence après: ${(coherence_apres * 100).toFixed(1)}%`);
        
        if (coherence_apres > 0.7) {
            console.log('✅ Système bien synchronisé');
        } else {
            console.log('⚠️ Synchronisation à améliorer');
        }
        
        // Test 7: Persistance infinie
        console.log('\n♾️ TEST 7: PERSISTANCE INFINIE');
        console.log(`Configuration persistance: ${systeme.config_auto_accelerateurs.persistance_infinie}`);
        console.log(`Adaptation dynamique: ${systeme.config_auto_accelerateurs.adaptation_dynamique}`);
        console.log(`Installation automatique: ${systeme.config_auto_accelerateurs.installation_automatique}`);
        
        if (systeme.config_auto_accelerateurs.persistance_infinie) {
            console.log('✅ Persistance infinie activée - Les accélérateurs restent à vie');
        }
        
        // Test 8: Métriques finales
        console.log('\n📊 TEST 8: MÉTRIQUES FINALES');
        const stats_finales = systeme.obtenirStatistiquesCompletes();
        console.log(`• Operations totales: ${systeme.metriques.operations_totales}`);
        console.log(`• Réponses agent: ${systeme.metriques.reponses_agent}`);
        console.log(`• Accélérations appliquées: ${systeme.metriques.accelerations_appliquees}`);
        console.log(`• Déplacements fluides: ${systeme.metriques.deplacements_fluides}`);
        console.log(`• Performance globale: ${stats_finales.systeme.performance_globale.toFixed(1)}%`);
        
        // Test 9: Sauvegarde état
        console.log('\n💾 TEST 9: SAUVEGARDE ÉTAT');
        systeme.sauvegarderEtatSysteme();
        
        const fs = require('fs');
        if (fs.existsSync('systeme-unifie-etat.json')) {
            console.log('✅ État système sauvegardé');
            const etat_sauve = JSON.parse(fs.readFileSync('systeme-unifie-etat.json', 'utf8'));
            console.log(`📅 Timestamp: ${etat_sauve.timestamp}`);
            console.log(`🎯 Performance: ${etat_sauve.performance_globale.toFixed(1)}%`);
        } else {
            console.log('❌ Échec sauvegarde état');
        }
        
        // Résumé final
        console.log('\n🎯 RÉSUMÉ FINAL DU TEST');
        console.log('======================');
        
        const tests_reussis = [
            mouvement_detecte ? '✅' : '❌',
            reponse1.success ? '✅' : '❌',
            stats_initiales.agent_19gb.actif ? '✅' : '⚠️',
            stats_acc_apres.statistiques.actifs > 0 ? '✅' : '❌',
            coherence_apres > 0.7 ? '✅' : '⚠️',
            systeme.config_auto_accelerateurs.persistance_infinie ? '✅' : '❌',
            fs.existsSync('systeme-unifie-etat.json') ? '✅' : '❌'
        ];
        
        console.log(`• Déplacement fluide: ${tests_reussis[0]}`);
        console.log(`• Traitement unifié: ${tests_reussis[1]}`);
        console.log(`• Agent 19GB: ${tests_reussis[2]}`);
        console.log(`• Accélérateurs: ${tests_reussis[3]}`);
        console.log(`• Synchronisation: ${tests_reussis[4]}`);
        console.log(`• Persistance infinie: ${tests_reussis[5]}`);
        console.log(`• Sauvegarde: ${tests_reussis[6]}`);
        
        const score = tests_reussis.filter(t => t === '✅').length;
        console.log(`\n🏆 SCORE FINAL: ${score}/7 tests réussis`);
        
        if (score >= 6) {
            console.log('🎉 SYSTÈME UNIFIÉ FLUIDE RÉEL OPÉRATIONNEL !');
        } else if (score >= 4) {
            console.log('⚠️ Système partiellement fonctionnel - Améliorations nécessaires');
        } else {
            console.log('❌ Système non fonctionnel - Révision requise');
        }
        
        console.log('\n🌊✨ TEST SYSTÈME UNIFIÉ FLUIDE TERMINÉ ! ✨🌊');
        
    } catch (error) {
        console.error('❌ Erreur test système unifié:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testerSystemeComplet();
