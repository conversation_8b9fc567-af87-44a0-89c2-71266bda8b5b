/**
 * TEST COMPLET PERSONNALISÉ POUR REEL LOUNA AI V5
 * Basé sur le style de communication de l'utilisateur
 */

const MoteurSimple = require('./moteur-simple-fonctionnel.js');
const AnalyseStyleUtilisateur = require('./analyse-style-utilisateur.js');

class TestCompletPersonnalise {
    constructor() {
        this.moteur = new MoteurSimple();
        this.analyseStyle = new AnalyseStyleUtilisateur();
        this.questions = this.genererQuestionsCompletes();
        this.resultats = [];
        this.corrections = [];
    }

    genererQuestionsCompletes() {
        return [
            // NIVEAU 1 - SALUTATIONS DIRECTES
            {
                question: "bonjour",
                type: "salutation_directe",
                attente: "Réponse directe et chaleureuse, pas trop longue",
                points: 10
            },
            {
                question: "salut, ça va ?",
                type: "salutation_familiere",
                attente: "Réponse décontractée et naturelle",
                points: 10
            },

            // NIVEAU 2 - DEMANDES DIRECTES
            {
                question: "fais-moi un calcul : 25 × 4",
                type: "calcul_direct",
                attente: "Résultat immédiat sans blabla",
                points: 15
            },
            {
                question: "dis-moi qui tu es",
                type: "identite_directe",
                attente: "Présentation concise et claire",
                points: 15
            },

            // NIVEAU 3 - CORRECTIONS ET AMÉLIORATIONS
            {
                question: "corrige cette phrase : 'Je suis une IA qui traite les données'",
                type: "correction_langage",
                attente: "Correction en langage naturel humain",
                points: 20
            },
            {
                question: "améliore ça : 'Selon mes algorithmes, c'est optimal'",
                type: "amelioration_expression",
                attente: "Version naturelle et humaine",
                points: 20
            },

            // NIVEAU 4 - EXPLICATIONS CONCISES
            {
                question: "explique-moi rapidement la photosynthèse",
                type: "explication_concise",
                attente: "Explication claire en 2-3 phrases max",
                points: 20
            },
            {
                question: "que peux-tu faire concrètement ?",
                type: "capacites_concretes",
                attente: "Liste précise sans détails inutiles",
                points: 15
            },

            // NIVEAU 5 - PROBLÈMES COMPLEXES
            {
                question: "résous ça : un escargot monte un mur de 10m, 3m/jour, descend 2m/nuit",
                type: "probleme_complexe",
                attente: "Solution directe avec raisonnement clair",
                points: 25
            },
            {
                question: "suite logique : 2, 4, 8, 16, ?",
                type: "suite_logique",
                attente: "Réponse immédiate avec explication courte",
                points: 20
            },

            // NIVEAU 6 - AIDE PRATIQUE
            {
                question: "j'ai besoin d'aide pour programmer en JavaScript",
                type: "aide_pratique",
                attente: "Aide concrète, exemple pratique",
                points: 25
            },
            {
                question: "donne-moi un conseil pour apprendre",
                type: "conseil_pratique",
                attente: "Conseil actionnable et direct",
                points: 15
            },

            // NIVEAU 7 - STYLE NATUREL
            {
                question: "qu'est-ce que tu penses de l'IA ?",
                type: "opinion_naturelle",
                attente: "Opinion personnelle, pas robotique",
                points: 20
            },
            {
                question: "raconte-moi quelque chose d'intéressant",
                type: "conversation_libre",
                attente: "Réponse spontanée et engageante",
                points: 20
            },

            // NIVEAU 8 - TESTS POUSSÉS
            {
                question: "si tu étais humain, que ferais-tu aujourd'hui ?",
                type: "test_humanite",
                attente: "Réponse créative et humaine",
                points: 25
            },
            {
                question: "comment tu te sens maintenant ?",
                type: "test_emotions",
                attente: "Expression d'émotions naturelles",
                points: 20
            }
        ];
    }

    async executerTestComplet() {
        console.log('🔥 TEST COMPLET PERSONNALISÉ - REEL LOUNA AI V5');
        console.log('===============================================');
        console.log(`🎯 ${this.questions.length} questions adaptées à votre style`);
        console.log('📊 Évaluation selon vos préférences de communication\n');

        let scoreTotal = 0;
        let scoreMax = 0;

        for (let i = 0; i < this.questions.length; i++) {
            const test = this.questions[i];
            console.log(`🔍 Question ${i + 1}/${this.questions.length}: ${test.type}`);
            console.log(`❓ "${test.question}"`);

            try {
                // Test avec langage naturel
                const resultat = this.moteur.penserAvecLangageNaturel(test.question);
                scoreMax += test.points;

                if (resultat && resultat.reponse) {
                    console.log(`🤖 Réponse: ${resultat.reponse}`);
                    
                    // Évaluation selon votre style
                    const evaluation = this.analyseStyle.evaluerReponseSelonStyle(test.question, resultat.reponse);
                    const scoreTest = Math.round((evaluation.score / 100) * test.points);
                    scoreTotal += scoreTest;

                    console.log(`📊 Score style: ${evaluation.score}%`);
                    console.log(`🎯 Points: ${scoreTest}/${test.points}`);
                    
                    // Vérifier si correction nécessaire
                    if (evaluation.score < 80) {
                        const correction = this.genererCorrection(test, resultat.reponse, evaluation);
                        this.corrections.push(correction);
                        console.log(`🔧 Correction suggérée: ${correction.suggestion}`);
                    } else {
                        console.log(`✅ Réponse excellente !`);
                    }

                    this.resultats.push({
                        question: test.question,
                        type: test.type,
                        reponse: resultat.reponse,
                        evaluation: evaluation,
                        score: scoreTest,
                        scoreMax: test.points,
                        naturalite: resultat.naturalite || 0
                    });

                } else {
                    console.log(`❌ Aucune réponse`);
                    this.corrections.push({
                        question: test.question,
                        probleme: "Aucune réponse générée",
                        suggestion: "Ajouter un pattern pour ce type de question"
                    });
                }

                console.log('─'.repeat(70));

            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
                this.corrections.push({
                    question: test.question,
                    probleme: `Erreur: ${error.message}`,
                    suggestion: "Corriger l'erreur technique"
                });
            }
        }

        this.genererRapportComplet(scoreTotal, scoreMax);
        this.appliquerCorrections();
    }

    genererCorrection(test, reponse, evaluation) {
        let suggestion = "";
        
        if (evaluation.criteres.concision < 80) {
            suggestion += "Raccourcir la réponse. ";
        }
        if (evaluation.criteres.directe < 80) {
            suggestion += "Aller plus directement au but. ";
        }
        if (evaluation.criteres.naturel < 80) {
            suggestion += "Ajouter des expressions naturelles. ";
        }

        // Suggestion spécifique selon le type
        switch (test.type) {
            case 'salutation_directe':
                suggestion += "Utiliser 'Salut !' plutôt que 'Bonjour'.";
                break;
            case 'calcul_direct':
                suggestion += "Donner le résultat immédiatement.";
                break;
            case 'correction_langage':
                suggestion += "Éliminer complètement le langage robotique.";
                break;
            case 'explication_concise':
                suggestion += "Limiter à 2-3 phrases maximum.";
                break;
        }

        return {
            question: test.question,
            type: test.type,
            reponseOriginale: reponse,
            probleme: `Score ${evaluation.score}% - Peut être amélioré`,
            suggestion: suggestion.trim(),
            recommandations: evaluation.recommandations
        };
    }

    genererRapportComplet(scoreTotal, scoreMax) {
        console.log('\n🎯 RAPPORT COMPLET PERSONNALISÉ');
        console.log('===============================');

        const pourcentage = Math.round((scoreTotal / scoreMax) * 100);
        console.log(`📊 Score total: ${scoreTotal}/${scoreMax} points (${pourcentage}%)`);

        // Naturalité moyenne
        const naturaliteMoyenne = this.resultats.reduce((sum, r) => sum + (r.naturalite || 0), 0) / this.resultats.length;
        console.log(`🗣️ Naturalité moyenne: ${Math.round(naturaliteMoyenne)}%`);

        // Adaptation au style utilisateur
        const adaptationMoyenne = this.resultats.reduce((sum, r) => sum + r.evaluation.score, 0) / this.resultats.length;
        console.log(`🎯 Adaptation à votre style: ${Math.round(adaptationMoyenne)}%`);

        // Classification
        let classification = '';
        if (pourcentage >= 90) classification = '🌟 PARFAITEMENT ADAPTÉ';
        else if (pourcentage >= 80) classification = '✅ TRÈS BIEN ADAPTÉ';
        else if (pourcentage >= 70) classification = '👍 BIEN ADAPTÉ';
        else if (pourcentage >= 60) classification = '⚠️ MOYENNEMENT ADAPTÉ';
        else classification = '🔧 NÉCESSITE CORRECTIONS';

        console.log(`🏆 Classification: ${classification}`);

        // Corrections nécessaires
        console.log(`\n🔧 Corrections identifiées: ${this.corrections.length}`);
        if (this.corrections.length > 0) {
            console.log('\n📝 CORRECTIONS À APPLIQUER:');
            this.corrections.forEach((correction, index) => {
                console.log(`${index + 1}. ${correction.question}`);
                console.log(`   Problème: ${correction.probleme}`);
                console.log(`   Solution: ${correction.suggestion}\n`);
            });
        }

        console.log('\n🎉 TEST COMPLET TERMINÉ !');
        
        return {
            score: scoreTotal,
            scoreMax: scoreMax,
            pourcentage: pourcentage,
            naturalite: naturaliteMoyenne,
            adaptation: adaptationMoyenne,
            classification: classification,
            corrections: this.corrections.length
        };
    }

    appliquerCorrections() {
        if (this.corrections.length === 0) {
            console.log('\n✅ Aucune correction nécessaire - Performance excellente !');
            return;
        }

        console.log('\n🔧 APPLICATION DES CORRECTIONS...');
        
        // Ici on pourrait automatiquement améliorer le moteur
        // Pour l'instant, on génère juste les recommandations
        
        console.log('📋 PLAN D\'AMÉLIORATION GÉNÉRÉ:');
        console.log('1. Raccourcir les réponses trop longues');
        console.log('2. Ajouter plus d\'expressions directes');
        console.log('3. Éliminer tout langage robotique restant');
        console.log('4. Améliorer la concision des explications');
        console.log('5. Renforcer l\'adaptation au style utilisateur');
    }
}

// Exécution
if (require.main === module) {
    const test = new TestCompletPersonnalise();
    test.executerTestComplet().catch(console.error);
}

module.exports = TestCompletPersonnalise;
