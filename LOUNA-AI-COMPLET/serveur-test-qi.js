/**
 * SERVEUR SIMPLE POUR TEST QI
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const MoteurSimple = require('./moteur-simple-fonctionnel.js');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Initialiser le moteur
const moteur = new MoteurSimple();

// Route principale
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'interface-louna-complete.html'));
});

// Route chat
app.post('/api/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.json({
                success: false,
                error: 'Message requis'
            });
        }

        console.log(`💬 Question: "${message}"`);

        // Traitement avec le moteur et langage naturel
        const resultat = moteur.penserAvecLangageNaturel(message);

        if (resultat && resultat.reponse) {
            console.log(`🤖 Réponse: ${resultat.reponse.substring(0, 100)}...`);
            console.log(`🗣️ Naturalité: ${resultat.naturalite || 0}%`);

            res.json({
                success: true,
                reponse: resultat.reponse,
                source: resultat.source,
                qi_actuel: 320,
                naturalite: resultat.naturalite || 0,
                langageNaturel: resultat.langageNaturel || false,
                memory_used: true,
                timestamp: Date.now()
            });
        } else {
            res.json({
                success: true,
                reponse: "Je n'ai pas pu traiter votre demande. Pouvez-vous reformuler ?",
                source: 'Fallback',
                qi_actuel: 320,
                memory_used: false,
                timestamp: Date.now()
            });
        }

    } catch (error) {
        console.error('❌ Erreur:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

// Route test langage naturel
app.get('/test-langage', async (req, res) => {
    try {
        const TestLangageNaturel = require('./test-langage-naturel.js');
        const test = new TestLangageNaturel();

        // Capturer la sortie console
        let output = '';
        const originalLog = console.log;
        console.log = (...args) => {
            output += args.join(' ') + '\n';
            originalLog(...args);
        };

        const resultats = await test.executerTests();

        // Restaurer console.log
        console.log = originalLog;

        res.json({
            success: true,
            rapport: output,
            score: resultats.score,
            scoreMax: resultats.scoreMax,
            pourcentage: resultats.pourcentage,
            naturalite: resultats.naturalite,
            classification: resultats.classification
        });

    } catch (error) {
        console.error('❌ Erreur test langage:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

// Route test QI
app.get('/test-qi', async (req, res) => {
    try {
        const TestQIComplet = require('./test-qi-complet.js');
        const test = new TestQIComplet();
        
        // Capturer la sortie console
        let output = '';
        const originalLog = console.log;
        console.log = (...args) => {
            output += args.join(' ') + '\n';
            originalLog(...args);
        };
        
        await test.executerTest();
        
        // Restaurer console.log
        console.log = originalLog;
        
        res.json({
            success: true,
            rapport: output,
            score: test.score,
            scoreMax: test.scoreMax,
            pourcentage: Math.round((test.score / test.scoreMax) * 100)
        });
        
    } catch (error) {
        console.error('❌ Erreur test QI:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

// Démarrage serveur
app.listen(PORT, () => {
    console.log('🚀 SERVEUR REEL LOUNA AI V5 DÉMARRÉ');
    console.log('===================================');
    console.log(`🌐 Interface: http://localhost:${PORT}`);
    console.log(`🧠 Test QI: http://localhost:${PORT}/test-qi`);
    console.log(`🗣️ Test Langage: http://localhost:${PORT}/test-langage`);
    console.log('✅ Moteur simple initialisé');
    console.log('🗣️ Système langage naturel humain activé');
    console.log('🎯 Prêt pour tous les tests !');
});

module.exports = app;
