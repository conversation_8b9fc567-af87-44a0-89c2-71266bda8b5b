/**
 * TEST RÉEL DU PROTOCOLE MCP SÉCURISÉ
 * Vérification complète du système MCP de LOUNA-AI V5
 */

const { ProtocoleMCPSecurise } = require('./protocole-mcp-securise.js');
const axios = require('axios');

class TestMCPReel {
    constructor() {
        this.mcp = new ProtocoleMCPSecurise();
        this.resultatsTests = {
            initialisation: false,
            recherche_securisee: false,
            verification_domaines: false,
            cache_fonctionnel: false,
            statistiques: false,
            securite_https: false
        };
    }

    // TEST 1: Initialisation du MCP
    async testerInitialisation() {
        console.log('\n🔒 TEST 1: INITIALISATION MCP SÉCURISÉ');
        console.log('==========================================');
        
        try {
            // Vérifier que le MCP est bien initialisé
            const stats = this.mcp.obtenirStatistiquesSecurite();

            if (stats && typeof stats === 'object') {
                console.log('✅ MCP initialisé correctement');
                console.log(`   - Domaines certifiés: ${stats.securite.domaines_certifies || 'N/A'}`);
                console.log(`   - Patterns dangereux: ${stats.securite.patterns_dangereux || 'N/A'}`);
                console.log(`   - Cache actif: ${stats.securite.cache_actif ? 'Oui' : 'Non'}`);

                this.resultatsTests.initialisation = true;
                return true;
            } else {
                console.log('❌ Erreur initialisation MCP');
                return false;
            }
        } catch (error) {
            console.log(`❌ Erreur test initialisation: ${error.message}`);
            return false;
        }
    }

    // TEST 2: Recherche sécurisée
    async testerRechercheSecurisee() {
        console.log('\n🔍 TEST 2: RECHERCHE SÉCURISÉE');
        console.log('===============================');
        
        try {
            const requete = 'intelligence artificielle neurones';
            console.log(`🔍 Recherche: "${requete}"`);
            
            const resultats = await this.mcp.rechercherSecurise(requete);
            
            if (resultats && resultats.resultats && Array.isArray(resultats.resultats)) {
                console.log(`✅ Recherche réussie`);
                console.log(`   - Requête: ${resultats.requete}`);
                console.log(`   - Nombre de résultats: ${resultats.resultats.length}`);
                console.log(`   - Temps de réponse: ${resultats.temps_reponse || 'N/A'}ms`);
                
                // Afficher quelques résultats
                resultats.resultats.slice(0, 3).forEach((resultat, index) => {
                    console.log(`\n   ${index + 1}. ${resultat.titre || 'Titre non disponible'}`);
                    console.log(`      Source: ${resultat.source || 'Source inconnue'}`);
                    console.log(`      Fiabilité: ${resultat.fiabilite ? (resultat.fiabilite * 100).toFixed(0) + '%' : 'N/A'}`);
                });
                
                this.resultatsTests.recherche_securisee = true;
                return true;
            } else {
                console.log('❌ Aucun résultat de recherche');
                return false;
            }
        } catch (error) {
            console.log(`❌ Erreur recherche sécurisée: ${error.message}`);
            return false;
        }
    }

    // TEST 3: Vérification des domaines
    async testerVerificationDomaines() {
        console.log('\n🌐 TEST 3: VÉRIFICATION DOMAINES');
        console.log('=================================');
        
        try {
            const domainesTest = [
                'wikipedia.org',
                'service-public.fr', 
                'insee.fr',
                'site-malveillant-test.com'
            ];
            
            let domainesValides = 0;
            
            for (const domaine of domainesTest) {
                const estCertifie = this.mcp.verifierDomaineCertifie(domaine);
                console.log(`   ${domaine}: ${estCertifie ? '✅ Certifié' : '❌ Non certifié'}`);
                
                if (estCertifie && !domaine.includes('malveillant')) {
                    domainesValides++;
                }
            }
            
            if (domainesValides >= 2) {
                console.log('✅ Vérification domaines fonctionnelle');
                this.resultatsTests.verification_domaines = true;
                return true;
            } else {
                console.log('❌ Problème vérification domaines');
                return false;
            }
        } catch (error) {
            console.log(`❌ Erreur vérification domaines: ${error.message}`);
            return false;
        }
    }

    // TEST 4: Fonctionnement du cache
    async testerCache() {
        console.log('\n💾 TEST 4: FONCTIONNEMENT CACHE');
        console.log('================================');
        
        try {
            const clefTest = 'test-cache-mcp-' + Date.now();
            const donneesTest = {
                titre: 'Test Cache MCP',
                contenu: 'Données de test pour le cache',
                timestamp: Date.now()
            };
            
            // Mettre en cache
            await this.mcp.mettreEnCache(clefTest, donneesTest);
            console.log('✅ Données mises en cache');
            
            // Récupérer du cache
            const donneesRecuperees = await this.mcp.verifierCache(clefTest);
            
            if (donneesRecuperees && donneesRecuperees.titre === donneesTest.titre) {
                console.log('✅ Données récupérées du cache');
                console.log(`   - Titre: ${donneesRecuperees.titre}`);
                console.log(`   - Timestamp: ${donneesRecuperees.timestamp}`);
                
                this.resultatsTests.cache_fonctionnel = true;
                return true;
            } else {
                console.log('❌ Erreur récupération cache');
                return false;
            }
        } catch (error) {
            console.log(`❌ Erreur test cache: ${error.message}`);
            return false;
        }
    }

    // TEST 5: Statistiques de sécurité
    async testerStatistiques() {
        console.log('\n📊 TEST 5: STATISTIQUES SÉCURITÉ');
        console.log('=================================');
        
        try {
            const stats = this.mcp.obtenirStatistiquesSecurite();

            if (stats && typeof stats === 'object') {
                console.log('✅ Statistiques disponibles');
                console.log(`   - Recherches effectuées: ${stats.metriques.requetes_totales || 0}`);
                console.log(`   - Domaines refusés: ${stats.metriques.domaines_refuses || 0}`);
                console.log(`   - Erreurs sécurité: ${stats.metriques.erreurs_securite || 0}`);
                console.log(`   - Cache hits: ${stats.metriques.cache_hits || 0}`);

                this.resultatsTests.statistiques = true;
                return true;
            } else {
                console.log('❌ Statistiques non disponibles');
                return false;
            }
        } catch (error) {
            console.log(`❌ Erreur statistiques: ${error.message}`);
            return false;
        }
    }

    // TEST 6: Sécurité HTTPS
    async testerSecuriteHTTPS() {
        console.log('\n🔒 TEST 6: SÉCURITÉ HTTPS');
        console.log('==========================');
        
        try {
            // Tester URL HTTP (doit être refusée)
            const urlHTTP = 'http://example.com';
            
            try {
                await this.mcp.requeteHTTPSSecurisee(urlHTTP);
                console.log('❌ URL HTTP acceptée (problème de sécurité)');
                return false;
            } catch (error) {
                if (error.message.includes('HTTPS obligatoire')) {
                    console.log('✅ URL HTTP correctement refusée');
                    this.resultatsTests.securite_https = true;
                    return true;
                } else {
                    console.log(`❌ Erreur inattendue: ${error.message}`);
                    return false;
                }
            }
        } catch (error) {
            console.log(`❌ Erreur test HTTPS: ${error.message}`);
            return false;
        }
    }

    // EXÉCUTER TOUS LES TESTS
    async executerTousLesTests() {
        console.log('🚀 DÉMARRAGE TESTS RÉELS MCP SÉCURISÉ');
        console.log('=====================================');
        console.log('🔥 LOUNA-AI V5 - Vérification Protocole MCP');
        console.log('=====================================\n');
        
        const tests = [
            { nom: 'Initialisation', methode: () => this.testerInitialisation() },
            { nom: 'Recherche Sécurisée', methode: () => this.testerRechercheSecurisee() },
            { nom: 'Vérification Domaines', methode: () => this.testerVerificationDomaines() },
            { nom: 'Cache Fonctionnel', methode: () => this.testerCache() },
            { nom: 'Statistiques', methode: () => this.testerStatistiques() },
            { nom: 'Sécurité HTTPS', methode: () => this.testerSecuriteHTTPS() }
        ];
        
        let testsReussis = 0;
        
        for (const test of tests) {
            try {
                const resultat = await test.methode();
                if (resultat) {
                    testsReussis++;
                }
            } catch (error) {
                console.log(`❌ Erreur test ${test.nom}: ${error.message}`);
            }
        }
        
        // RÉSUMÉ FINAL
        console.log('\n🏆 RÉSUMÉ TESTS MCP SÉCURISÉ');
        console.log('=============================');
        console.log(`✅ Tests réussis: ${testsReussis}/${tests.length}`);
        console.log(`📊 Taux de réussite: ${((testsReussis / tests.length) * 100).toFixed(1)}%`);
        
        Object.entries(this.resultatsTests).forEach(([test, resultat]) => {
            console.log(`   ${resultat ? '✅' : '❌'} ${test.replace(/_/g, ' ')}`);
        });
        
        if (testsReussis === tests.length) {
            console.log('\n🎉 TOUS LES TESTS MCP RÉUSSIS !');
            console.log('🔒 Protocole MCP Sécurisé 100% OPÉRATIONNEL');
        } else {
            console.log('\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ');
            console.log('🔧 Vérification nécessaire du système MCP');
        }
        
        return testsReussis === tests.length;
    }
}

// EXÉCUTION DU TEST
if (require.main === module) {
    const testeur = new TestMCPReel();
    testeur.executerTousLesTests()
        .then(succes => {
            process.exit(succes ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ ERREUR CRITIQUE TESTS MCP:', error.message);
            process.exit(1);
        });
}

module.exports = { TestMCPReel };
