/**
 * MÉMOIRE THERMIQUE RÉELLE POUR LOUNA-AI
 * Stockage et récupération vraie - pas de simulation
 */

const fs = require('fs');
const path = require('path');

class MemoireThermiqueReelle {
    constructor() {
        this.fichierMemoire = path.join(__dirname, 'memoire-thermique-data.json');
        this.memoires = new Map();
        this.temperaturesZones = [65, 55, 45, 35, 30, 25]; // 6 zones thermiques réajustées
        this.curseurThermique = 0.5; // Position du curseur
        
        this.chargerMemoire();
    }

    // CHARGEMENT RÉEL DE LA MÉMOIRE
    chargerMemoire() {
        try {
            if (fs.existsSync(this.fichierMemoire)) {
                const data = JSON.parse(fs.readFileSync(this.fichierMemoire, 'utf8'));
                this.memoires = new Map(data.memoires || []);
                this.curseurThermique = data.curseurThermique || 0.5;
                console.log(`📥 Mémoire chargée: ${this.memoires.size} entrées`);
            } else {
                console.log(`📥 Nouveau fichier mémoire créé`);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement mémoire:`, error.message);
            this.memoires = new Map();
        }
    }

    // SAUVEGARDE RÉELLE DE LA MÉMOIRE
    sauvegarderMemoire() {
        try {
            const data = {
                memoires: Array.from(this.memoires.entries()),
                curseurThermique: this.curseurThermique,
                timestamp: Date.now()
            };
            fs.writeFileSync(this.fichierMemoire, JSON.stringify(data, null, 2));
            console.log(`💾 Mémoire sauvegardée: ${this.memoires.size} entrées`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur sauvegarde mémoire:`, error.message);
            return false;
        }
    }

    // CALCUL RÉEL DE LA TEMPÉRATURE AVEC DIVERSITÉ
    calculerTemperature(importance, recence, utilisation, source = 'general') {
        // Base selon l'importance
        let temperatureBase = importance * 50; // 0.5 = 25°C, 1.0 = 50°C

        // Bonus selon la source
        const bonusSource = {
            'Raisonnement': 25,     // Zone 1 (70-75°C) - PRIORITÉ MAXIMALE
            'Formation': 20,        // Zone 1 (65-70°C)
            'Internet': 15,         // Zone 2 (55-65°C)
            'Google': 15,           // Zone 2 (55-65°C)
            'Ollama': 10,          // Zone 3 (45-55°C)
            'general': 5           // Zone 4-6 (25-45°C)
        };

        temperatureBase += bonusSource[source] || bonusSource['general'];

        // Facteur récence (plus récent = plus chaud)
        temperatureBase += recence * 15;

        // Facteur utilisation (plus utilisé = plus chaud)
        temperatureBase += Math.min(utilisation / 5, 10); // Max +10°C

        // Variation aléatoire pour diversité (±5°C)
        const variation = (Math.random() - 0.5) * 10;
        temperatureBase += variation;

        // Contraindre entre 25°C et 75°C pour plus de diversité
        return Math.max(25, Math.min(75, temperatureBase));
    }

    // STOCKAGE RÉEL D'UNE MÉMOIRE
    stocker(contenu, source, importance = 0.5) {
        const id = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const timestamp = Date.now();
        
        const memoire = {
            id: id,
            contenu: contenu,
            source: source,
            timestamp: timestamp,
            importance: importance,
            utilisation: 0,
            temperature: this.calculerTemperature(importance, 1.0, 0, source),
            zone: this.determinerZone(this.calculerTemperature(importance, 1.0, 0, source))
        };
        
        this.memoires.set(id, memoire);
        this.sauvegarderMemoire();
        
        console.log(`🧠 Mémoire stockée: ${id} (temp: ${memoire.temperature.toFixed(1)}°C, zone: ${memoire.zone})`);
        return id;
    }

    // DÉTERMINATION RÉELLE DE LA ZONE
    determinerZone(temperature) {
        for (let i = 0; i < this.temperaturesZones.length; i++) {
            if (temperature >= this.temperaturesZones[i]) {
                return i + 1; // Zone 1-6
            }
        }
        return 6; // Zone la plus froide
    }

    // RECHERCHE RÉELLE DANS LA MÉMOIRE
    rechercher(requete, limite = 5) {
        const resultats = [];
        const requeteLower = requete.toLowerCase();

        console.log(`🔍 Recherche mémoire: "${requete}"`);

        for (const [id, memoire] of this.memoires) {
            const contenuLower = memoire.contenu.toLowerCase();

            // Calcul de pertinence réel et STRICT
            let pertinence = 0;
            let motsCorrespondants = 0;

            // Correspondance exacte (très importante)
            if (contenuLower.includes(requeteLower)) {
                pertinence += 2.0;
            }

            // Correspondance de mots STRICTE
            const motsRequete = requeteLower.split(' ').filter(mot => mot.length > 2);
            const motsContenu = contenuLower.split(' ');

            for (const mot of motsRequete) {
                // Correspondance exacte de mot
                if (motsContenu.includes(mot)) {
                    pertinence += 1.0;
                    motsCorrespondants++;
                }
                // Correspondance partielle (plus stricte)
                else if (motsContenu.some(m => m.includes(mot) && m.length < mot.length + 3)) {
                    pertinence += 0.3;
                    motsCorrespondants++;
                }
            }

            // SEUIL TRÈS STRICT : au moins 70% des mots doivent correspondre ET correspondance spécifique
            const tauxCorrespondance = motsRequete.length > 0 ? motsCorrespondants / motsRequete.length : 0;

            // Vérification spéciale pour les capitales
            if (requeteLower.includes('capitale')) {
                let paysDetecte = false;
                const paysDansRequete = ['france', 'italie', 'espagne', 'allemagne', 'japon', 'brésil', 'bresil', 'maroc', 'australie', 'guadeloupe'];
                const paysDansContenu = contenuLower;

                for (const pays of paysDansRequete) {
                    if (requeteLower.includes(pays)) {
                        if (!paysDansContenu.includes(pays)) {
                            pertinence = 0; // Rejeter si pays différent
                        }
                        paysDetecte = true;
                        break;
                    }
                }

                // Si question sur capitale mais pas le bon pays, rejeter
                if (paysDetecte && pertinence === 0) {
                    continue;
                }
            }

            if (tauxCorrespondance < 0.7) {
                pertinence = 0; // Rejeter si pas assez de correspondance
            }

            // PAS de bonus température pour éviter les faux positifs
            // if (pertinence > 0) {
            //     pertinence += (memoire.temperature / 100) * 0.1;
            // }

            // SEUIL TRÈS ÉLEVÉ pour éviter les faux positifs
            if (pertinence > 1.5) {
                // Incrémenter l'utilisation
                memoire.utilisation++;

                resultats.push({
                    id: id,
                    contenu: memoire.contenu,
                    source: memoire.source,
                    pertinence: pertinence,
                    temperature: memoire.temperature,
                    zone: memoire.zone,
                    utilisation: memoire.utilisation,
                    tauxCorrespondance: tauxCorrespondance
                });
            }
        }

        // Tri par pertinence
        resultats.sort((a, b) => b.pertinence - a.pertinence);

        const resultatsLimites = resultats.slice(0, limite);
        console.log(`📋 ${resultatsLimites.length} résultats trouvés (seuil strict appliqué)`);

        // Sauvegarder les changements d'utilisation seulement si résultats pertinents
        if (resultatsLimites.length > 0) {
            this.sauvegarderMemoire();
        }

        return resultatsLimites;
    }

    // MISE À JOUR THERMIQUE RÉELLE
    mettreAJourTemperatures() {
        const maintenant = Date.now();
        let memoiresModifiees = 0;
        
        for (const [id, memoire] of this.memoires) {
            const ageJours = (maintenant - memoire.timestamp) / (1000 * 60 * 60 * 24);
            const recence = Math.max(0, 1 - (ageJours / 30)); // Décroît sur 30 jours
            
            const nouvelleTemperature = this.calculerTemperature(
                memoire.importance,
                recence,
                Math.min(1, memoire.utilisation / 10), // Normalisation utilisation
                memoire.source
            );
            
            if (Math.abs(nouvelleTemperature - memoire.temperature) > 1) {
                memoire.temperature = nouvelleTemperature;
                memoire.zone = this.determinerZone(nouvelleTemperature);
                memoiresModifiees++;
            }
        }
        
        if (memoiresModifiees > 0) {
            console.log(`🌡️ ${memoiresModifiees} températures mises à jour`);
            this.sauvegarderMemoire();
        }
        
        return memoiresModifiees;
    }

    // OUBLI AUTOMATIQUE RÉEL
    oublierMemoires() {
        const seuilOubli = 15; // Température en dessous de laquelle on oublie
        let memoiresOubliees = 0;
        
        for (const [id, memoire] of this.memoires) {
            if (memoire.temperature < seuilOubli && memoire.utilisation === 0) {
                this.memoires.delete(id);
                memoiresOubliees++;
                console.log(`🗑️ Mémoire oubliée: ${id} (temp: ${memoire.temperature.toFixed(1)}°C)`);
            }
        }
        
        if (memoiresOubliees > 0) {
            this.sauvegarderMemoire();
        }
        
        return memoiresOubliees;
    }

    // STATISTIQUES RÉELLES
    getStatistiquesReelles() {
        const zones = [0, 0, 0, 0, 0, 0];
        let temperatureTotal = 0;
        
        for (const memoire of this.memoires.values()) {
            zones[memoire.zone - 1]++;
            temperatureTotal += memoire.temperature;
        }
        
        return {
            totalEntries: this.memoires.size,
            averageTemperature: this.memoires.size > 0 ? temperatureTotal / this.memoires.size : 0,
            zonesDistribution: zones,
            curseurThermique: this.curseurThermique,
            fichierMemoire: this.fichierMemoire
        };
    }

    // MAINTENANCE AUTOMATIQUE
    maintenance() {
        console.log(`🔧 Maintenance mémoire thermique...`);
        const temperaturesModifiees = this.mettreAJourTemperatures();
        const memoiresOubliees = this.oublierMemoires();
        
        return {
            temperaturesModifiees,
            memoiresOubliees,
            totalMemoires: this.memoires.size
        };
    }
}

module.exports = { MemoireThermiqueReelle };
