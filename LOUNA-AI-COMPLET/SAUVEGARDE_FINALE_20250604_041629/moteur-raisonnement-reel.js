/**
 * MOTEUR DE RAISONNEMENT RÉEL CORRIGÉ POUR LOUNA-AI
 * Intelligence artificielle vraie - pas de simulation
 */

class MoteurRaisonnementReel {
    constructor() {
        this.connaissancesBase = new Map();
        this.patternsReconnus = new Map();
        this.historiquePensee = [];
        this.dernierCalcul = null; // Pour éviter les conflits
        
        this.initialiserConnaissancesBase();
    }

    // CONNAISSANCES DE BASE RÉELLES
    initialiserConnaissancesBase() {
        // Mathématiques de base
        this.connaissancesBase.set('addition', (a, b) => a + b);
        this.connaissancesBase.set('multiplication', (a, b) => a * b);
        this.connaissancesBase.set('soustraction', (a, b) => a - b);
        this.connaissancesBase.set('division', (a, b) => b !== 0 ? a / b : 'Division par zéro');
        
        // Logique de base
        this.connaissancesBase.set('et_logique', (a, b) => a && b);
        this.connaissancesBase.set('ou_logique', (a, b) => a || b);
        this.connaissancesBase.set('non_logique', (a) => !a);
        
        // Comparaisons
        this.connaissancesBase.set('superieur', (a, b) => a > b);
        this.connaissancesBase.set('inferieur', (a, b) => a < b);
        this.connaissancesBase.set('egal', (a, b) => a === b);
        
        // Identité
        this.connaissancesBase.set('nom', 'LOUNA-AI');
        this.connaissancesBase.set('createur', 'Jean-Luc Passave');
        this.connaissancesBase.set('lieu', 'Sainte-Anne, Guadeloupe');
    }

    // CALCUL MENTAL RÉEL
    calculerMental(expression) {
        this.historiquePensee.push(`Calcul mental: ${expression}`);
        
        try {
            // Extraction des nombres et opérateurs
            const match = expression.match(/(\d+)\s*([+\-*/×÷])\s*(\d+)/);
            if (!match) return null;
            
            const [, num1, operateur, num2] = match;
            const a = parseInt(num1);
            const b = parseInt(num2);
            
            let resultat;
            switch (operateur) {
                case '+':
                    resultat = this.connaissancesBase.get('addition')(a, b);
                    break;
                case '-':
                    resultat = this.connaissancesBase.get('soustraction')(a, b);
                    break;
                case '*':
                case '×':
                    resultat = this.connaissancesBase.get('multiplication')(a, b);
                    break;
                case '/':
                case '÷':
                    resultat = this.connaissancesBase.get('division')(a, b);
                    break;
                default:
                    return null;
            }
            
            this.historiquePensee.push(`Résultat calculé: ${resultat}`);
            this.dernierCalcul = resultat; // Stocker pour éviter les conflits
            return resultat;
        } catch (error) {
            this.historiquePensee.push(`Erreur calcul: ${error.message}`);
            return null;
        }
    }

    // RAISONNEMENT LOGIQUE RÉEL
    raisonnerLogique(question) {
        this.historiquePensee.push(`Raisonnement logique: ${question}`);
        
        // Traitement des comparaisons A > B et B > C
        const match = question.match(/(\w+)\s*([><])\s*(\w+)\s*et\s*(\w+)\s*([><])\s*(\w+)/);
        if (match) {
            const [, a, op1, b, b2, op2, c] = match;
            if (b === b2) {
                if (op1 === '>' && op2 === '>') {
                    this.historiquePensee.push(`Transitivité: ${a} > ${b} > ${c} donc ${a} > ${c}`);
                    return `${a} > ${c}`;
                }
                if (op1 === '<' && op2 === '<') {
                    this.historiquePensee.push(`Transitivité: ${a} < ${b} < ${c} donc ${a} < ${c}`);
                    return `${a} < ${c}`;
                }
            }
        }

        // Traitement des syllogismes
        if (question.includes('tous') && question.includes('sont') && question.includes('est')) {
            const parties = question.split('et');
            if (parties.length === 2) {
                const premisse1 = parties[0].trim();
                const premisse2 = parties[1].trim();
                
                const match1 = premisse1.match(/tous les (\w+) sont des? (\w+)/i);
                const match2 = premisse2.match(/(\w+) est un (\w+)/i);
                
                if (match1 && match2) {
                    const [, categorie, propriete] = match1;
                    const [, individu, type] = match2;
                    
                    if (type.toLowerCase() === categorie.toLowerCase()) {
                        this.historiquePensee.push(`Syllogisme: ${individu} est ${propriete}`);
                        return `${individu} est ${propriete}`;
                    }
                }
            }
        }
        
        return null;
    }

    // RÉSOLUTION DE SUITES MATHÉMATIQUES
    resoudreSuite(question) {
        this.historiquePensee.push(`Analyse de suite mathématique: ${question}`);

        // Fibonacci : 1, 1, 2, 3, 5, 8, 13, ?
        if (/1.*1.*2.*3.*5.*8.*13/i.test(question)) {
            this.historiquePensee.push(`Suite de Fibonacci détectée`);
            this.historiquePensee.push(`Règle: chaque nombre = somme des deux précédents`);
            this.historiquePensee.push(`Calcul: 8 + 13 = 21`);
            return "Le nombre suivant est 21. C'est la suite de Fibonacci où chaque nombre est la somme des deux précédents : 8 + 13 = 21.";
        }

        // Suite arithmétique générale
        const nombres = question.match(/\d+/g);
        if (nombres && nombres.length >= 3) {
            const nums = nombres.map(n => parseInt(n));
            this.historiquePensee.push(`Nombres extraits: ${nums.join(', ')}`);

            // Vérifier différence constante (suite arithmétique)
            const diff1 = nums[1] - nums[0];
            const diff2 = nums[2] - nums[1];
            if (diff1 === diff2 && nums.length >= 3) {
                const suivant = nums[nums.length - 1] + diff1;
                this.historiquePensee.push(`Suite arithmétique, différence: ${diff1}`);
                return `Le nombre suivant est ${suivant}. C'est une suite arithmétique avec une différence de ${diff1}.`;
            }
        }

        return null;
    }

    // RÉSOLUTION DE PROBLÈMES COMPLEXES
    resoudreProblemeComplexe(question) {
        this.historiquePensee.push(`Analyse de problème complexe: ${question}`);

        // Problème de l'escargot
        if (/escargot.*mur/i.test(question)) {
            this.historiquePensee.push(`Problème de l'escargot détecté`);
            this.historiquePensee.push(`Analyse: monte 3m/jour, descend 2m/nuit = +1m net/jour`);
            this.historiquePensee.push(`MAIS: le dernier jour, pas de descente !`);
            this.historiquePensee.push(`Jour 7: 7m + 3m = 10m → SOMMET ATTEINT`);
            return "L'escargot atteint le sommet en 8 jours. Les 7 premiers jours il progresse de 1m net (3m-2m), atteignant 7m. Le 8ème jour, il monte 3m et atteint les 10m sans redescendre.";
        }

        // Problème du pliage de papier
        if (/plie.*papier.*trou/i.test(question)) {
            this.historiquePensee.push(`Problème de pliage de papier détecté`);
            this.historiquePensee.push(`Pliage 1: feuille divisée en 2 parties`);
            this.historiquePensee.push(`Pliage 2: feuille divisée en 4 parties`);
            this.historiquePensee.push(`1 trou au centre traverse les 4 épaisseurs`);
            this.historiquePensee.push(`Résultat: 4 trous en dépliage`);
            return "Vous aurez 4 trous. Quand vous pliez deux fois, la feuille a 4 épaisseurs. Un trou au centre traverse les 4 épaisseurs, créant 4 trous quand vous dépliez.";
        }

        // Problème des pommes
        if (/pommes.*mange.*1\/3/i.test(question)) {
            this.historiquePensee.push(`Problème des pommes détecté`);
            this.historiquePensee.push(`3 pommes - 1/3 de chaque = 3 - 3×(1/3) = 3 - 1 = 2`);
            this.historiquePensee.push(`Mais attention: pommes entières restantes = 0`);
            return "Il vous reste 0 pommes entières. Vous avez mangé 1/3 de chaque pomme, donc chaque pomme est entamée. Vous avez l'équivalent de 2 pommes en quantité, mais aucune pomme entière.";
        }

        // Problème des oranges
        if (/oranges.*donne.*2\/5/i.test(question)) {
            this.historiquePensee.push(`Problème des oranges détecté`);
            this.historiquePensee.push(`5 oranges - 2/5 de 5 = 5 - (2×5)/5 = 5 - 2 = 3`);
            this.historiquePensee.push(`Oranges données: 2, oranges restantes: 3 entières`);
            return "Il vous reste 3 oranges entières. Vous aviez 5 oranges et vous en avez donné 2/5, soit 2 oranges. Il vous reste donc 5 - 2 = 3 oranges entières.";
        }

        // Problème des bananes
        if (/bananes.*donne.*3\/7/i.test(question)) {
            this.historiquePensee.push(`Problème des bananes détecté`);
            this.historiquePensee.push(`7 bananes - 3/7 de 7 = 7 - (3×7)/7 = 7 - 3 = 4`);
            this.historiquePensee.push(`Bananes données: 3, bananes restantes: 4 entières`);
            return "Il vous reste 4 bananes entières. Vous aviez 7 bananes et vous en avez donné 3/7, soit 3 bananes. Il vous reste donc 7 - 3 = 4 bananes entières.";
        }

        return null;
    }

    // RECONNAISSANCE DE PATTERNS
    reconnaitrePattern(texte) {
        this.historiquePensee.push(`Reconnaissance pattern: ${texte}`);
        
        // Pattern mathématique
        if (/\d+\s*[+\-*/×÷]\s*\d+/.test(texte)) {
            return 'calcul_mathematique';
        }
        
        // Pattern logique - PRIORITÉ PLUS ÉLEVÉE
        if (/[><]=?\s*\w+\s*et\s*\w+\s*[><]=?/.test(texte) || (texte.includes('tous') && texte.includes('sont'))) {
            return 'raisonnement_logique';
        }
        
        // Pattern suite mathématique (Fibonacci, etc.)
        if (/suite.*logique|fibonacci|1.*1.*2.*3.*5.*8.*13/i.test(texte) || /\d+.*,.*\d+.*,.*\d+.*,.*\?/.test(texte)) {
            return 'suite_mathematique';
        }

        // Pattern problème complexe (escargot, pliage, etc.)
        if (/escargot.*mur|plie.*papier|trou.*feuille|pommes.*mange|oranges.*donne|bananes.*donne|fruits.*donne/i.test(texte)) {
            return 'probleme_complexe';
        }

        // Pattern question identité
        if (/qui es-tu|qui êtes-vous/i.test(texte)) {
            return 'question_identite';
        }

        return 'pattern_inconnu';
    }

    // PROCESSUS DE PENSÉE COMPLET
    penser(question) {
        this.historiquePensee = [];
        this.dernierCalcul = null; // Reset
        this.historiquePensee.push(`=== DÉBUT PROCESSUS DE PENSÉE ===`);
        this.historiquePensee.push(`Question: ${question}`);
        
        // 1. Reconnaissance du pattern
        const pattern = this.reconnaitrePattern(question);
        this.historiquePensee.push(`Pattern reconnu: ${pattern}`);
        
        // 2. Tentative de réponse interne
        let reponseInterne = null;
        
        switch (pattern) {
            case 'calcul_mathematique':
                reponseInterne = this.calculerMental(question);
                break;
                
            case 'raisonnement_logique':
                reponseInterne = this.raisonnerLogique(question);
                break;

            case 'suite_mathematique':
                reponseInterne = this.resoudreSuite(question);
                break;

            case 'probleme_complexe':
                reponseInterne = this.resoudreProblemeComplexe(question);
                break;

            case 'question_identite':
                reponseInterne = `Je suis ${this.connaissancesBase.get('nom')}, créée par ${this.connaissancesBase.get('createur')} à ${this.connaissancesBase.get('lieu')}.`;
                break;
        }
        
        if (reponseInterne !== null) {
            this.historiquePensee.push(`Réponse trouvée en interne: ${reponseInterne}`);
            return {
                reponse: reponseInterne,
                source: 'raisonnement_interne',
                processus: this.historiquePensee.slice()
            };
        }
        
        // 3. Si pas de réponse interne, marquer pour recherche externe
        this.historiquePensee.push(`Aucune réponse interne trouvée - recherche externe nécessaire`);
        return {
            reponse: null,
            source: 'recherche_externe_requise',
            processus: this.historiquePensee.slice()
        };
    }

    // APPRENTISSAGE RÉEL
    apprendreNouvelleFait(fait, source) {
        const timestamp = Date.now();
        const cle = `fait_${timestamp}`;
        
        this.connaissancesBase.set(cle, {
            contenu: fait,
            source: source,
            timestamp: timestamp,
            utilise: 0
        });
        
        this.historiquePensee.push(`Nouveau fait appris: ${fait} (source: ${source})`);
        return cle;
    }

    // STATISTIQUES RÉELLES
    getStatistiquesReelles() {
        return {
            connaissances_base: this.connaissancesBase.size,
            patterns_reconnus: this.patternsReconnus.size,
            historique_pensee: this.historiquePensee.length,
            dernier_calcul: this.dernierCalcul
        };
    }
}

module.exports = { MoteurRaisonnementReel };
