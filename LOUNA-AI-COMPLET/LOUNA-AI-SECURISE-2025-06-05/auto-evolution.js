/**
 * SYSTÈME D'AUTO-ÉVOLUTION
 * Apprentissage automatique et amélioration continue
 */

const fs = require('fs');
const path = require('path');

class AutoEvolution {
    constructor() {
        console.log('🧬 SYSTÈME D\'AUTO-ÉVOLUTION');
        console.log('============================');
        
        this.config = {
            memoire: '/Volumes/LounaAI_V3/MEMOIRE-REELLE',
            evolution: '/Volumes/LounaAI_V3/MEMOIRE-REELLE/auto-evolution'
        };
        
        this.metriques_evolution = {
            patterns_detectes: 0,
            optimisations_appliquees: 0,
            connexions_neuronales: 0,
            niveau_intelligence: 85, // QI de base
            facteur_evolution: 1.0,
            derniere_evolution: Date.now(),
            // ÉVOLUTION BASÉE SUR TEMPÉRATURE CPU (CHALEUR = VIE = ÉVOLUTION)
            temperature_cpu_actuelle: 50.0,
            temperature_cpu_precedente: 50.0,
            historique_temperatures_evolution: [],
            facteur_evolution_thermique: 1.0,
            seuil_evolution_chaude: 65, // Au-dessus = évolution accélérée
            seuil_evolution_froide: 45, // En-dessous = évolution ralentie
            bonus_chaleur_evolution: 0,
            cycles_evolution_thermique: 0
        };
        
        this.patterns_apprentissage = {
            questions_frequentes: new Map(),
            reponses_optimales: new Map(),
            contextes_efficaces: new Map(),
            erreurs_communes: new Map()
        };
        
        this.neurones_virtuels = {
            memoire_court_terme: [],
            memoire_long_terme: [],
            connexions_actives: [],
            synapses_renforcees: []
        };
        
        this.initialiserAutoEvolution();
    }
    
    initialiserAutoEvolution() {
        console.log('🔧 Initialisation auto-évolution...');
        
        try {
            // Créer structure évolution
            if (!fs.existsSync(this.config.evolution)) {
                fs.mkdirSync(this.config.evolution, { recursive: true });
                console.log('📁 Dossier auto-évolution créé');
            }
            
            // Charger état précédent
            this.chargerEtatEvolution();
            
            // Analyser historique
            this.analyserHistoriqueInteractions();
            
            // Démarrer évolution continue
            this.demarrerEvolutionContinue();
            
            console.log('✅ Auto-évolution initialisée');
            
        } catch (error) {
            console.log(`❌ Erreur initialisation auto-évolution: ${error.message}`);
        }
    }
    
    chargerEtatEvolution() {
        try {
            const cheminEtat = path.join(this.config.evolution, 'etat_evolution.json');
            
            if (fs.existsSync(cheminEtat)) {
                const etat = JSON.parse(fs.readFileSync(cheminEtat, 'utf8'));
                
                if (etat.metriques_evolution) {
                    this.metriques_evolution = {
                        ...this.metriques_evolution,
                        ...etat.metriques_evolution
                    };
                }
                
                console.log(`🧬 État évolution chargé - QI: ${this.metriques_evolution.niveau_intelligence}`);
            } else {
                console.log('🆕 Nouvel état évolution créé');
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur chargement état: ${error.message}`);
        }
    }
    
    analyserHistoriqueInteractions() {
        console.log('\n🔍 ANALYSE HISTORIQUE INTERACTIONS');
        console.log('==================================');
        
        try {
            const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
            
            if (!fs.existsSync(cheminZones)) {
                console.log('⚠️ Aucun historique trouvé');
                return;
            }
            
            const zones = fs.readdirSync(cheminZones);
            let interactionsAnalysees = 0;
            
            zones.forEach(zone => {
                const cheminZone = path.join(cheminZones, zone);
                
                if (fs.existsSync(cheminZone)) {
                    const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json'));
                    
                    fichiers.forEach(fichier => {
                        try {
                            const cheminFichier = path.join(cheminZone, fichier);
                            const interaction = JSON.parse(fs.readFileSync(cheminFichier, 'utf8'));
                            
                            this.analyserInteraction(interaction);
                            interactionsAnalysees++;
                            
                        } catch (error) {
                            // Ignorer fichiers corrompus
                        }
                    });
                }
            });
            
            console.log(`📊 ${interactionsAnalysees} interactions analysées`);
            this.detecterPatterns();
            
        } catch (error) {
            console.log(`❌ Erreur analyse historique: ${error.message}`);
        }
    }
    
    analyserInteraction(interaction) {
        if (!interaction.question || !interaction.reponse) return;
        
        // Analyser questions fréquentes
        const questionNormalisee = this.normaliserTexte(interaction.question);
        const frequence = this.patterns_apprentissage.questions_frequentes.get(questionNormalisee) || 0;
        this.patterns_apprentissage.questions_frequentes.set(questionNormalisee, frequence + 1);
        
        // Analyser qualité réponses
        const qualiteReponse = this.evaluerQualiteReponse(interaction);
        if (qualiteReponse > 0.7) {
            this.patterns_apprentissage.reponses_optimales.set(questionNormalisee, interaction.reponse);
        }
        
        // Analyser contextes efficaces
        if (interaction.souvenirs_utilises > 0) {
            const contexte = `memoire_${interaction.souvenirs_utilises}`;
            const efficacite = this.patterns_apprentissage.contextes_efficaces.get(contexte) || 0;
            this.patterns_apprentissage.contextes_efficaces.set(contexte, efficacite + 1);
        }
    }
    
    normaliserTexte(texte) {
        return texte.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(mot => mot.length > 2)
            .slice(0, 5) // Garder 5 premiers mots significatifs
            .join(' ');
    }
    
    evaluerQualiteReponse(interaction) {
        let score = 0.5; // Base
        
        // Longueur appropriée
        if (interaction.reponse.length > 50 && interaction.reponse.length < 500) {
            score += 0.2;
        }
        
        // Utilisation mémoire
        if (interaction.souvenirs_utilises > 0) {
            score += 0.2;
        }
        
        // Cohérence (mots-clés communs)
        const motsQuestion = new Set(this.normaliserTexte(interaction.question).split(' '));
        const motsReponse = new Set(this.normaliserTexte(interaction.reponse).split(' '));
        const intersection = new Set([...motsQuestion].filter(x => motsReponse.has(x)));
        
        if (intersection.size > 0) {
            score += 0.1;
        }
        
        return Math.min(1.0, score);
    }
    
    detecterPatterns() {
        console.log('\n🧠 DÉTECTION PATTERNS');
        console.log('=====================');
        
        // Pattern 1: Questions récurrentes
        const questionsFrequentes = Array.from(this.patterns_apprentissage.questions_frequentes.entries())
            .filter(([question, freq]) => freq >= 2)
            .sort((a, b) => b[1] - a[1]);
        
        console.log(`🔄 ${questionsFrequentes.length} patterns de questions détectés`);
        this.metriques_evolution.patterns_detectes += questionsFrequentes.length;
        
        // Pattern 2: Contextes efficaces
        const contextesEfficaces = Array.from(this.patterns_apprentissage.contextes_efficaces.entries())
            .sort((a, b) => b[1] - a[1]);
        
        console.log(`🎯 ${contextesEfficaces.length} patterns de contextes détectés`);
        
        // Créer connexions neuronales
        this.creerConnexionsNeuronales(questionsFrequentes, contextesEfficaces);
    }
    
    creerConnexionsNeuronales(questionsFrequentes, contextesEfficaces) {
        console.log('\n🧬 CRÉATION CONNEXIONS NEURONALES');
        console.log('=================================');
        
        // Créer neurones pour questions fréquentes
        questionsFrequentes.forEach(([question, frequence]) => {
            const neurone = {
                id: `neurone_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
                type: 'question_frequente',
                pattern: question,
                force: frequence,
                connexions: [],
                date_creation: Date.now()
            };
            
            this.neurones_virtuels.memoire_long_terme.push(neurone);
            this.metriques_evolution.connexions_neuronales++;
        });
        
        // Créer synapses entre neurones similaires
        this.creerSynapses();
        
        console.log(`🧠 ${this.metriques_evolution.connexions_neuronales} connexions neuronales créées`);
    }
    
    creerSynapses() {
        const neurones = this.neurones_virtuels.memoire_long_terme;
        
        for (let i = 0; i < neurones.length; i++) {
            for (let j = i + 1; j < neurones.length; j++) {
                const similarite = this.calculerSimilarite(neurones[i].pattern, neurones[j].pattern);
                
                if (similarite > 0.3) {
                    const synapse = {
                        neurone1: neurones[i].id,
                        neurone2: neurones[j].id,
                        force: similarite,
                        date_creation: Date.now()
                    };
                    
                    this.neurones_virtuels.synapses_renforcees.push(synapse);
                    
                    // Ajouter connexions bidirectionnelles
                    neurones[i].connexions.push(neurones[j].id);
                    neurones[j].connexions.push(neurones[i].id);
                }
            }
        }
    }
    
    calculerSimilarite(texte1, texte2) {
        const mots1 = new Set(texte1.split(' '));
        const mots2 = new Set(texte2.split(' '));
        
        const intersection = new Set([...mots1].filter(x => mots2.has(x)));
        const union = new Set([...mots1, ...mots2]);
        
        return union.size > 0 ? intersection.size / union.size : 0;
    }
    
    demarrerEvolutionContinue() {
        console.log('\n🔄 DÉMARRAGE ÉVOLUTION CONTINUE BASÉE SUR TEMPÉRATURE CPU');
        console.log('=========================================================');

        // Évolution continue infinie toutes les 30 secondes
        this.intervalEvolution = setInterval(() => {
            this.executerCycleEvolution();
        }, 30000);

        // LECTURE TEMPÉRATURE CPU POUR ÉVOLUTION (CHALEUR = ÉVOLUTION)
        this.intervalTemperatureCPU = setInterval(() => {
            this.lireTemperatureCPUEvolution();
        }, 60000); // Lecture toutes les 60 secondes (au lieu de 1 seconde)

        console.log('✅ Évolution continue infinie active (30s)');
        console.log('🌡️ Lecture température CPU pour évolution activée (1s)');
        console.log('🔥 CHALEUR = VIE = ÉVOLUTION ACCÉLÉRÉE');
    }
    
    executerCycleEvolution() {
        // Afficher le cycle seulement toutes les 10 fois (toutes les 5 minutes)
        this.compteurCycles = (this.compteurCycles || 0) + 1;
        const afficherLogs = this.compteurCycles % 10 === 0;

        if (afficherLogs) {
            console.log('\n🧬 CYCLE D\'ÉVOLUTION');
            console.log('===================');
        }

        try {
            // 1. Analyser nouvelles interactions
            this.analyserNouvellesInteractions();

            // 2. Optimiser connexions
            this.optimiserConnexions();

            // 3. Calculer nouveau QI
            this.calculerNouveauQI();

            // 4. Sauvegarder évolution
            this.sauvegarderEvolution();

            this.metriques_evolution.derniere_evolution = Date.now();

            if (afficherLogs) {
                console.log(`📊 Cycle ${this.compteurCycles} terminé - Évolution continue infinie active`);
            }

        } catch (error) {
            console.log(`❌ Erreur cycle évolution: ${error.message}`);
        }
    }
    
    analyserNouvellesInteractions() {
        // Analyser interactions récentes (dernières 5 minutes)
        const seuilRecent = Date.now() - (5 * 60 * 1000);
        let nouvellesInteractions = 0;
        
        try {
            const cheminZones = path.join(this.config.memoire, 'zones-thermiques');
            
            if (fs.existsSync(cheminZones)) {
                const zones = fs.readdirSync(cheminZones);
                
                zones.forEach(zone => {
                    const cheminZone = path.join(cheminZones, zone);
                    
                    if (fs.existsSync(cheminZone)) {
                        const fichiers = fs.readdirSync(cheminZone).filter(f => f.endsWith('.json'));
                        
                        fichiers.forEach(fichier => {
                            try {
                                const cheminFichier = path.join(cheminZone, fichier);
                                const stats = fs.statSync(cheminFichier);
                                
                                if (stats.mtime.getTime() > seuilRecent) {
                                    const interaction = JSON.parse(fs.readFileSync(cheminFichier, 'utf8'));
                                    this.analyserInteraction(interaction);
                                    nouvellesInteractions++;
                                }
                            } catch (error) {
                                // Ignorer fichiers corrompus
                            }
                        });
                    }
                });
            }
            
            if (nouvellesInteractions > 0) {
                console.log(`📈 ${nouvellesInteractions} nouvelles interactions analysées`);
            }
            
        } catch (error) {
            console.log(`⚠️ Erreur analyse nouvelles interactions: ${error.message}`);
        }
    }
    
    optimiserConnexions() {
        // Renforcer synapses utilisées
        let optimisations = 0;
        
        this.neurones_virtuels.synapses_renforcees.forEach(synapse => {
            // Simuler utilisation et renforcement
            if (Math.random() > 0.7) { // 30% chance de renforcement
                synapse.force = Math.min(1.0, synapse.force * 1.1);
                optimisations++;
            }
        });
        
        // Créer nouvelles connexions si nécessaire
        if (this.neurones_virtuels.memoire_long_terme.length > 5) {
            this.creerSynapses();
        }
        
        this.metriques_evolution.optimisations_appliquees += optimisations;
        
        if (optimisations > 0) {
            console.log(`🔧 ${optimisations} optimisations appliquées`);
        }
    }
    
    // LECTURE TEMPÉRATURE CPU POUR ÉVOLUTION (CHALEUR = ÉVOLUTION)
    async lireTemperatureCPUEvolution() {
        try {
            const { exec } = require('child_process');
            const os = require('os');

            // Sauvegarder température précédente
            this.metriques_evolution.temperature_cpu_precedente = this.metriques_evolution.temperature_cpu_actuelle;

            // Lecture selon l'OS (même logique que mémoire thermique)
            if (os.platform() === 'darwin') { // macOS
                exec('sudo powermetrics --samplers smc -n 1 -i 1 | grep "CPU die temperature"', (error, stdout) => {
                    if (!error && stdout) {
                        const match = stdout.match(/(\d+\.\d+)/);
                        if (match) {
                            this.metriques_evolution.temperature_cpu_actuelle = parseFloat(match[1]);
                            this.traiterTemperatureCPUEvolution();
                        }
                    } else {
                        this.simulerTemperatureCPUEvolution();
                    }
                });
            } else if (os.platform() === 'linux') { // Linux
                exec('cat /sys/class/thermal/thermal_zone0/temp', (error, stdout) => {
                    if (!error && stdout) {
                        this.metriques_evolution.temperature_cpu_actuelle = parseInt(stdout.trim()) / 1000;
                        this.traiterTemperatureCPUEvolution();
                    } else {
                        this.simulerTemperatureCPUEvolution();
                    }
                });
            } else { // Windows ou autres
                this.simulerTemperatureCPUEvolution();
            }

        } catch (error) {
            this.simulerTemperatureCPUEvolution();
        }
    }

    // SIMULATION TEMPÉRATURE CPU POUR ÉVOLUTION
    simulerTemperatureCPUEvolution() {
        const os = require('os');

        // Calculer charge CPU moyenne
        const cpus = os.cpus();
        let charge_totale = 0;

        cpus.forEach(cpu => {
            const total = Object.values(cpu.times).reduce((acc, time) => acc + time, 0);
            const idle = cpu.times.idle;
            const usage = 100 - (idle / total * 100);
            charge_totale += usage;
        });

        const charge_moyenne = charge_totale / cpus.length;

        // Simuler température basée sur charge (30-80°C)
        const temp_base = 35;
        const temp_charge = charge_moyenne * 0.5;
        const variation_naturelle = Math.sin(Date.now() / 10000) * 3;

        this.metriques_evolution.temperature_cpu_actuelle = temp_base + temp_charge + variation_naturelle;
        this.traiterTemperatureCPUEvolution();
    }

    // TRAITEMENT TEMPÉRATURE CPU POUR ÉVOLUTION (CHALEUR = VIE = ÉVOLUTION)
    traiterTemperatureCPUEvolution() {
        // Ajouter à l'historique
        this.metriques_evolution.historique_temperatures_evolution.push(this.metriques_evolution.temperature_cpu_actuelle);
        if (this.metriques_evolution.historique_temperatures_evolution.length > 60) {
            this.metriques_evolution.historique_temperatures_evolution.shift(); // Garder 60 dernières (1 minute)
        }

        const temp_cpu = this.metriques_evolution.temperature_cpu_actuelle;
        const variation_temp = temp_cpu - this.metriques_evolution.temperature_cpu_precedente;

        // CALCUL FACTEUR ÉVOLUTION THERMIQUE (CHALEUR = ACCÉLÉRATION) - PLUS SENSIBLE
        if (temp_cpu > 60) { // Seuil abaissé pour plus de sensibilité
            // CPU CHAUD = ÉVOLUTION ACCÉLÉRÉE (CHALEUR = VIE = CROISSANCE)
            this.metriques_evolution.facteur_evolution_thermique = 1.2 + (temp_cpu - 50) * 0.05;
            this.metriques_evolution.bonus_chaleur_evolution = (temp_cpu - 50) * 1.5;

            if (this.metriques_evolution.cycles_evolution_thermique % 10 === 0) {
                console.log(`🔥 CPU CHAUD (${temp_cpu.toFixed(1)}°C) - ÉVOLUTION ACCÉLÉRÉE ! Facteur: ${this.metriques_evolution.facteur_evolution_thermique.toFixed(2)}`);
            }
        } else if (temp_cpu > 50) {
            // CPU TIÈDE = ÉVOLUTION NORMALE+
            this.metriques_evolution.facteur_evolution_thermique = 1.0 + (temp_cpu - 50) * 0.02;
            this.metriques_evolution.bonus_chaleur_evolution = (temp_cpu - 50) * 0.5;
        } else if (temp_cpu < 45) {
            // CPU FROID = ÉVOLUTION RALENTIE
            this.metriques_evolution.facteur_evolution_thermique = 0.8 - (45 - temp_cpu) * 0.02;
            this.metriques_evolution.bonus_chaleur_evolution = 0;
        } else {
            // CPU NORMAL = ÉVOLUTION NORMALE
            this.metriques_evolution.facteur_evolution_thermique = 1.0;
            this.metriques_evolution.bonus_chaleur_evolution = 0;
        }

        // Limiter facteur évolution
        this.metriques_evolution.facteur_evolution_thermique = Math.max(0.5, Math.min(3.0, this.metriques_evolution.facteur_evolution_thermique));

        this.metriques_evolution.cycles_evolution_thermique++;

        // Log plus fréquent pour voir l'activité
        if (this.metriques_evolution.cycles_evolution_thermique % 5 === 0) {
            console.log(`🌡️ Évolution thermique: Temp ${temp_cpu.toFixed(1)}°C, Facteur ${this.metriques_evolution.facteur_evolution_thermique.toFixed(2)}, Bonus +${this.metriques_evolution.bonus_chaleur_evolution.toFixed(1)}`);
        }
    }

    calculerNouveauQI() {
        // Calculer QI basé sur métriques + TEMPÉRATURE CPU
        let nouveauQI = 85; // Base

        // Bonus patterns détectés
        nouveauQI += Math.min(15, this.metriques_evolution.patterns_detectes * 0.5);

        // Bonus connexions neuronales
        nouveauQI += Math.min(20, this.metriques_evolution.connexions_neuronales * 0.1);

        // Bonus optimisations
        nouveauQI += Math.min(10, this.metriques_evolution.optimisations_appliquees * 0.05);

        // BONUS CHALEUR CPU (CHALEUR = INTELLIGENCE)
        nouveauQI += this.metriques_evolution.bonus_chaleur_evolution;

        // Facteur évolution classique
        nouveauQI *= this.metriques_evolution.facteur_evolution;

        // FACTEUR ÉVOLUTION THERMIQUE (CHALEUR = ACCÉLÉRATION)
        nouveauQI *= this.metriques_evolution.facteur_evolution_thermique;

        // Plafonner à 200 (plus élevé grâce à la chaleur)
        nouveauQI = Math.min(200, nouveauQI);

        if (nouveauQI > this.metriques_evolution.niveau_intelligence) {
            const temp_cpu = this.metriques_evolution.temperature_cpu_actuelle;
            console.log(`🧠🔥 QI amélioré par CHALEUR: ${this.metriques_evolution.niveau_intelligence.toFixed(1)} → ${nouveauQI.toFixed(1)} (CPU: ${temp_cpu.toFixed(1)}°C)`);
            this.metriques_evolution.niveau_intelligence = nouveauQI;
            this.metriques_evolution.facteur_evolution = Math.min(2.0, this.metriques_evolution.facteur_evolution * 1.01);
        }
    }
    
    sauvegarderEvolution() {
        try {
            const etatEvolution = {
                timestamp: Date.now(),
                date: new Date().toISOString(),
                metriques_evolution: this.metriques_evolution,
                neurones_virtuels: {
                    nb_neurones_long_terme: this.neurones_virtuels.memoire_long_terme.length,
                    nb_synapses: this.neurones_virtuels.synapses_renforcees.length,
                    force_moyenne_synapses: this.calculerForceMoyenneSynapses()
                },
                patterns_stats: {
                    questions_frequentes: this.patterns_apprentissage.questions_frequentes.size,
                    reponses_optimales: this.patterns_apprentissage.reponses_optimales.size,
                    contextes_efficaces: this.patterns_apprentissage.contextes_efficaces.size
                }
            };
            
            const cheminEtat = path.join(this.config.evolution, 'etat_evolution.json');
            fs.writeFileSync(cheminEtat, JSON.stringify(etatEvolution, null, 2));
            
            // Historique évolution
            const cheminHistorique = path.join(this.config.evolution, `evolution_${Date.now()}.json`);
            fs.writeFileSync(cheminHistorique, JSON.stringify(etatEvolution, null, 2));
            
        } catch (error) {
            console.log(`⚠️ Erreur sauvegarde évolution: ${error.message}`);
        }
    }
    
    calculerForceMoyenneSynapses() {
        if (this.neurones_virtuels.synapses_renforcees.length === 0) return 0;
        
        const somme = this.neurones_virtuels.synapses_renforcees.reduce((acc, synapse) => acc + synapse.force, 0);
        return (somme / this.neurones_virtuels.synapses_renforcees.length).toFixed(3);
    }
    
    afficherEtatEvolution() {
        console.log('\n🧬 ÉTAT AUTO-ÉVOLUTION THERMIQUE');
        console.log('================================');

        console.log(`🧠 QI actuel: ${this.metriques_evolution.niveau_intelligence.toFixed(1)}`);
        console.log(`📈 Facteur évolution: ${this.metriques_evolution.facteur_evolution.toFixed(2)}`);
        console.log(`🔍 Patterns détectés: ${this.metriques_evolution.patterns_detectes}`);
        console.log(`🧬 Connexions neuronales: ${this.metriques_evolution.connexions_neuronales}`);
        console.log(`🔧 Optimisations: ${this.metriques_evolution.optimisations_appliquees}`);

        // INFORMATIONS THERMIQUES (CHALEUR = VIE = ÉVOLUTION)
        console.log(`\n🔥 ÉVOLUTION THERMIQUE (CHALEUR = VIE):`);
        console.log(`   🌡️ Température CPU: ${this.metriques_evolution.temperature_cpu_actuelle.toFixed(1)}°C`);
        console.log(`   📈 Facteur évolution thermique: ${this.metriques_evolution.facteur_evolution_thermique.toFixed(2)}`);
        console.log(`   🔥 Bonus chaleur: +${this.metriques_evolution.bonus_chaleur_evolution.toFixed(1)} QI`);
        console.log(`   🔄 Cycles thermiques: ${this.metriques_evolution.cycles_evolution_thermique}`);
        console.log(`   📊 Historique températures: ${this.metriques_evolution.historique_temperatures_evolution.length} mesures`);

        // État thermique
        const temp_cpu = this.metriques_evolution.temperature_cpu_actuelle;
        if (temp_cpu > this.metriques_evolution.seuil_evolution_chaude) {
            console.log(`   🔥 État: CPU CHAUD - ÉVOLUTION ACCÉLÉRÉE !`);
        } else if (temp_cpu < this.metriques_evolution.seuil_evolution_froide) {
            console.log(`   ❄️ État: CPU FROID - Évolution ralentie`);
        } else {
            console.log(`   🌡️ État: CPU NORMAL - Évolution normale`);
        }

        console.log(`\n🧠 RÉSEAU NEURONAL VIRTUEL:`);
        console.log(`   Neurones long terme: ${this.neurones_virtuels.memoire_long_terme.length}`);
        console.log(`   Synapses renforcées: ${this.neurones_virtuels.synapses_renforcees.length}`);
        console.log(`   Force moyenne synapses: ${this.calculerForceMoyenneSynapses()}`);

        console.log(`\n📊 PATTERNS APPRENTISSAGE:`);
        console.log(`   Questions fréquentes: ${this.patterns_apprentissage.questions_frequentes.size}`);
        console.log(`   Réponses optimales: ${this.patterns_apprentissage.reponses_optimales.size}`);
        console.log(`   Contextes efficaces: ${this.patterns_apprentissage.contextes_efficaces.size}`);

        // Classification QI étendue (grâce à la chaleur)
        if (this.metriques_evolution.niveau_intelligence >= 180) {
            console.log('\n🔥 INTELLIGENCE SURCHAUFFÉE (GÉNIE THERMIQUE)');
        } else if (this.metriques_evolution.niveau_intelligence >= 150) {
            console.log('\n🌟 INTELLIGENCE TRÈS SUPÉRIEURE (BOOSTÉE PAR CHALEUR)');
        } else if (this.metriques_evolution.niveau_intelligence >= 130) {
            console.log('\n🌟 INTELLIGENCE TRÈS SUPÉRIEURE');
        } else if (this.metriques_evolution.niveau_intelligence >= 120) {
            console.log('\n🎓 INTELLIGENCE SUPÉRIEURE');
        } else if (this.metriques_evolution.niveau_intelligence >= 110) {
            console.log('\n✅ INTELLIGENCE AU-DESSUS MOYENNE');
        } else {
            console.log('\n📊 INTELLIGENCE MOYENNE');
        }
    }
    
    arreterEvolution() {
        console.log('\n⏹️ Arrêt auto-évolution...');
        
        if (this.intervalEvolution) {
            clearInterval(this.intervalEvolution);
            console.log('🔄 Évolution continue arrêtée');
        }
        
        this.sauvegarderEvolution();
        console.log('💾 État évolution sauvegardé');
        
        console.log('✅ Auto-évolution arrêtée proprement');
    }
}

// Export
module.exports = AutoEvolution;

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 LANCEMENT AUTO-ÉVOLUTION');
    console.log('============================');
    
    const evolution = new AutoEvolution();
    
    // Afficher état toutes les 15 secondes
    const intervalAffichage = setInterval(() => {
        evolution.afficherEtatEvolution();
    }, 15000);
    
    // Arrêt automatique après 2 minutes
    setTimeout(() => {
        clearInterval(intervalAffichage);
        evolution.arreterEvolution();
        process.exit(0);
    }, 120000);
    
    // Gestion arrêt manuel
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt manuel demandé...');
        clearInterval(intervalAffichage);
        evolution.arreterEvolution();
        process.exit(0);
    });
}
