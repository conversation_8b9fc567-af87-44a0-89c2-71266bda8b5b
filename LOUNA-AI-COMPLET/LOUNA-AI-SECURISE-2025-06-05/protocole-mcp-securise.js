/**
 * PROTOCOLE MCP SÉCURISÉ
 * Model Context Protocol avec protection maximale
 */

const https = require('https');
const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class ProtocoleMCPSecurise {
    constructor() {
        console.log('🔒 INITIALISATION PROTOCOLE MCP SÉCURISÉ');
        console.log('========================================');
        
        // Configuration sécurité MAXIMALE
        this.securite = {
            https_obligatoire: true,           // HTTPS uniquement
            verification_certificats: true,   // Vérifier SSL/TLS
            timeout_requetes: 10000,          // 10s timeout
            max_redirections: 3,              // Max 3 redirections
            user_agent_securise: 'LOUNA-AI-Secure/1.0',
            headers_securises: {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'close'
            }
        };
        
        // Domaines de confiance CERTIFIÉS
        this.domainesCertifies = new Set([
            'wikipedia.org',
            'www.wikipedia.org',
            'fr.wikipedia.org',
            'en.wikipedia.org',
            'scholar.google.com',
            'arxiv.org',
            'www.nature.com',
            'www.science.org',
            'pubmed.ncbi.nlm.nih.gov',
            'www.ncbi.nlm.nih.gov',
            'stackoverflow.com',
            'github.com',
            'developer.mozilla.org',
            'nodejs.org',
            'www.w3.org',
            'tools.ietf.org',
            'www.rfc-editor.org'
        ]);
        
        // Patterns dangereux à bloquer
        this.patternsDangereux = [
            /javascript:/i,
            /data:/i,
            /vbscript:/i,
            /onload/i,
            /onerror/i,
            /onclick/i,
            /<script/i,
            /<iframe/i,
            /eval\(/i,
            /document\.write/i,
            /\.exe$/i,
            /\.bat$/i,
            /\.cmd$/i,
            /\.scr$/i,
            /\.vbs$/i,
            /\.js$/i
        ];
        
        // Cache sécurisé
        this.cache = {
            chemin: path.join(__dirname, 'MCP-CACHE-SECURISE'),
            ttl: 3600000, // 1 heure
            max_taille: 100 * 1024 * 1024, // 100MB max
            pages_verifiees: new Map()
        };
        
        // Métriques sécurité
        this.metriques = {
            requetes_totales: 0,
            requetes_bloquees: 0,
            domaines_refuses: 0,
            pages_mises_en_cache: 0,
            erreurs_securite: 0,
            derniere_verification: Date.now()
        };
        
        // Créer structure cache
        this.creerCacheSecurise();
        
        console.log('✅ Protocole MCP sécurisé initialisé');
        console.log(`🔒 Domaines certifiés: ${this.domainesCertifies.size}`);
        console.log(`🛡️ Patterns dangereux: ${this.patternsDangereux.length}`);
        console.log(`💾 Cache: ${this.cache.chemin}`);
    }

    // RECHERCHE SÉCURISÉE
    async rechercherSecurise(requete, options = {}) {
        console.log(`\n🔍 RECHERCHE SÉCURISÉE MCP`);
        console.log(`Requête: "${requete}"`);
        
        this.metriques.requetes_totales++;
        
        try {
            // Nettoyer et valider requête
            const requeteNettoyee = this.nettoyerRequete(requete);
            
            // Construire URLs de recherche sécurisées
            const urlsRecherche = this.construireURLsSecurisees(requeteNettoyee);
            
            const resultats = [];
            
            for (const urlRecherche of urlsRecherche) {
                console.log(`🔍 Recherche sur: ${urlRecherche.domaine}`);
                
                try {
                    const resultat = await this.effectuerRechercheSecurisee(urlRecherche);
                    if (resultat) {
                        resultats.push(resultat);
                    }
                } catch (error) {
                    console.log(`⚠️ Erreur ${urlRecherche.domaine}: ${error.message}`);
                    this.metriques.erreurs_securite++;
                }
            }
            
            console.log(`✅ ${resultats.length} résultats sécurisés obtenus`);
            
            return {
                requete: requeteNettoyee,
                resultats: resultats,
                securite: {
                    domaines_verifies: true,
                    contenu_filtre: true,
                    cache_utilise: true
                },
                timestamp: Date.now()
            };
            
        } catch (error) {
            console.log(`❌ Erreur recherche sécurisée: ${error.message}`);
            this.metriques.erreurs_securite++;
            throw error;
        }
    }

    nettoyerRequete(requete) {
        // Supprimer caractères dangereux
        let nettoyee = requete
            .replace(/[<>\"']/g, '')
            .replace(/javascript:/gi, '')
            .replace(/data:/gi, '')
            .trim();
        
        // Vérifier patterns dangereux
        for (const pattern of this.patternsDangereux) {
            if (pattern.test(nettoyee)) {
                throw new Error(`Requête bloquée: pattern dangereux détecté`);
            }
        }
        
        return nettoyee;
    }

    construireURLsSecurisees(requete) {
        const urls = [];
        
        // Wikipedia (source la plus sûre)
        urls.push({
            domaine: 'fr.wikipedia.org',
            url: `https://fr.wikipedia.org/w/api.php?action=query&list=search&srsearch=${encodeURIComponent(requete)}&format=json&srlimit=3`,
            type: 'wikipedia_api'
        });
        
        // ArXiv pour recherches scientifiques
        if (requete.includes('neuron') || requete.includes('AI') || requete.includes('intelligence')) {
            urls.push({
                domaine: 'arxiv.org',
                url: `https://arxiv.org/search/?query=${encodeURIComponent(requete)}&searchtype=all&abstracts=show&order=-announced_date_first&size=3`,
                type: 'arxiv_search'
            });
        }
        
        // PubMed pour recherches médicales/biologiques
        if (requete.includes('brain') || requete.includes('neuron') || requete.includes('memory')) {
            urls.push({
                domaine: 'pubmed.ncbi.nlm.nih.gov',
                url: `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(requete)}&size=3`,
                type: 'pubmed_search'
            });
        }
        
        return urls;
    }

    async effectuerRechercheSecurisee(urlRecherche) {
        // Vérifier domaine certifié
        if (!this.verifierDomaineCertifie(urlRecherche.domaine)) {
            console.log(`🚫 Domaine non certifié: ${urlRecherche.domaine}`);
            this.metriques.domaines_refuses++;
            return null;
        }
        
        // Vérifier cache
        const cacheKey = this.genererClefCache(urlRecherche.url);
        const resultCache = await this.verifierCache(cacheKey);
        
        if (resultCache) {
            console.log(`💾 Résultat depuis cache sécurisé`);
            return resultCache;
        }
        
        // Effectuer requête sécurisée
        const contenu = await this.requeteHTTPSSecurisee(urlRecherche.url);
        
        // Filtrer contenu dangereux
        const contenuFiltre = this.filtrerContenuDangereux(contenu);
        
        // Parser selon type
        const resultatParse = await this.parserContenuSecurise(contenuFiltre, urlRecherche.type);
        
        // Mettre en cache
        await this.mettreEnCache(cacheKey, resultatParse);
        
        return resultatParse;
    }

    verifierDomaineCertifie(domaine) {
        // Extraire domaine principal
        const domaineNormalise = domaine.toLowerCase().replace(/^www\./, '');
        
        return this.domainesCertifies.has(domaine.toLowerCase()) || 
               this.domainesCertifies.has(domaineNormalise);
    }

    async requeteHTTPSSecurisee(urlCible) {
        return new Promise((resolve, reject) => {
            const parsedUrl = url.parse(urlCible);
            
            // Forcer HTTPS
            if (parsedUrl.protocol !== 'https:') {
                reject(new Error('HTTPS obligatoire'));
                return;
            }
            
            const options = {
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || 443,
                path: parsedUrl.path,
                method: 'GET',
                headers: {
                    ...this.securite.headers_securises,
                    'User-Agent': this.securite.user_agent_securise,
                    'Host': parsedUrl.hostname
                },
                timeout: this.securite.timeout_requetes,
                rejectUnauthorized: this.securite.verification_certificats
            };
            
            const req = https.request(options, (res) => {
                // Vérifier code de statut
                if (res.statusCode < 200 || res.statusCode >= 300) {
                    reject(new Error(`Code HTTP: ${res.statusCode}`));
                    return;
                }
                
                // Vérifier type de contenu
                const contentType = res.headers['content-type'] || '';
                if (!contentType.includes('text/') && !contentType.includes('application/json')) {
                    reject(new Error('Type de contenu non autorisé'));
                    return;
                }
                
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                    
                    // Limiter taille réponse
                    if (data.length > 1024 * 1024) { // 1MB max
                        req.destroy();
                        reject(new Error('Réponse trop volumineuse'));
                    }
                });
                
                res.on('end', () => {
                    resolve(data);
                });
            });
            
            req.on('error', (error) => {
                reject(new Error(`Erreur réseau: ${error.message}`));
            });
            
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Timeout de requête'));
            });
            
            req.end();
        });
    }

    filtrerContenuDangereux(contenu) {
        let contenuFiltre = contenu;
        
        // Supprimer scripts et éléments dangereux
        contenuFiltre = contenuFiltre
            .replace(/<script[^>]*>.*?<\/script>/gis, '')
            .replace(/<iframe[^>]*>.*?<\/iframe>/gis, '')
            .replace(/javascript:[^"']*/gi, '')
            .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
            .replace(/data:.*?;base64,[^"'\s]*/gi, '');
        
        // Vérifier patterns dangereux
        for (const pattern of this.patternsDangereux) {
            if (pattern.test(contenuFiltre)) {
                console.log(`⚠️ Pattern dangereux détecté et supprimé`);
                contenuFiltre = contenuFiltre.replace(pattern, '[CONTENU_BLOQUE]');
            }
        }
        
        return contenuFiltre;
    }

    async parserContenuSecurise(contenu, type) {
        switch (type) {
            case 'wikipedia_api':
                return this.parserWikipediaAPI(contenu);
                
            case 'arxiv_search':
                return this.parserArxivHTML(contenu);
                
            case 'pubmed_search':
                return this.parserPubmedHTML(contenu);
                
            default:
                return this.parserHTMLGenerique(contenu);
        }
    }

    parserWikipediaAPI(contenu) {
        try {
            const data = JSON.parse(contenu);
            const resultats = [];
            
            if (data.query && data.query.search) {
                for (const item of data.query.search.slice(0, 3)) {
                    resultats.push({
                        titre: item.title,
                        extrait: item.snippet.replace(/<[^>]*>/g, ''),
                        url: `https://fr.wikipedia.org/wiki/${encodeURIComponent(item.title)}`,
                        source: 'Wikipedia',
                        fiabilite: 0.9
                    });
                }
            }
            
            return {
                type: 'wikipedia_api',
                resultats: resultats,
                source_certifiee: true
            };
            
        } catch (error) {
            console.log(`⚠️ Erreur parsing Wikipedia: ${error.message}`);
            return { type: 'wikipedia_api', resultats: [], erreur: error.message };
        }
    }

    parserArxivHTML(contenu) {
        try {
            const resultats = [];

            // Parser basique pour ArXiv
            const titreMatch = contenu.match(/<title[^>]*>([^<]*)<\/title>/i);
            const titre = titreMatch ? titreMatch[1].trim() : 'Recherche ArXiv';

            // Extraire abstracts ou descriptions
            const abstracts = contenu.match(/<p[^>]*class[^>]*abstract[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/p>/gi) || [];

            if (abstracts.length === 0) {
                // Fallback: extraire premiers paragraphes
                const paragraphes = contenu.match(/<p[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/p>/gi) || [];
                abstracts.push(...paragraphes.slice(0, 3));
            }

            abstracts.slice(0, 3).forEach((abstract, index) => {
                const texte = abstract.replace(/<[^>]*>/g, '').trim();
                if (texte.length > 50) {
                    resultats.push({
                        titre: `${titre} - Résultat ${index + 1}`,
                        extrait: texte.substring(0, 200),
                        contenu: texte.substring(0, 500),
                        source: 'ArXiv',
                        fiabilite: 0.85
                    });
                }
            });

            return {
                type: 'arxiv_search',
                resultats: resultats,
                source_certifiee: true
            };

        } catch (error) {
            console.log(`⚠️ Erreur parsing ArXiv: ${error.message}`);
            return { type: 'arxiv_search', resultats: [], erreur: error.message };
        }
    }

    parserPubmedHTML(contenu) {
        try {
            const resultats = [];

            // Parser basique pour PubMed
            const titreMatch = contenu.match(/<title[^>]*>([^<]*)<\/title>/i);
            const titre = titreMatch ? titreMatch[1].trim() : 'Recherche PubMed';

            // Extraire résumés ou descriptions
            const resumes = contenu.match(/<div[^>]*class[^>]*abstract[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/div>/gi) || [];

            if (resumes.length === 0) {
                // Fallback: extraire premiers paragraphes
                const paragraphes = contenu.match(/<p[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/p>/gi) || [];
                resumes.push(...paragraphes.slice(0, 3));
            }

            resumes.slice(0, 3).forEach((resume, index) => {
                const texte = resume.replace(/<[^>]*>/g, '').trim();
                if (texte.length > 50) {
                    resultats.push({
                        titre: `${titre} - Article ${index + 1}`,
                        extrait: texte.substring(0, 200),
                        contenu: texte.substring(0, 500),
                        source: 'PubMed',
                        fiabilite: 0.9
                    });
                }
            });

            return {
                type: 'pubmed_search',
                resultats: resultats,
                source_certifiee: true
            };

        } catch (error) {
            console.log(`⚠️ Erreur parsing PubMed: ${error.message}`);
            return { type: 'pubmed_search', resultats: [], erreur: error.message };
        }
    }

    parserHTMLGenerique(contenu) {
        // Parser HTML basique et sécurisé
        const resultats = [];

        // Extraire titre
        const titreMatch = contenu.match(/<title[^>]*>([^<]*)<\/title>/i);
        const titre = titreMatch ? titreMatch[1].trim() : 'Sans titre';

        // Extraire description meta
        const descMatch = contenu.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)["']/i);
        const description = descMatch ? descMatch[1].trim() : '';

        // Extraire premiers paragraphes
        const paragraphes = contenu.match(/<p[^>]*>([^<]*(?:<[^>]*>[^<]*)*)<\/p>/gi) || [];
        const texte = paragraphes.slice(0, 3)
            .map(p => p.replace(/<[^>]*>/g, '').trim())
            .filter(p => p.length > 20)
            .join(' ');

        resultats.push({
            titre: titre,
            extrait: description || texte.substring(0, 200),
            contenu: texte.substring(0, 500),
            source: 'HTML générique',
            fiabilite: 0.6
        });

        return {
            type: 'html_generique',
            resultats: resultats,
            source_certifiee: true
        };
    }

    // CACHE SÉCURISÉ
    creerCacheSecurise() {
        if (!fs.existsSync(this.cache.chemin)) {
            fs.mkdirSync(this.cache.chemin, { recursive: true });
            console.log(`📁 Cache sécurisé créé: ${this.cache.chemin}`);
        }
    }

    genererClefCache(url) {
        return crypto.createHash('sha256').update(url).digest('hex');
    }

    async verifierCache(clef) {
        const fichierCache = path.join(this.cache.chemin, `${clef}.json`);
        
        try {
            if (fs.existsSync(fichierCache)) {
                const stats = fs.statSync(fichierCache);
                const age = Date.now() - stats.mtime.getTime();
                
                if (age < this.cache.ttl) {
                    const data = fs.readFileSync(fichierCache, 'utf8');
                    return JSON.parse(data);
                } else {
                    // Cache expiré
                    fs.unlinkSync(fichierCache);
                }
            }
        } catch (error) {
            console.log(`⚠️ Erreur cache: ${error.message}`);
        }
        
        return null;
    }

    async mettreEnCache(clef, donnees) {
        const fichierCache = path.join(this.cache.chemin, `${clef}.json`);
        
        try {
            fs.writeFileSync(fichierCache, JSON.stringify(donnees, null, 2));
            this.metriques.pages_mises_en_cache++;
        } catch (error) {
            console.log(`⚠️ Erreur mise en cache: ${error.message}`);
        }
    }

    // STATISTIQUES SÉCURITÉ
    obtenirStatistiquesSecurite() {
        return {
            metriques: this.metriques,
            securite: {
                domaines_certifies: this.domainesCertifies.size,
                patterns_dangereux: this.patternsDangereux.length,
                https_obligatoire: this.securite.https_obligatoire,
                verification_certificats: this.securite.verification_certificats
            },
            cache: {
                chemin: this.cache.chemin,
                taille_max: this.cache.max_taille,
                ttl_heures: this.cache.ttl / 3600000
            }
        };
    }

    afficherStatistiquesSecurite() {
        console.log('\n📊 STATISTIQUES SÉCURITÉ MCP');
        console.log('============================');
        
        const stats = this.obtenirStatistiquesSecurite();
        
        console.log(`🔍 RECHERCHES:`);
        console.log(`   Requêtes totales: ${stats.metriques.requetes_totales}`);
        console.log(`   Requêtes bloquées: ${stats.metriques.requetes_bloquees}`);
        console.log(`   Domaines refusés: ${stats.metriques.domaines_refuses}`);
        console.log(`   Erreurs sécurité: ${stats.metriques.erreurs_securite}`);
        
        console.log(`\n🔒 SÉCURITÉ:`);
        console.log(`   Domaines certifiés: ${stats.securite.domaines_certifies}`);
        console.log(`   Patterns dangereux: ${stats.securite.patterns_dangereux}`);
        console.log(`   HTTPS obligatoire: ${stats.securite.https_obligatoire ? 'OUI' : 'NON'}`);
        console.log(`   Vérification SSL: ${stats.securite.verification_certificats ? 'OUI' : 'NON'}`);
        
        console.log(`\n💾 CACHE:`);
        console.log(`   Pages en cache: ${stats.metriques.pages_mises_en_cache}`);
        console.log(`   TTL: ${stats.cache.ttl_heures}h`);
        console.log(`   Taille max: ${(stats.cache.taille_max / 1024 / 1024).toFixed(0)}MB`);
    }
}

// Export
module.exports = { ProtocoleMCPSecurise };

// Test automatique si exécuté directement
if (require.main === module) {
    console.log('🚀 TEST PROTOCOLE MCP SÉCURISÉ');
    console.log('==============================');
    
    const mcp = new ProtocoleMCPSecurise();
    
    // Test recherche sécurisée
    mcp.rechercherSecurise('neurone artificiel intelligence')
        .then(resultats => {
            console.log('\n✅ RÉSULTATS RECHERCHE SÉCURISÉE:');
            console.log(`   Requête: ${resultats.requete}`);
            console.log(`   Résultats: ${resultats.resultats.length}`);
            
            resultats.resultats.forEach((resultat, index) => {
                console.log(`\n   ${index + 1}. ${resultat.titre}`);
                console.log(`      Source: ${resultat.source}`);
                console.log(`      Fiabilité: ${(resultat.fiabilite * 100).toFixed(0)}%`);
            });
            
            // Afficher statistiques
            mcp.afficherStatistiquesSecurite();
        })
        .catch(error => {
            console.error('❌ Erreur test MCP:', error.message);
        });
}
