#!/usr/bin/env node

/**
 * 💾 SAUVEGARDE AUTOMATIQUE SIMPLE ET EFFICACE
 * 
 * Système de sauvegarde qui fonctionne vraiment :
 * - Sauvegarde continue toutes les 30 secondes
 * - Redondance multiple (local + externe)
 * - Vérification d'intégrité
 * - Récupération automatique
 * - Persistance infinie garantie
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SauvegardeAutomatiqueSimple {
    constructor() {
        console.log('💾 INITIALISATION SAUVEGARDE AUTOMATIQUE SIMPLE');
        console.log('================================================');
        
        this.config = {
            // Intervalles
            sauvegarde_continue: 30000,     // 30 secondes
            sauvegarde_externe: 300000,     // 5 minutes
            verification: 60000,            // 1 minute
            
            // Chemins
            dossier_local: './sauvegardes-auto',
            dossier_externe: '/Volumes/seagate/LOUNA-AI-BACKUP',
            
            // Fichiers critiques à sauvegarder
            fichiers_critiques: [
                'memoire-thermique-intelligente.json',
                'formations-sauvegardees.json',
                'systeme-unifie-etat.json',
                'config-louna-unifie.json',
                'conversations-data.json'
            ],
            
            // Redondance
            nb_copies_locales: 10,
            nb_copies_externes: 5
        };
        
        this.stats = {
            sauvegardes_reussies: 0,
            sauvegardes_echouees: 0,
            derniere_sauvegarde: null,
            fichiers_surveilles: 0,
            recuperations: 0,
            taille_totale: 0
        };
        
        this.checksums = new Map();
        this.timers = [];
        
        this.initialiser();
    }
    
    async initialiser() {
        try {
            // Créer dossiers
            this.creerDossiers();
            
            // Démarrer surveillance
            this.demarrerSurveillance();
            
            // Démarrer sauvegardes automatiques
            this.demarrerSauvegardesAuto();
            
            console.log('✅ Sauvegarde automatique simple opérationnelle');
            
        } catch (error) {
            console.error('❌ Erreur initialisation sauvegarde:', error.message);
        }
    }
    
    creerDossiers() {
        const dossiers = [
            this.config.dossier_local,
            `${this.config.dossier_local}/continue`,
            `${this.config.dossier_local}/externe`,
            `${this.config.dossier_local}/recovery`
        ];
        
        dossiers.forEach(dossier => {
            if (!fs.existsSync(dossier)) {
                fs.mkdirSync(dossier, { recursive: true });
                console.log(`📁 Dossier créé: ${dossier}`);
            }
        });
        
        // Créer dossier externe si disque disponible
        if (fs.existsSync('/Volumes/seagate')) {
            if (!fs.existsSync(this.config.dossier_externe)) {
                fs.mkdirSync(this.config.dossier_externe, { recursive: true });
                console.log(`💾 Dossier externe créé: ${this.config.dossier_externe}`);
            }
        }
    }
    
    demarrerSurveillance() {
        console.log('👁️ Démarrage surveillance fichiers critiques...');
        
        this.config.fichiers_critiques.forEach(fichier => {
            if (fs.existsSync(fichier)) {
                // Calculer checksum initial
                const checksum = this.calculerChecksum(fichier);
                this.checksums.set(fichier, checksum);
                
                // Surveiller modifications
                fs.watchFile(fichier, { interval: 5000 }, (curr, prev) => {
                    this.gererModification(fichier, curr, prev);
                });
                
                this.stats.fichiers_surveilles++;
                console.log(`👁️ Surveillance: ${fichier}`);
            }
        });
        
        console.log(`✅ ${this.stats.fichiers_surveilles} fichiers surveillés`);
    }
    
    async gererModification(fichier, curr, prev) {
        try {
            console.log(`🔄 Modification détectée: ${fichier}`);
            
            // Vérifier si vraiment modifié
            const nouveauChecksum = this.calculerChecksum(fichier);
            const ancienChecksum = this.checksums.get(fichier);
            
            if (nouveauChecksum !== ancienChecksum) {
                console.log(`💾 Sauvegarde immédiate: ${fichier}`);
                
                // Sauvegarder immédiatement
                await this.sauvegarderFichier(fichier);
                
                // Mettre à jour checksum
                this.checksums.set(fichier, nouveauChecksum);
            }
            
        } catch (error) {
            console.error(`❌ Erreur gestion modification ${fichier}:`, error.message);
        }
    }
    
    async sauvegarderFichier(fichier) {
        try {
            const timestamp = Date.now();
            const nomBase = path.basename(fichier, path.extname(fichier));
            const nomSauvegarde = `${nomBase}_${timestamp}.json`;
            
            // Lire contenu
            const contenu = fs.readFileSync(fichier);
            
            // Sauvegarder avec métadonnées
            const sauvegarde = {
                timestamp: timestamp,
                fichier_original: fichier,
                taille: contenu.length,
                checksum: this.calculerChecksum(fichier),
                contenu: contenu.toString()
            };
            
            // Écrire sauvegarde
            const cheminSauvegarde = path.join(this.config.dossier_local, 'continue', nomSauvegarde);
            fs.writeFileSync(cheminSauvegarde, JSON.stringify(sauvegarde, null, 2));
            
            this.stats.sauvegardes_reussies++;
            this.stats.derniere_sauvegarde = timestamp;
            this.stats.taille_totale += contenu.length;
            
            console.log(`✅ Sauvegarde: ${nomSauvegarde} (${this.formatTaille(contenu.length)})`);
            
            // Nettoyer anciennes sauvegardes
            this.nettoyerAnciennesSauvegardes(nomBase);
            
        } catch (error) {
            console.error(`❌ Erreur sauvegarde ${fichier}:`, error.message);
            this.stats.sauvegardes_echouees++;
        }
    }
    
    demarrerSauvegardesAuto() {
        console.log('⏰ Démarrage sauvegardes automatiques...');
        
        // Sauvegarde continue (30s)
        const timerContinue = setInterval(() => {
            this.effectuerSauvegardeContinue();
        }, this.config.sauvegarde_continue);
        this.timers.push(timerContinue);
        
        // Sauvegarde externe (5min)
        const timerExterne = setInterval(() => {
            this.effectuerSauvegardeExterne();
        }, this.config.sauvegarde_externe);
        this.timers.push(timerExterne);
        
        // Vérification intégrité (1min)
        const timerVerification = setInterval(() => {
            this.verifierIntegrite();
        }, this.config.verification);
        this.timers.push(timerVerification);
        
        console.log('✅ Timers de sauvegarde démarrés');
    }
    
    async effectuerSauvegardeContinue() {
        try {
            console.log('💾 Sauvegarde continue...');
            
            const timestamp = Date.now();
            const archive = {
                timestamp: timestamp,
                version: '1.0',
                type: 'sauvegarde_continue',
                fichiers: {},
                stats: { ...this.stats }
            };
            
            // Archiver tous les fichiers critiques
            for (const fichier of this.config.fichiers_critiques) {
                if (fs.existsSync(fichier)) {
                    try {
                        const contenu = fs.readFileSync(fichier, 'utf8');
                        archive.fichiers[fichier] = {
                            contenu: contenu,
                            checksum: this.calculerChecksum(fichier),
                            taille: contenu.length,
                            timestamp: fs.statSync(fichier).mtime.getTime()
                        };
                    } catch (error) {
                        console.log(`⚠️ Impossible de lire ${fichier}:`, error.message);
                    }
                }
            }
            
            // Sauvegarder archive
            const nomArchive = `archive_continue_${timestamp}.json`;
            const cheminArchive = path.join(this.config.dossier_local, nomArchive);
            
            fs.writeFileSync(cheminArchive, JSON.stringify(archive, null, 2));
            
            console.log(`✅ Archive continue: ${nomArchive}`);
            this.stats.sauvegardes_reussies++;
            
            // Nettoyer anciennes archives
            this.nettoyerArchives();
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde continue:', error.message);
            this.stats.sauvegardes_echouees++;
        }
    }
    
    async effectuerSauvegardeExterne() {
        if (!fs.existsSync('/Volumes/seagate')) {
            console.log('⚠️ Disque externe non disponible');
            return;
        }
        
        try {
            console.log('💽 Sauvegarde externe...');
            
            // Copier dernière archive vers externe
            const archives = fs.readdirSync(this.config.dossier_local)
                .filter(f => f.startsWith('archive_continue_'))
                .sort()
                .reverse();
            
            if (archives.length > 0) {
                const derniereArchive = archives[0];
                const source = path.join(this.config.dossier_local, derniereArchive);
                const destination = path.join(this.config.dossier_externe, derniereArchive);
                
                fs.copyFileSync(source, destination);
                console.log(`✅ Copie externe: ${derniereArchive}`);
                
                // Nettoyer anciennes sauvegardes externes
                this.nettoyerSauvegardesExternes();
            }
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde externe:', error.message);
        }
    }
    
    async verifierIntegrite() {
        try {
            console.log('🔍 Vérification intégrité...');
            
            let fichiersOK = 0;
            let fichiersKO = 0;
            
            for (const fichier of this.config.fichiers_critiques) {
                if (fs.existsSync(fichier)) {
                    const checksumActuel = this.calculerChecksum(fichier);
                    const checksumSurveillee = this.checksums.get(fichier);
                    
                    if (checksumSurveillee && checksumActuel === checksumSurveillee) {
                        fichiersOK++;
                    } else {
                        fichiersKO++;
                        console.log(`⚠️ Intégrité compromise: ${fichier}`);
                        
                        // Tentative de récupération
                        await this.recupererFichier(fichier);
                    }
                } else {
                    fichiersKO++;
                    console.log(`❌ Fichier manquant: ${fichier}`);
                    
                    // Tentative de récupération
                    await this.recupererFichier(fichier);
                }
            }
            
            console.log(`✅ Intégrité: ${fichiersOK} OK, ${fichiersKO} problèmes`);
            
        } catch (error) {
            console.error('❌ Erreur vérification intégrité:', error.message);
        }
    }
    
    async recupererFichier(fichier) {
        try {
            console.log(`🔧 Récupération: ${fichier}`);
            
            // Chercher dans sauvegardes continues
            const nomBase = path.basename(fichier, path.extname(fichier));
            const dossierContinue = path.join(this.config.dossier_local, 'continue');
            
            if (fs.existsSync(dossierContinue)) {
                const sauvegardes = fs.readdirSync(dossierContinue)
                    .filter(f => f.startsWith(nomBase))
                    .sort()
                    .reverse();
                
                for (const sauvegarde of sauvegardes) {
                    try {
                        const cheminSauvegarde = path.join(dossierContinue, sauvegarde);
                        const data = JSON.parse(fs.readFileSync(cheminSauvegarde, 'utf8'));
                        
                        if (data.contenu) {
                            // Créer backup du fichier corrompu
                            if (fs.existsSync(fichier)) {
                                const backupPath = `${fichier}.corrupted.${Date.now()}`;
                                fs.copyFileSync(fichier, backupPath);
                                console.log(`📋 Backup corrompu: ${backupPath}`);
                            }
                            
                            // Restaurer fichier
                            fs.writeFileSync(fichier, data.contenu);
                            
                            // Mettre à jour checksum
                            const nouveauChecksum = this.calculerChecksum(fichier);
                            this.checksums.set(fichier, nouveauChecksum);
                            
                            console.log(`✅ Fichier récupéré: ${fichier}`);
                            this.stats.recuperations++;
                            return true;
                        }
                    } catch (error) {
                        console.log(`⚠️ Sauvegarde ${sauvegarde} corrompue`);
                    }
                }
            }
            
            // Chercher dans archives
            const archives = fs.readdirSync(this.config.dossier_local)
                .filter(f => f.startsWith('archive_continue_'))
                .sort()
                .reverse();
            
            for (const archive of archives) {
                try {
                    const cheminArchive = path.join(this.config.dossier_local, archive);
                    const data = JSON.parse(fs.readFileSync(cheminArchive, 'utf8'));
                    
                    if (data.fichiers && data.fichiers[fichier]) {
                        const fichierData = data.fichiers[fichier];
                        
                        // Restaurer fichier
                        fs.writeFileSync(fichier, fichierData.contenu);
                        
                        // Mettre à jour checksum
                        const nouveauChecksum = this.calculerChecksum(fichier);
                        this.checksums.set(fichier, nouveauChecksum);
                        
                        console.log(`✅ Fichier récupéré depuis archive: ${fichier}`);
                        this.stats.recuperations++;
                        return true;
                    }
                } catch (error) {
                    console.log(`⚠️ Archive ${archive} corrompue`);
                }
            }
            
            console.error(`❌ Impossible de récupérer: ${fichier}`);
            return false;
            
        } catch (error) {
            console.error(`❌ Erreur récupération ${fichier}:`, error.message);
            return false;
        }
    }
    
    calculerChecksum(fichier) {
        try {
            const contenu = fs.readFileSync(fichier);
            return crypto.createHash('sha256').update(contenu).digest('hex');
        } catch (error) {
            return null;
        }
    }
    
    nettoyerAnciennesSauvegardes(nomBase) {
        try {
            const dossierContinue = path.join(this.config.dossier_local, 'continue');
            const sauvegardes = fs.readdirSync(dossierContinue)
                .filter(f => f.startsWith(nomBase))
                .sort()
                .reverse();
            
            if (sauvegardes.length > this.config.nb_copies_locales) {
                for (let i = this.config.nb_copies_locales; i < sauvegardes.length; i++) {
                    const fichierASupprimer = path.join(dossierContinue, sauvegardes[i]);
                    fs.unlinkSync(fichierASupprimer);
                }
            }
        } catch (error) {
            console.log('⚠️ Erreur nettoyage sauvegardes:', error.message);
        }
    }
    
    nettoyerArchives() {
        try {
            const archives = fs.readdirSync(this.config.dossier_local)
                .filter(f => f.startsWith('archive_continue_'))
                .sort()
                .reverse();
            
            if (archives.length > this.config.nb_copies_locales) {
                for (let i = this.config.nb_copies_locales; i < archives.length; i++) {
                    const fichierASupprimer = path.join(this.config.dossier_local, archives[i]);
                    fs.unlinkSync(fichierASupprimer);
                }
            }
        } catch (error) {
            console.log('⚠️ Erreur nettoyage archives:', error.message);
        }
    }
    
    nettoyerSauvegardesExternes() {
        try {
            if (!fs.existsSync(this.config.dossier_externe)) return;
            
            const sauvegardes = fs.readdirSync(this.config.dossier_externe)
                .filter(f => f.startsWith('archive_continue_'))
                .sort()
                .reverse();
            
            if (sauvegardes.length > this.config.nb_copies_externes) {
                for (let i = this.config.nb_copies_externes; i < sauvegardes.length; i++) {
                    const fichierASupprimer = path.join(this.config.dossier_externe, sauvegardes[i]);
                    fs.unlinkSync(fichierASupprimer);
                }
            }
        } catch (error) {
            console.log('⚠️ Erreur nettoyage externes:', error.message);
        }
    }
    
    formatTaille(bytes) {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
    
    obtenirStatistiques() {
        return {
            ...this.stats,
            derniere_sauvegarde_formatee: this.stats.derniere_sauvegarde ? 
                new Date(this.stats.derniere_sauvegarde).toLocaleString() : 'Jamais',
            taille_totale_formatee: this.formatTaille(this.stats.taille_totale),
            uptime: Date.now() - (this.stats.derniere_sauvegarde || Date.now())
        };
    }
    
    arreter() {
        console.log('🛑 Arrêt sauvegarde automatique...');
        
        // Arrêter tous les timers
        this.timers.forEach(timer => clearInterval(timer));
        this.timers = [];
        
        // Arrêter surveillance fichiers
        this.config.fichiers_critiques.forEach(fichier => {
            fs.unwatchFile(fichier);
        });
        
        console.log('✅ Sauvegarde automatique arrêtée');
    }
    
    // Méthode pour forcer une sauvegarde complète
    async forcerSauvegardeComplete() {
        console.log('🚀 Sauvegarde complète forcée...');
        
        await this.effectuerSauvegardeContinue();
        await this.effectuerSauvegardeExterne();
        await this.verifierIntegrite();
        
        console.log('✅ Sauvegarde complète terminée');
        return this.obtenirStatistiques();
    }
}

module.exports = { SauvegardeAutomatiqueSimple };
