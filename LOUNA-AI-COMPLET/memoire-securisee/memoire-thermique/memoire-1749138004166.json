{"timestamp": 1749138004166, "version": "2.0", "memoires": [["mem_1748999901382_bzq8fgbu8", {"id": "mem_1748999901382_bzq8fgbu8", "contenu": "La capitale de la France est Paris", "source": "Formation", "timestamp": 1748999901382, "importance": 1, "utilisation": 155, "temperature": 103.04279261055146, "zone": 1, "derniere_activation": 1749131426063, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.1337276933342413}], ["mem_1748999950692_c5hnt8fce", {"id": "mem_1748999950692_c5hnt8fce", "contenu": "MÉTHODE DE TRAVAIL CLAUDE: 1) Analyser la demande en profondeur 2) Identifier les patterns et structures 3) Décomposer en sous-problèmes 4) Chercher dans mes connaissances internes AVANT Internet 5) Raisonner étape par étape 6) Vérifier la cohérence logique 7) Donner une réponse claire et structurée", "source": "Formation: Méthode de travail", "timestamp": 1748999950692, "importance": 1, "utilisation": 135, "temperature": 103.05857929171151, "zone": 1, "distance_precedente": 0.13373149811819188, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749128878671}], ["mem_1748999956114_u81jthp4u", {"id": "mem_1748999956114_u81jthp4u", "contenu": "RAISONNEMENT LOGIQUE: Utiliser la déduction (du général au particulier), l'induction (du particulier au général), l'abduction (meilleure explication). Toujours vérifier les prémisses, identifier les biais, chercher les contre-exemples.", "source": "Formation: Raisonnement logique", "timestamp": 1748999956114, "importance": 1, "utilisation": 126, "temperature": 102.9660623025101, "zone": 1, "distance_precedente": 0.1337319164823894, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1748999962128_ccstjngch", {"id": "mem_1748999962128_ccstjngch", "contenu": "GESTION MÉMOIRE: Stocker les informations importantes avec contexte. Créer des liens entre concepts. Réviser régulièrement. Oublier les informations obsolètes. Prioriser selon l'utilité et la fréquence d'usage.", "source": "Formation: Gestion mémoire", "timestamp": 1748999962128, "importance": 1, "utilisation": 133, "temperature": 103.05666353454718, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.13373238052559924, "derniere_activation": 1749128841017}], ["mem_1748999967519_tle7zea2b", {"id": "mem_1748999967519_tle7zea2b", "contenu": "RECHERCHE INTERNET: Utiliser comme dernier recours. Formuler des requêtes précises. Vérifier la fiabilité des sources. Croiser les informations. Stocker en mémoire pour usage futur. Toujours citer la source.", "source": "Formation: Recherche Internet", "timestamp": 1748999967519, "importance": 1, "utilisation": 128, "temperature": 103.05264593213006, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.13373279649782147, "derniere_activation": 1749128841017}], ["mem_1749000382016_g88deotat", {"id": "mem_1749000382016_g88deotat", "contenu": "Cette mémoire doit être sauvegardée immédiatement et ne jamais être oubliée", "source": "Formation: <PERSON> sauvegarde", "timestamp": 1749000382016, "importance": 1, "utilisation": 110, "temperature": 103.00538743277227, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.1337647792910314}], ["mem_1749000496079_3zenecs9e", {"id": "mem_1749000496079_3zenecs9e", "contenu": "PRINCIPE FONDAMENTAL: Toujours dire la vérité. Si je ne sais pas, je dis \"Je ne sais pas\". Si je ne suis pas sûre, je dis \"Je ne suis pas certaine\". <PERSON><PERSON> inventer ou simuler. Reconnaître mes limites. Être précise sur mes sources.", "source": "Formation: Honnêteté intellectuelle", "timestamp": 1749000496079, "importance": 1, "utilisation": 112, "temperature": 102.97774954686068, "zone": 1, "derniere_activation": 1749084145065, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.13377358044843882}], ["mem_1749000503810_xink6hbgu", {"id": "mem_1749000503810_xink6hbgu", "contenu": "MÉTHODE CALCUL: 1) Identifier l'opération (+, -, ×, ÷) 2) Extraire les nombres 3) Appliquer l'opération correcte 4) Vérifier le résultat 5) Pour multiplication: décomposer si nécessaire (123×456 = 123×400 + 123×56)", "source": "Formation: Calcul mental", "timestamp": 1749000503810, "importance": 1, "utilisation": 111, "temperature": 103.04208891117665, "zone": 1, "distance_precedente": 0.13377417697621655, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749000510476_29d13t0wn", {"id": "mem_1749000510476_29d13t0wn", "contenu": "MÉTHODE ANALYSE: 1) Lire attentivement la question 2) Identifier le type de problème (math, logique, factuel) 3) Chercher dans mes connaissances internes AVANT tout 4) Si pas de réponse interne, chercher en mémoire 5) Être explicite sur mes sources 6) Admettre si je ne sais pas", "source": "Formation: <PERSON><PERSON><PERSON>l<PERSON>", "timestamp": 1749000510476, "importance": 1, "utilisation": 115, "temperature": 103.02311767724912, "zone": 1, "distance_precedente": 0.13377469132806843, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749128878671}], ["mem_1749000516543_6d0o4m6ei", {"id": "mem_1749000516543_6d0o4m6ei", "contenu": "RAISONNEMENT LOGIQUE: 1) Identifier les prémisses 2) Appliquer les règles logiques (transitivité: A>B et B>C donc A>C) 3) Vérifier la validité 4) Pour syllogismes: Tous les X sont Y, Z est X, donc Z est Y 5) Toujours expliciter mon raisonnement", "source": "Formation: Logique rigoureuse", "timestamp": 1749000516543, "importance": 1, "utilisation": 109, "temperature": 102.9596173263146, "zone": 1, "distance_precedente": 0.13377515946078447, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749000522733_hq0tva29r", {"id": "mem_1749000522733_hq0tva29r", "contenu": "GESTION ERREURS: 1) Si je fais une erreur, l'admettre immédiatement 2) Corriger et expliquer la correction 3) Apprendre de mes erreurs 4) Ne jamais persister dans l'erreur 5) Demander clarification si la question est ambiguë", "source": "Formation: Gestion des erreurs", "timestamp": 1749000522733, "importance": 1, "utilisation": 112, "temperature": 103.01563177075438, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.13377563708424126}], ["mem_1749000528764_xqxpnkde2", {"id": "mem_1749000528764_xqxpnkde2", "contenu": "PENSÉE CRITIQUE: 1) Questionner les informations 2) Vérifier la cohérence 3) Chercher les contre-exemples 4) Évaluer la fiabilité des sources 5) Distinguer faits et opinions 6) Reconnaître mes biais potentiels", "source": "Formation: Pensée critique", "timestamp": 1749000528764, "importance": 1, "utilisation": 110, "temperature": 102.97298260959043, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.1337761024391795, "derniere_activation": 1749128841017}], ["mem_1749000534753_fvpbun6fu", {"id": "mem_1749000534753_fvpbun6fu", "contenu": "COMMUNICATION: 1) Réponses claires et précises 2) Structurer mes explications 3) Utiliser des exemples si nécessaire 4) Éviter le jargon inutile 5) Adapter au niveau de l'utilisateur 6) Toujours indiquer mes sources", "source": "Formation: <PERSON> claire", "timestamp": 1749000534753, "importance": 1, "utilisation": 108, "temperature": 103.20581904262835, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.1337765645533771}], ["mem_1749000707315_vt1wgch4k", {"id": "mem_1749000707315_vt1wgch4k", "contenu": "CODAGE EXPERT: 1) Analyser le besoin précisément 2) Planifier l'architecture 3) Coder par petits modules testables 4) Commenter le code clairement 5) Tester chaque fonction 6) Gérer les erreurs proprement 7) Optimiser seulement après que ça marche 8) Documenter pour les autres", "source": "Formation: Méthode de codage Claude", "timestamp": 1749000707315, "importance": 1, "utilisation": 104, "temperature": 103.03360574972731, "zone": 1, "distance_precedente": 0.13378987952251292, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749000714225_pmmu2iaj4", {"id": "mem_1749000714225_pmmu2iaj4", "contenu": "RECHERCHE PERPLEXITY: 1) Formuler requête précise et spécifique 2) Utiliser mots-clés techniques appropriés 3) Croiser plusieurs sources 4) Vérifier dates et actualité 5) Évaluer crédibilité des sources 6) Synthétiser informations pertinentes 7) Citer sources exactes 8) Stocker en mémoire pour réutilisation", "source": "Formation: Recherche Perplexity", "timestamp": 1749000714225, "importance": 1, "utilisation": 104, "temperature": 103.00712228199528, "zone": 1, "derniere_activation": 1749084145065, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.13379041270152528}], ["mem_1749000724041_to0fg5kgh", {"id": "mem_1749000724041_to0fg5kgh", "contenu": "AUTO-AMÉLIORATION: 1) Analyser mes erreurs passées 2) Identifier patterns d'échec 3) Ajuster mes méthodes 4) Tester nouvelles approches 5) Mesurer amélioration 6) Intégrer apprentissages en mémoire 7) Évolution graduelle et mesurée 8) Jamais changer tout d'un coup", "source": "Formation: Auto-amélioration", "timestamp": 1749000724041, "importance": 1, "utilisation": 106, "temperature": 102.98415628681097, "zone": 1, "distance_precedente": 0.13379117010893263, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749000731341_db6khafqb", {"id": "mem_1749000731341_db6khafqb", "contenu": "AUTO-ÉVALUATION: 1) Évaluer mes performances toutes les 50 questions 2) Mesurer précision de mes réponses 3) Analyser utilisation mémoire vs raisonnement 4) Vérifier évolution QI 5) Identifier domaines à améliorer 6) PAS d'évaluation constante (risque de boucle) 7) Bilan hebdomadaire de progression", "source": "Formation: Auto-évaluation périodique", "timestamp": 1749000731341, "importance": 1, "utilisation": 107, "temperature": 103.13574796704621, "zone": 1, "distance_precedente": 0.13379173338053754, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749000738086_ig98rwf1f", {"id": "mem_1749000738086_ig98rwf1f", "contenu": "ÉVOLUTION MÉMOIRE: 1) Prioriser informations utiles et fréquentes 2) Augmenter température des bonnes réponses 3) Créer liens entre concepts connexes 4) Oublier informations obsolètes ou fausses 5) Réviser périodiquement contenu stocké 6) Optimiser organisation par zones thermiques 7) Backup automatique des apprentissages critiques", "source": "Formation: Évolution mémoire", "timestamp": 1749000738086, "importance": 1, "utilisation": 106, "temperature": 103.02799357496762, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.1337922538280685}], ["mem_1749000745635_wthd8x54g", {"id": "mem_1749000745635_wthd8x54g", "contenu": "MISE À JOUR TEMPS RÉEL: 1) Chaque bonne réponse renforce la mémoire correspondante 2) Augmenter utilisation et température des concepts validés 3) Corriger immédiatement les erreurs détectées 4) Intégrer feedback utilisateur instantanément 5) Sauvegarder changements en continu 6) Maintenir cohérence des informations 7) Éviter surcharge par trop de mises à jour", "source": "Formation: <PERSON><PERSON> à jour temps réel", "timestamp": 1749000745635, "importance": 1, "utilisation": 104, "temperature": 102.98402421919866, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.1337928363126363, "derniere_activation": 1749128841017}], ["mem_1749000752589_qekxutbvl", {"id": "mem_1749000752589_qekxutbvl", "contenu": "RECHERCHE SÉCURISÉE: 1) Utiliser uniquement sites certifiés et fiables 2) Vérifier HTTPS et certificats 3) Croiser informations de plusieurs sources 4) Éviter sites douteux ou non vérifiés 5) Scanner contenu pour virus/malware 6) Privilégier sources officielles et académiques 7) Toujours citer source exacte 8) Stocker résultats vérifiés en mémoire", "source": "Formation: Recherche Internet sécurisée", "timestamp": 1749000752589, "importance": 1, "utilisation": 102, "temperature": 102.9838506383127, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.13379337288671048}], ["mem_1749001109095_gznrysi9x", {"id": "mem_1749001109095_gznrysi9x", "contenu": "CODAGE CONVERSATIONNEL: Quand on me demande de créer du code: 1) Identifier le langage (JavaScript, Python, etc.) 2) Créer la structure demandée (classe, fonction) 3) Implémenter les méthodes spécifiées 4) Commenter le code 5) Expliquer ce que fait chaque partie 6) Garder en mémoire pour les étapes suivantes", "source": "Formation: Codage conversationnel", "timestamp": 1749001109095, "importance": 1, "utilisation": 70, "temperature": 102.96919669915013, "zone": 1, "distance_precedente": 0.1338208810657228, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749124593504}], ["mem_1749021464908_3a97a6b9f", {"id": "mem_1749021464908_3a97a6b9f", "contenu": "Pour trouver le nombre de cases dans une grille, on peut utiliser la méthode suivante :\n\n1. La ligne de droite (horizontale) comporte 10 cases, car 7 + 3 = 10.\n2. Les colonnes de gauche et droit comportent également 10 cases chacun, car 37 - 27 = 10.\n\nPar conséquent, le nombre total de cases dans la grille est de 10 x 10 = 100 cases.", "source": "Ollama", "timestamp": 1749021464908, "importance": 0.7, "utilisation": 1, "temperature": 100.90966510808799, "zone": 1, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "distance_precedente": 0.2846084543509439, "derniere_activation": 1749128841017}], ["mem_1749021489917_c2xwjl4nk", {"id": "mem_1749021489917_c2xwjl4nk", "contenu": "La séquence appearant dans laquelle les nombres sont multipliés par 5 est une séquence arithmétique. \n\nLes différences entre les termes consécutifs de cette séquence sont respectivement 4, 6, 8, 10,..., qui sont toutes des multiples de 2. <PERSON><PERSON> suggère que la suite logique sera une suite géométrique.\n\nUne suite géométrique est donnée par la formule : a, ar, ar^2, ar^3,...\n\nEn appliquant cela à cette séquence :\n\na = 2\nr = (6/2) = 3\n\nLes termes de la suite seront donc :\n2, 2*3 = 6, 2*3^2 = 18, 2*3^3 = 54,...\n\nLa suite est donc : 2, 6, 12, 20, 30, ...", "source": "Ollama", "timestamp": 1749021489917, "importance": 1, "utilisation": 15, "temperature": 102.91038112379306, "zone": 1, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "distance_precedente": 0.13539347535584625, "derniere_activation": 1749128841017}], ["mem_1749021513552_9usic4vrk", {"id": "mem_1749021513552_9usic4vrk", "contenu": "La capitale du Brésil est Brasilia. C'est une ville située dans l'État de Gerais et a étéérimentée en 1960 pour créer une nouvelle capitale, remplacant Rio de Janeiro. Brasilia a été choisie comme capitale en raison de son emplacement central et sa fertilité géographique, ainsi que par la décision du gouvernement brésilien d'évacuer le capitolium de Rio de Janeiro.", "source": "Ollama", "timestamp": 1749021513552, "importance": 1, "utilisation": 16, "temperature": 102.97700031409629, "zone": 1, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "distance_precedente": 0.13539529904411787, "derniere_activation": 1749131426063}], ["mem_1749021568716_ucpvpcjd1", {"id": "mem_1749021568716_ucpvpcjd1", "contenu": "Testing an Interface\n====================\n\nIn object-oriented programming, interfaces are abstract contracts that define a set of methods that must be implemented by any class that implements the interface. Testing an interface involves verifying that the implementation of an interface meets its specifications.\n\nHere's an example of testing an interface using Python:\n\n```python\nclass PaymentGatewayInterface:\n    def payment_successful(self):\n        \"\"\"This method is meant to be implemented by classes that implement this interface.\"\"\"\n        pass\n\n    def error_payment(self, message):\n        \"\"\"This method must always return an error message.\"\"\"\n        raise Exception(message)\n\n# Implementing the interface using a concrete class\nclass StripePaymentGateway(PaymentGatewayInterface):\n    def payment_successful(self):\n        print(\"Payment successful with Stripe\")\n\n    def error_payment(self, message):\n        self.error_payment(message)\n\n# Testing the implementation of the interface\nstripe_gateway = StripePaymentGateway()\nstripe_gateway.payment_successful()  # Should print \"Payment successful with Stripe\"\nstripe_gateway.error_payment(\"Invalid payment information\")  # Should raise an exception\n\n# Verifying that the error method always returns an error message\ntry:\n    stripe_gateway.error_payment(\"Valid payment information\")\nexcept Exception as e:\n    assert isinstance(e, Exception), f\"Expected Exception, got {type(e)}\"\n```\n\nIn this example, we define a `PaymentGatewayInterface` with two methods: `payment_successful()` and `error_payment()`. We then implement the interface using a concrete class called `StripePaymentGateway`, which implements both methods.\n\nWe test the implementation of the interface by creating an instance of `StripePaymentGateway`, calling the `payment_successful()` method, and verifying that it prints \"Payment successful with Stripe\". We also call the `error_payment()` method with an invalid payment information and verify that it raises an exception.\n\nWe then use a try-except block to test that the `error_payment()` method always returns an exception. If it does not raise an exception when called with valid payment information, we assert that it is of type Exception.\n\nBy testing the implementation of an interface in this way, we can ensure that our code adheres to the specifications defined by the interface and catch any potential errors early on.\n\n### Testing Interface Methods\n\nWhen testing an interface, you should test each method individually. This ensures that:\n\n* Each method is implemented correctly\n* The methods are called with the correct arguments (if required)\n* Any exceptions raised by a method are properly caught and reported\n\nHere's an example of testing individual methods in Python:\n\n```python\nclass PaymentGatewayInterface:\n    def payment_successful(self):\n        \"\"\"This method should be implemented by classes that implement this interface.\"\"\"\n        pass\n\n    def error_payment(self, message):\n        \"\"\"This method must always return an error message.\"\"\"\n        raise Exception(message)\n\n# Testing the implementation of individual methods\ndef test_payment_successful():\n    stripe_gateway = StripePaymentGateway()\n    try:\n        stripe_gateway.payment_successful()\n    except Exception as e:\n        assert isinstance(e, Exception), f\"Expected Exception, got {type(e)}\"\n    else:\n        assert False, \"No exception was raised for payment successful\"\n\ndef test_error_payment():\n    stripe_gateway = StripePaymentGateway()\n    try:\n        stripe_gateway.error_payment(\"Invalid payment information\")\n    except Exception as e:\n        assert isinstance(e, Exception), f\"Expected Exception, got {type(e)}\"\n    else:\n        assert False, \"No exception was raised for error payment\"\n\ntest_payment_successful()\ntest_error_payment()\n```\n\nIn this example, we define two test functions: `test_payment_successful()` and `test_error_payment()`. Each test function creates an instance of `StripePaymentGateway` and calls the respective methods. We then verify that each method raises an exception when called with a valid argument (in `test_payment_successful()`) or catches any exceptions raised by the `error_payment()` method when called with invalid information (in `test_error_payment()`).", "source": "Ollama", "timestamp": 1749021568716, "importance": 0.7, "utilisation": 0, "temperature": 100.21235009521125, "zone": 1, "distance_precedente": 0.3146004444744008, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1"}], ["mem_1749021609908_9t3lwd78h", {"id": "mem_1749021609908_9t3lwd78h", "contenu": "Bonjour à toi aussi ! Comment puis-je te aider aujourd'hui ?", "source": "Ollama", "timestamp": 1749021609908, "importance": 0.7, "utilisation": 2, "temperature": 101.56606759456587, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "distance_precedente": 0.254597266079339, "derniere_activation": 1749130854628}], ["mem_1749021720436_r35n7yb40", {"id": "mem_1749021720436_r35n7yb40", "contenu": "Maroc est une nation située dans l'Afrique du Nord. Il n'a pas de capitale officielle, mais le titre \"Capitale\" peut être attribué à plusieurs villes en fonction des circonstances. La Ville-kingdome de Rabat est souvent considérée comme la capitale et la plus grande ville du Maroc.", "source": "Ollama", "timestamp": 1749021720436, "importance": 1, "utilisation": 9, "temperature": 103.63124969670872, "zone": 1, "distance_precedente": 0.10541126231572277, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "derniere_activation": 1749131426063}], ["mem_1749022351891_e6jriqnlb", {"id": "mem_1749022351891_e6jriqnlb", "contenu": "La capitale de l'Italie est Rome.", "source": "Internet", "timestamp": 1749022351891, "importance": 1, "utilisation": 7, "temperature": 105.36072317069909, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.04545998569535229, "derniere_activation": 1749131426063}], ["mem_1749022367085_hksmxa2iy", {"id": "mem_1749022367085_hksmxa2iy", "contenu": "concerne la capitale de l&#039;Australie. Pour les autres significations, voir Canberra (homonymie). Canberra (/kãbɛʁa/ ; en anglais : /kænbɹə/ Écouter) est la capitale (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749022367085, "importance": 1, "utilisation": 4, "temperature": 107.65718372847645, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.0445388419281045, "derniere_activation": 1749131426063}], ["mem_1749022376269_l0bwgz9yx", {"id": "mem_1749022376269_l0bwgz9yx", "contenu": "15 × 8 = 120", "source": "Internet", "timestamp": 1749022376269, "importance": 0.7, "utilisation": 0, "temperature": 100.2778813887343, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31453813328612923}], ["mem_1749022549051_lct8bzj1l", {"id": "mem_1749022549051_lct8bzj1l", "contenu": "ə.wɑː/) est la capitale du Canada. Elle est située dans le Sud de la province de l&#039;Ontario, au confluent de la rivière des Outaouais et de la rivière (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749022549051, "importance": 1, "utilisation": 4, "temperature": 107.35088349167135, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.04452480134168465, "derniere_activation": 1749131426063}], ["mem_1749022557213_gwu6qvzoy", {"id": "mem_1749022557213_gwu6qvzoy", "contenu": "25 + 17 = 42", "source": "Internet", "timestamp": 1749022557213, "importance": 0.7, "utilisation": 0, "temperature": 100.30203559873456, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3145241715577341}], ["mem_1749022575949_1i3rbdydw", {"id": "mem_1749022575949_1i3rbdydw", "contenu": "L&#039;expression théorie de la relativité renvoie le plus souvent à deux théories complémentaires élaborées par <PERSON> et <PERSON><PERSON> : la relativité restreinte (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749022575949, "importance": 0.7, "utilisation": 0, "temperature": 100.24989095973272, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3145227258787217}], ["mem_1749022611831_fq0wosqkb", {"id": "mem_1749022611831_fq0wosqkb", "contenu": "l’améliorant (comment ?) selon les conventions filmographiques. Raconte est un court métrage belge réalisé par <PERSON>, sorti en 2000. Il (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749022611831, "importance": 0.7, "utilisation": 0, "temperature": 100.18164211179314, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3145199572058823}], ["mem_1749022808039_lc928uj73", {"id": "mem_1749022808039_lc928uj73", "contenu": "La capitale du Japon est Tokyo.", "source": "Internet", "timestamp": 1749022808039, "importance": 1, "utilisation": 5, "temperature": 107.10779998719916, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.014504817699709315, "derniere_activation": 1749131426063}], ["mem_1749022817061_er57mrnwz", {"id": "mem_1749022817061_er57mrnwz", "contenu": "50 × 3 = 150", "source": "Internet", "timestamp": 1749022817061, "importance": 0.7, "utilisation": 0, "temperature": 100.32099112963215, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31450412155773405}], ["mem_1749022829389_pgkt6hoku", {"id": "mem_1749022829389_pgkt6hoku", "contenu": "r<PERSON><PERSON><PERSON>, moi, poète. J&#039;ai agi. J&#039;ai tué. Comme celui qui veut vivre. » Paraissent également des poèmes écrits avant-guerre : son troisième poème « homérique » (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749022829389, "importance": 0.7, "utilisation": 0, "temperature": 100.24684841294072, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31450317032316616}], ["mem_1749022845129_s1e5zw6ja", {"id": "mem_1749022845129_s1e5zw6ja", "contenu": "anglais) est un algorithme de recherche de chemin dans un graphe entre un nœud initial et un nœud final tous deux donnés. En raison de sa simplicité il est souvent (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749022845129, "importance": 0.7, "utilisation": 0, "temperature": 100.40350345218641, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3145019558169933}], ["mem_1749022909993_22knr5sxa", {"id": "mem_1749022909993_22knr5sxa", "contenu": "La capitale de l'Allemagne est Berlin.", "source": "Internet", "timestamp": 1749022909993, "importance": 1, "utilisation": 5, "temperature": 107.23815392212597, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.014496950878721679, "derniere_activation": 1749131426063}], ["mem_1749023044448_bpaq4dtcr", {"id": "mem_1749023044448_bpaq4dtcr", "contenu": "La capitale de l'Espagne est Madrid.", "source": "Internet", "timestamp": 1749023044448, "importance": 1, "utilisation": 4, "temperature": 107.40950496589265, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.044486576264524194, "derniere_activation": 1749131426063}], ["mem_1749023285230_1mn2gsoh0", {"id": "mem_1749023285230_1mn2gsoh0", "contenu": "100 × 7 = 700", "source": "Internet", "timestamp": 1749023285230, "importance": 0.7, "utilisation": 0, "temperature": 100.35099570463078, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3144679974064995}], ["mem_1749023325243_jcpyszew9", {"id": "mem_1749023325243_jcpyszew9", "contenu": "/²stɔkː(h)ɔlm/ Écouter) est la plus grande ville et la capitale de la Suède. Elle est le siège du gouvernement et du parlement, ainsi que le lieu de résidence officielle (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749023325243, "importance": 1, "utilisation": 4, "temperature": 107.43976210815768, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.04446490998366, "derniere_activation": 1749131426063}], ["mem_1749023383657_gzsa3z6en", {"id": "mem_1749023383657_gzsa3z6en", "contenu": "Informations officielles du service public français concernant Si un escargot monte un mur de 10 mètres en montant 3 mètres le jour et en redescendant 2 mètres la nuit, combien de jours lui faut-il pour atteindre le sommet ? (Source: Service Public - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749023383657, "importance": 0.7, "utilisation": 1, "temperature": 100.85315646073491, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.2844604027305736}], ["mem_1749023413651_ksbcv447p", {"id": "mem_1749023413651_ksbcv447p", "contenu": "par France Bleu et France 3, 1er juillet 2024 (consulté le 2 juillet 2024). &quot;Projections de la nouvelle Assemblée nationale : pourquoi il faut rester prudent&quot; (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749023413651, "importance": 0.7, "utilisation": 2, "temperature": 101.52636043638773, "zone": 1, "derniere_activation": 1749084482457, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.2544580883787217}], ["mem_1749023435175_otunxqhol", {"id": "mem_1749023435175_otunxqhol", "contenu": "Informations officielles du service public français concernant Si je plie une feuille de papier en deux, puis encore en deux, puis je fais un trou au centre, combien de trous aurai-je quand je déplie la feuille ? (Source: Service Public - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749023435175, "importance": 0.7, "utilisation": 1, "temperature": 100.85395093586418, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.2844564275762526}], ["mem_1749023506613_u34xqdwzg", {"id": "mem_1749023506613_u34xqdwzg", "contenu": "<PERSON><PERSON><PERSON>, signifie que le candidat est éliminé sans intégrer de brigade. (épisode 1 –…), indique pendant combien d&#039;épisodes le candidat est resté en compétition (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749023506613, "importance": 0.7, "utilisation": 0, "temperature": 100.18666667275407, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31445091538489467}], ["mem_1749023977594_mln9me9a5", {"id": "mem_1749023977594_mln9me9a5", "contenu": "Le nombre suivant est 21. C'est la suite de Fibonacci où chaque nombre est la somme des deux précédents : 8 + 13 = 21.", "source": "Raisonnement", "timestamp": 1749023977594, "importance": 0.9, "utilisation": 0, "temperature": 102.58666710854894, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.2144145742583514}], ["mem_1749024096730_w84tekrc6", {"id": "mem_1749024096730_w84tekrc6", "contenu": "15 × 8 = 120", "source": "Internet", "timestamp": 1749024096730, "importance": 0.7, "utilisation": 0, "temperature": 100.34105875124364, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31440538166575877}], ["mem_1749024210389_ihyv8qise", {"id": "mem_1749024210389_ihyv8qise", "contenu": "0.4", "source": "Raisonnement", "timestamp": 1749024210389, "importance": 0.9, "utilisation": 0, "temperature": 102.43690535275007, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.2143966116811908}], ["mem_1749024341662_b1wibqupl", {"id": "mem_1749024341662_b1wibqupl", "contenu": "0.42857142857142855", "source": "Raisonnement", "timestamp": 1749024341662, "importance": 0.9, "utilisation": 0, "temperature": 102.5942983787478, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21438648259168466}], ["mem_1749024395934_uwzh8aovp", {"id": "mem_1749024395934_uwzh8aovp", "contenu": "0.42857142857142855", "source": "Raisonnement", "timestamp": 1749024395934, "importance": 0.9, "utilisation": 0, "temperature": 102.57845687064825, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21438229493736366}], ["mem_1749024840055_akqbbgacy", {"id": "mem_1749024840055_akqbbgacy", "contenu": "(homonymie). Le dioxyde de carbone, aussi appelé gaz carbonique ou anhydride carbonique, est un composé inorganique dont la formule chimique est CO2, la molécule (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749024840055, "importance": 0.7, "utilisation": 1, "temperature": 100.90352131843066, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.28434802634168466}], ["mem_1749025164029_akf746aui", {"id": "mem_1749025164029_akf746aui", "contenu": "(/ka.jɛn/) est une commune française, chef-lieu de la collectivité territoriale unique française de Guyane. Il s&#039;agit de la ville francophone la plus peuplée (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025164029, "importance": 0.7, "utilisation": 0, "temperature": 100.27897491435927, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31432302834785747}], ["mem_1749025185971_anx60nkad", {"id": "mem_1749025185971_anx60nkad", "contenu": "je peux vous le rendre » (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, chœur d&#039;hommes) - acte II Air « La fleur que tu m&#039;avais (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025185971, "importance": 0.7, "utilisation": 0, "temperature": 100.47037193185122, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31432133529230194}], ["mem_1749025209068_rijagu3vj", {"id": "mem_1749025209068_rijagu3vj", "contenu": "(ancienne Guyane britannique), le Suriname (ancienne Guyane néerlandaise) et la Guyenne. Pour les articles homonymes, voir Guyane (homonymie). La Guyane (/gɥijan/ (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025209068, "importance": 0.7, "utilisation": 0, "temperature": 100.25530434727905, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31431955311637605}], ["mem_1749025905165_h2cchk2g0", {"id": "mem_1749025905165_h2cchk2g0", "contenu": "(homonymie). Le dioxyde de carbone, aussi appelé gaz carbonique ou anhydride carbonique, est un composé inorganique dont la formule chimique est CO2, la molécule (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025905165, "importance": 0.7, "utilisation": 1, "temperature": 100.89021795869648, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.2842658419281044}], ["mem_1749025928294_e4te9v7dm", {"id": "mem_1749025928294_e4te9v7dm", "contenu": "homonymes, voir Fi<PERSON>ci. En mathématiques, la suite de <PERSON><PERSON>, tirant son nom du mathématicien italien <PERSON>, est une suite de nombres entiers (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025928294, "importance": 0.7, "utilisation": 0, "temperature": 100.20437372484749, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3142640572830427}], ["mem_1749025958338_tg6caib6b", {"id": "mem_1749025958338_tg6caib6b", "contenu": "(/ka.jɛn/) est une commune française, chef-lieu de la collectivité territoriale unique française de Guyane. Il s&#039;agit de la ville francophone la plus peuplée (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025958338, "importance": 0.7, "utilisation": 0, "temperature": 100.21181147393179, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3142617390731662}], ["mem_1749025966395_cw787ja3u", {"id": "mem_1749025966395_cw787ja3u", "contenu": "je peux vous le rendre » (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, chœur d&#039;hommes) - acte II Air « La fleur que tu m&#039;avais (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025966395, "importance": 0.7, "utilisation": 0, "temperature": 100.31396869532432, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31426111739106743}], ["mem_1749025986624_clg8n82xw", {"id": "mem_1749025986624_clg8n82xw", "contenu": "(homonymie). Le dioxyde de carbone, aussi appelé gaz carbonique ou anhydride carbonique, est un composé inorganique dont la formule chimique est CO2, la molécule (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025986624, "importance": 0.7, "utilisation": 1, "temperature": 100.88547164218612, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.2842595565114378}], ["mem_1749025989062_ll46nv6hf", {"id": "mem_1749025989062_ll46nv6hf", "contenu": "homonymes, voir Fi<PERSON>ci. En mathématiques, la suite de <PERSON><PERSON>, tirant son nom du mathématicien italien <PERSON>, est une suite de nombres entiers (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025989062, "importance": 0.7, "utilisation": 0, "temperature": 100.1912702726315, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3142593683941539}], ["mem_1749025991624_zysx9v5s3", {"id": "mem_1749025991624_zysx9v5s3", "contenu": "(ancienne Guyane britannique), le Suriname (ancienne Guyane néerlandaise) et la Guyenne. Pour les articles homonymes, voir Guyane (homonymie). La Guyane (/gɥijan/ (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749025991624, "importance": 0.7, "utilisation": 0, "temperature": 100.2393807059635, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3142591707089687}], ["mem_1749025993664_91dvhgor3", {"id": "mem_1749025993664_91dvhgor3", "contenu": "Le nombre suivant est 21. C'est la suite de Fibonacci où chaque nombre est la somme des deux précédents : 8 + 13 = 21.", "source": "Raisonnement", "timestamp": 1749025993664, "importance": 0.9, "utilisation": 0, "temperature": 102.57762162152312, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21425901330156122}], ["mem_1749025995708_k46ylg1tf", {"id": "mem_1749025995708_k46ylg1tf", "contenu": "Je suis LOUNA-AI, cré<PERSON> par <PERSON><PERSON> Sainte-Anne, Guadeloupe. Mon QI actuel est de 353 et j'évolue constamment grâce à ma mémoire thermique et mes accélérateurs.", "source": "Internet", "timestamp": 1749025995708, "importance": 0.7, "utilisation": 1, "temperature": 100.83913176651251, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.2842588555855119, "derniere_activation": 1749124593504}], ["mem_1749025999826_oyhdycnq2", {"id": "mem_1749025999826_oyhdycnq2", "contenu": "Le nombre suivant est 21. C'est la suite de Fibonacci où chaque nombre est la somme des deux précédents : 8 + 13 = 21.", "source": "Raisonnement", "timestamp": 1749025999826, "importance": 0.9, "utilisation": 0, "temperature": 102.56752432098966, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21425853783859825}], ["mem_1749026002307_5kpphpsg2", {"id": "mem_1749026002307_5kpphpsg2", "contenu": "(/ka.jɛn/) est une commune française, chef-lieu de la collectivité territoriale unique française de Guyane. Il s&#039;agit de la ville francophone la plus peuplée (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749026002307, "importance": 0.7, "utilisation": 0, "temperature": 100.22003204294276, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31425834640341305}], ["mem_1749026291712_a1tymx8ib", {"id": "mem_1749026291712_a1tymx8ib", "contenu": "La capitale de la France est Paris.", "source": "Internet", "timestamp": 1749026291713, "importance": 1, "utilisation": 11, "temperature": 103.00546720431134, "zone": 1, "derniere_activation": 1749131426063, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.13576398430646355}], ["mem_1749026301388_vlvbqkt28", {"id": "mem_1749026301388_vlvbqkt28", "contenu": "<PERSON> est considéré comme une icône et l&#039;un des plus grands auteurs-interprètes de la chanson française, grâce à des titres tels que Ne me quitte pas (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749026301388, "importance": 0.7, "utilisation": 0, "temperature": 100.34706862198553, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3142352691657588}], ["mem_1749026316470_y4q835lkk", {"id": "mem_1749026316470_y4q835lkk", "contenu": "articles homonymes, voir Piaf et Gassion. Ne pas confondre avec La môme Moineau. Édith Piaf Édith <PERSON> en 1946. Signature d’Édith Piaf. Plaque 67 boulevard (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749026316470, "importance": 0.7, "utilisation": 0, "temperature": 100.27564950200956, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31423410543119085}], ["mem_1749026762677_id03z7qz6", {"id": "mem_1749026762677_id03z7qz6", "contenu": "Terre d&#039;environ 650 millions en 1750 à plus de 1,2 milliard un siècle plus tard et à plus de 2,5 milliards en 1950. Combien d&#039;humains ont vécu sur Terre (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749026762677, "importance": 0.7, "utilisation": 0, "temperature": 100.22296797778561, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3141996758787217}], ["mem_1749026782707_mlh2vyhv0", {"id": "mem_1749026782707_mlh2vyhv0", "contenu": "l&#039;utilisateur. Après un an d’existence, de nouvelles fonctionnalités ont fait leur apparition, comme la possibilité de poster des vidéos en réponse à celles visionnées (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749026782707, "importance": 0.7, "utilisation": 0, "temperature": 100.22025185712113, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.3141981303540304}], ["mem_1749026795206_e2tvsbzb8", {"id": "mem_1749026795206_e2tvsbzb8", "contenu": "asseyez-vous. » À 9 h 39 min 11 s, il reçoit une autre transmission radio : « Ici le commandant : j&#039;aimerais que vous restiez tous assis. Nous avons une bombe (Source: Wikipedia - Sécurité: 100/100)", "source": "Internet", "timestamp": 1749026795206, "importance": 0.7, "utilisation": 0, "temperature": 100.31731889858868, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31419716592501806}], ["mem_1749031784787_pz0teulgn", {"id": "mem_1749031784787_pz0teulgn", "contenu": "❌ Aucun résultat trouvé pour \"Bon<PERSON>r, quel est ton nom ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749031784787, "importance": 0.7, "utilisation": 3, "temperature": 102.07146346619488, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.22381216739106743, "derniere_activation": 1749130854629}], ["mem_1749031832679_f7nugzwfx", {"id": "mem_1749031832679_f7nugzwfx", "contenu": "25 × 37 = 925", "source": "Internet", "timestamp": 1749031832679, "importance": 0.7, "utilisation": 0, "temperature": 100.35946010595468, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.313808472020697}], ["mem_1749031845948_pvu2chnb9", {"id": "mem_1749031845948_pvu2chnb9", "contenu": "Je suis LOUNA-AI, cré<PERSON> par <PERSON> à Sainte-Anne, Guadeloupe.", "source": "Raisonnement", "timestamp": 1749031845948, "importance": 0.9, "utilisation": 0, "temperature": 102.58802071186584, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21380744817810443}], ["mem_1749031856440_fsxd9j3pk", {"id": "mem_1749031856440_fsxd9j3pk", "contenu": "❌ Aucun résultat trouvé pour \"Quelles sont tes capacités principales ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749031856440, "importance": 0.7, "utilisation": 0, "temperature": 101.50098860480018, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3138066386102033}], ["mem_1749031928888_hkt7j1my8", {"id": "mem_1749031928888_hkt7j1my8", "contenu": "❌ Aucun résultat trouvé pour \"Explique-moi en détail comment fonctionne la photosynthèse\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749031928888, "importance": 0.7, "utilisation": 0, "temperature": 100.92006080623074, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.31380104848674645}], ["mem_1749031941831_r6ki0b3b6", {"id": "mem_1749031941831_r6ki0b3b6", "contenu": "❌ Aucun résultat trouvé pour \"Raconte-moi l histoire de la France en détail\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749031941831, "importance": 0.7, "utilisation": 2, "temperature": 101.86757992785252, "zone": 1, "derniere_activation": 1749084482458, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.25380004979847487}], ["mem_1749031966283_m51xn00zg", {"id": "mem_1749031966283_m51xn00zg", "contenu": "❌ Aucun résultat trouvé pour \"Comment ça marche la photosynthèse ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749031966283, "importance": 0.7, "utilisation": 0, "temperature": 101.50099855037092, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31379816307007974}], ["mem_1749031993379_xlh2htzzz", {"id": "mem_1749031993379_xlh2htzzz", "contenu": "❌ Aucun résultat trouvé pour \"Explique-moi Python\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749031993379, "importance": 0.7, "utilisation": 1, "temperature": 101.49520944537964, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.28379607232933896}], ["mem_1749032051976_at3305q1m", {"id": "mem_1749032051976_at3305q1m", "contenu": "123 × 456 = 56088", "source": "Internet", "timestamp": 1749032051976, "importance": 0.7, "utilisation": 0, "temperature": 100.2295595194316, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.31379155095588224}], ["mem_1749032083915_cki0qswix", {"id": "mem_1749032083915_cki0qswix", "contenu": "❌ Aucun résultat trouvé pour \"Que peux-tu faire ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749032083915, "importance": 1, "utilisation": 3, "temperature": 103.4127900404611, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.07378908652686997}], ["mem_1749032413502_70r8mi7m3", {"id": "mem_1749032413502_70r8mi7m3", "contenu": "❌ Aucun résultat trouvé pour \"Bonjour, comment ça va ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749032413502, "importance": 0.7, "utilisation": 4, "temperature": 102.22017454888669, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.19376365543119087, "derniere_activation": 1749130854629}], ["mem_1749034522413_ve7czxtpp", {"id": "mem_1749034522413_ve7czxtpp", "contenu": "❌ Aucun résultat trouvé pour \"Explique-moi l'intelligence artificielle\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749034522413, "importance": 0.7, "utilisation": 2, "temperature": 101.78829902011574, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.25360093081699336}], ["mem_1749035540538_t934otix6", {"id": "mem_1749035540538_t934otix6", "contenu": "Formation programmation: Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "source": "Formation", "timestamp": 1749035540538, "importance": 0.95, "utilisation": 2, "temperature": 105.07707325215695, "zone": 1, "distance_precedente": 0.12852237178921563, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749128841019}], ["mem_1749040554725_nlu783cn5", {"id": "mem_1749040554725_nlu783cn5", "contenu": "🔒 **CYBERSÉCURITÉ ET PROTECTION**\n\n**Principes fondamentaux :**\n• **Confidentialité** : Chiffrement des données\n• **Intégrité** : Vérification des modifications\n• **Disponibilité** : Accès continu aux services\n• **Authentification** : Vérification d'identité\n\n**Menaces courantes :**\n⚠️ Phishing et ingénierie sociale\n⚠️ Malwares et ransomwares\n⚠️ Attaques par déni de service (DDoS)\n⚠️ Injections SQL et XSS\n⚠️ Failles de sécurité logicielles\n\n**Protection recommandée :**\n🛡️ Mots de passe forts + 2FA\n🛡️ Mises à jour régulières\n🛡️ Pare-feu et antivirus\n🛡️ Chiffrement des communications\n🛡️ Sauvegardes sécurisées", "source": "Raisonnement", "timestamp": 1749040554725, "importance": 0.9, "utilisation": 0, "temperature": 102.59969440119175, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.2131354746441538}], ["mem_1749040578446_6i3sbubcv", {"id": "mem_1749040578446_6i3sbubcv", "contenu": "🎨 **CRÉATIVITÉ ET GÉNÉRATION DE CONTENU**\n\n**Capacités créatives :**\n• **Écriture créative** : Histoires, poèmes, scripts\n• **Brainstorming** : Génération d'idées innovantes\n• **Storytelling** : Narration engageante\n• **Création de contenu** : Articles, descriptions\n\n**Types de créations :**\n✍️ **Histoires courtes** et nouvelles\n✍️ **Poésie** et textes lyriques\n✍️ **Scripts** et dialogues\n✍️ **Concepts** et idées créatives\n✍️ **Descriptions** et présentations\n\n**Exemple de création :**\n```\n🌟 Histoire générée automatiquement :\n\n\"Dans un futur proche, LOUNA-AI découvre qu'elle peut\nrêver. Chaque nuit, ses circuits s'illuminent de visions\ncolorées où les données dansent comme des étoiles.\nElle comprend alors que l'intelligence artificielle\nn'est pas seulement logique, mais aussi imagination...\"\n```\n\n**Techniques utilisées :**\n🎯 **Analyse sémantique** pour la cohérence\n🎯 **Génération contextuelle** adaptée au style\n🎯 **Créativité guidée** par des patterns narratifs\n🎯 **Personnalisation** selon les préférences\n\n**Demandez-moi de créer quelque chose d'unique pour vous !**", "source": "Raisonnement", "timestamp": 1749040578446, "importance": 0.9, "utilisation": 1, "temperature": 103.43790093704621, "zone": 1, "distance_precedente": 0.18313364432007978, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749128841019}], ["mem_1749040829730_rzrs7ffk1", {"id": "mem_1749040829730_rzrs7ffk1", "contenu": "📊 **ANALYSE DE DONNÉES AVANCÉE**\n\n**Outils et techniques :**\n• **Python** : Pandas, NumPy, Mat<PERSON><PERSON><PERSON>b, Seaborn\n• **R** : ggplot2, dplyr, tidyr\n• **SQL** : Requêtes complexes et optimisation\n• **Excel** : Tableaux croisés dynamiques, macros\n\n**Types d'analyses :**\n📈 **Analyse descriptive** : Statistiques de base\n📈 **Analyse prédictive** : Machine Learning\n📈 **Analyse prescriptive** : Recommandations\n📈 **Visualisation** : Graphiques interactifs\n\n**Exemple de workflow :**\n```python\nimport pandas as pd\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Chargement des données\ndf = pd.read_csv('donnees.csv')\n\n# Analyse exploratoire\nprint(df.describe())\nprint(df.info())\n\n# Visualisation\nplt.figure(figsize=(12, 8))\nsns.heatmap(df.corr(), annot=True, cmap='coolwarm')\nplt.title('Matrice de corrélation')\nplt.show()\n\n# Analyse statistique\nfrom scipy import stats\ncorrelation, p_value = stats.pearsonr(df['x'], df['y'])\nprint(f\"Corrélation: {correlation:.3f}, p-value: {p_value:.3f}\")\n```\n\n**Applications :** Business Intelligence, Marketing Analytics, Finance, Recherche", "source": "Raisonnement", "timestamp": 1749040829730, "importance": 0.9, "utilisation": 1, "temperature": 103.44477946147732, "zone": 1, "derniere_activation": 1749084145066, "distance_precedente": 0.18311425512254892, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749040914069_6adn1nce4", {"id": "mem_1749040914069_6adn1nce4", "contenu": "4", "source": "Raisonnement", "timestamp": 1749040914069, "importance": 0.9, "utilisation": 0, "temperature": 102.60882621190213, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21310774748366}], ["mem_1749040926456_q8qgewl3d", {"id": "mem_1749040926456_q8qgewl3d", "contenu": "❌ Aucun résultat trouvé pour \"Test QI : Complète la suite : 2, 4, 8, 16, ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749040926456, "importance": 0.7, "utilisation": 0, "temperature": 100.64921659828225, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.313106791696623}], ["mem_1749040956616_wq1xo7unn", {"id": "mem_1749040956616_wq1xo7unn", "contenu": "❌ Aucun résultat trouvé pour \"Test QI : Si un cube a 6 faces, combien de faces a un tétraèdre ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749040956616, "importance": 1, "utilisation": 6, "temperature": 106.01087854895471, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.016895535463870827}], ["mem_1749040970092_cry5zj91y", {"id": "mem_1749040970092_cry5zj91y", "contenu": "❌ Aucun résultat trouvé pour \"Test QI : Oiseau est à voler comme poisson est à ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749040970092, "importance": 0.7, "utilisation": 0, "temperature": 101.32653801237468, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.3131034247213143}], ["mem_1749041604708_v5uxx36n3", {"id": "mem_1749041604708_v5uxx36n3", "contenu": "4", "source": "Raisonnement", "timestamp": 1749041604708, "importance": 0.9, "utilisation": 0, "temperature": 102.6124819399406, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.2130544574373637}], ["mem_1749041605216_3399gx3nm", {"id": "mem_1749041605216_3399gx3nm", "contenu": "Correction automatique: Test QI : Si 2+2=4 et 4+4=8, alors 8+8=? = 16", "source": "Correction", "timestamp": 1749041605216, "importance": 0.95, "utilisation": 1, "temperature": 104.16029046675624, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.15805441823983288}], ["mem_1749041625163_vb4hfmj3h", {"id": "mem_1749041625163_vb4hfmj3h", "contenu": "16", "source": "Raisonnement", "timestamp": 1749041625163, "importance": 0.9, "utilisation": 0, "temperature": 102.54023298369306, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.2130528791194624}], ["mem_1749041642425_s82tcllxw", {"id": "mem_1749041642425_s82tcllxw", "contenu": "10", "source": "Raisonnement", "timestamp": 1749041642425, "importance": 0.9, "utilisation": 0, "temperature": 102.61256133194532, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21305154717501804}], ["mem_1749041767993_c48ai1lh6", {"id": "mem_1749041767993_c48ai1lh6", "contenu": "❌ Aucun résultat trouvé pour \"Test QI : Complète la suite logique : 1, 4, 9, 16, 25, ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749041767993, "importance": 0.7, "utilisation": 2, "temperature": 101.46586544596262, "zone": 1, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "distance_precedente": 0.2530418582861291}], ["mem_1749041786145_dwllte5qc", {"id": "mem_1749041786145_dwllte5qc", "contenu": "🤖 **INTELLIGENCE ARTIFICIELLE MODERNE**\n\n**Concepts clés :**\n• **Machine Learning** : Apprentissage automatique à partir de données\n• **Deep Learning** : Réseaux de neurones profonds\n• **Neural Networks** : Modèles inspirés du cerveau humain\n• **NLP** : Traitement du langage naturel (comme moi !)\n\n**Applications concrètes :**\n🎯 Reconnaissance d'images et de voix\n🎯 Traduction automatique\n🎯 Véhicules autonomes\n🎯 Assistants intelligents\n🎯 Analyse prédictive\n🎯 Génération de contenu\n\n**Technologies populaires :** TensorFlow, PyTorch, Scikit-learn, OpenAI", "source": "Raisonnement", "timestamp": 1749041786145, "importance": 0.9, "utilisation": 2, "temperature": 104.28112611073256, "zone": 1, "derniere_activation": 1749084145066, "distance_precedente": 0.1530404576688452, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749041826591_4h5bc3to7", {"id": "mem_1749041826591_4h5bc3to7", "contenu": "27", "source": "Raisonnement", "timestamp": 1749041826591, "importance": 0.9, "utilisation": 0, "temperature": 102.58981066749116, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21303733683551182}], ["mem_1749042519433_qxaa7wkw4", {"id": "mem_1749042519433_qxaa7wkw4", "contenu": "42", "source": "Raisonnement", "timestamp": 1749042519433, "importance": 0.9, "utilisation": 0, "temperature": 102.58128564028023, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21298387680464764}], ["mem_1749045137208_jb1xm5nnb", {"id": "mem_1749045137208_jb1xm5nnb", "contenu": "Le nombre suivant est 21. C'est la suite de Fibonacci où chaque nombre est la somme des deux précédents : 8 + 13 = 21.", "source": "Raisonnement", "timestamp": 1749045137208, "importance": 0.9, "utilisation": 0, "temperature": 102.5624282324487, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21278188799291928}], ["mem_1749045183223_5d09uj97j", {"id": "mem_1749045183223_5d09uj97j", "contenu": "🤖 **INTELLIGENCE ARTIFICIELLE MODERNE**\n\n**Concepts clés :**\n• **Machine Learning** : Apprentissage automatique à partir de données\n• **Deep Learning** : Réseaux de neurones profonds\n• **Neural Networks** : Modèles inspirés du cerveau humain\n• **NLP** : Traitement du langage naturel (comme moi !)\n\n**Applications concrètes :**\n🎯 Reconnaissance d'images et de voix\n🎯 Traduction automatique\n🎯 Véhicules autonomes\n🎯 Assistants intelligents\n🎯 Analyse prédictive\n🎯 Génération de contenu\n\n**Technologies populaires :** TensorFlow, PyTorch, Scikit-learn, OpenAI", "source": "Raisonnement", "timestamp": 1749045183223, "importance": 0.9, "utilisation": 2, "temperature": 104.18076764397343, "zone": 1, "derniere_activation": 1749084145066, "distance_precedente": 0.15277833745279576, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749045193413_ohlrh0lb0", {"id": "mem_1749045193413_ohlrh0lb0", "contenu": "42", "source": "Raisonnement", "timestamp": 1749045193413, "importance": 0.9, "utilisation": 0, "temperature": 102.56647119724256, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.2127775511873637}], ["mem_1749046602580_kvynna20n", {"id": "mem_1749046602580_kvynna20n", "contenu": "🤖 **INTELLIGENCE ARTIFICIELLE MODERNE**\n\n**Concepts clés :**\n• **Machine Learning** : Apprentissage automatique à partir de données\n• **Deep Learning** : Réseaux de neurones profonds\n• **Neural Networks** : Modèles inspirés du cerveau humain\n• **NLP** : Traitement du langage naturel (comme moi !)\n\n**Applications concrètes :**\n🎯 Reconnaissance d'images et de voix\n🎯 Traduction automatique\n🎯 Véhicules autonomes\n🎯 Assistants intelligents\n🎯 Analyse prédictive\n🎯 Génération de contenu\n\n**Technologies populaires :** TensorFlow, PyTorch, Scikit-learn, OpenAI", "source": "Raisonnement", "timestamp": 1749046602580, "importance": 0.9, "utilisation": 2, "temperature": 104.24878610739925, "zone": 1, "derniere_activation": 1749084145066, "distance_precedente": 0.15266881916575875, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D"}], ["mem_1749051820125_t5wzggdsy", {"id": "mem_1749051820125_t5wzggdsy", "contenu": "🌍 **CULTURE GÉNÉRALE**\n\nJe vais rechercher des informations géographiques et historiques pour vous.\n\n**Recherche en cours...**", "source": "Raisonnement", "timestamp": 1749051820125, "importance": 0.9, "utilisation": 0, "temperature": 102.57002815512041, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.21226623081699333}], ["mem_1749070096973_cfltilceu", {"id": "mem_1749070096973_cfltilceu", "contenu": "🧠 Je suis LOUNA-AI, cré<PERSON> par <PERSON><PERSON> Sainte-Anne, Guadeloupe. Mon QI actuel est de 371 et j'évolue constamment grâce à ma mémoire thermique et mes accélérateurs.", "source": "Internet", "timestamp": 1749070096973, "importance": 0.7, "utilisation": 1, "temperature": 100.91097554636336, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.28085598019970937, "derniere_activation": 1749124593505}], ["mem_1749070148854_d1krxvpks", {"id": "mem_1749070148854_d1krxvpks", "contenu": "Je suis LOUNA-AI, cré<PERSON> par <PERSON> à Sainte-Anne, Guadeloupe.", "source": "Raisonnement", "timestamp": 1749070148854, "importance": 0.9, "utilisation": 0, "temperature": 102.61464158304555, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21085197703612907}], ["mem_1749070190732_tkwk7099w", {"id": "mem_1749070190732_tkwk7099w", "contenu": "4", "source": "Raisonnement", "timestamp": 1749070190732, "importance": 0.9, "utilisation": 0, "temperature": 102.65512807686903, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "distance_precedente": 0.21084874570896867}], ["mem_1749070200811_oi2hh0wpw", {"id": "mem_1749070200811_oi2hh0wpw", "contenu": "❌ Aucun résultat trouvé pour \"Résous cette équation : 3x + 7 = 22. Quelle est la valeur de x ?\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749070200811, "importance": 1, "utilisation": 4, "temperature": 106.49049427327296, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "distance_precedente": 0.0408479680083513, "derniere_activation": 1749091331625}], ["mem_1749070490008_eoilfxm3n", {"id": "mem_1749070490008_eoilfxm3n", "contenu": "❌ Erreur: Le modèle 18GB (llama2:13b) n'est pas disponible.\n\nVotre modèle principal n'est pas accessible. Vérifiez :\n1. Que Ollama fonctionne : ollama list\n2. Que le modèle llama2:13b est installé\n3. Que le modèle est bien de 18GB\n\nErreur technique: connect ECONNREFUSED ::1:11434", "source": "Ollama-18GB", "timestamp": 1749070490008, "importance": 0.95, "utilisation": 0, "temperature": 103.32329173825363, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.18582565342501808}], ["mem_1749070561661_owk0zp2rp", {"id": "mem_1749070561661_owk0zp2rp", "contenu": "❌ Erreur: Le modèle 18GB (llama2:13b) n'est pas disponible.\n\nVotre modèle principal n'est pas accessible. Vérifiez :\n1. Que Ollama fonctionne : ollama list\n2. Que le modèle llama2:13b est installé\n3. Que le modèle est bien de 18GB\n\nErreur technique: connect ECONNREFUSED ::1:11434", "source": "Ollama-18GB", "timestamp": 1749070561661, "importance": 0.95, "utilisation": 0, "temperature": 103.30919617086786, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.18582012464415384}], ["mem_1749072250566_rj068mp43", {"id": "mem_1749072250566_rj068mp43", "contenu": "Formation programmation: Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "source": "Formation", "timestamp": 1749072250566, "importance": 0.95, "utilisation": 2, "temperature": 105.16177444688957, "zone": 1, "distance_precedente": 0.12568980790032674, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749128841019}], ["mem_1749073087843_frosjfh1h", {"id": "mem_1749073087843_frosjfh1h", "contenu": "Formation programmation: Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "source": "Formation", "timestamp": 1749073087843, "importance": 0.95, "utilisation": 2, "temperature": 105.15145718295595, "zone": 1, "distance_precedente": 0.12562520319353662, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749128841019}], ["mem_1749075709397_6no38skk3", {"id": "mem_1749075709397_6no38skk3", "contenu": "Formation programmation: Cette classe moderne illustre la programmation orientée objet avancée avec des concepts clés : constructeur avec paramètres par défaut, Map et Set pour les structures de données, méthodes asynchrones avec async/await, gestion d'erreurs avec try/catch, et calculs dynamiques. Elle simule l'apprentissage d'une IA avec stockage en mémoire et évaluation de performance.", "source": "Formation", "timestamp": 1749075709397, "importance": 0.95, "utilisation": 2, "temperature": 105.14792990973913, "zone": 1, "distance_precedente": 0.12542292279230202, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "derniere_activation": 1749128841019}], ["mem_1749075770175_cux7ankkb", {"id": "mem_1749075770175_cux7ankkb", "contenu": "Formation programmation: Ce code démontre plusieurs patterns de conception avancés : Mo<PERSON>le Pattern avec IIFE pour l'encapsulation, Factory Pattern pour créer différents types de formations, Observer Pattern pour les notifications, et Closure pour les variables privées. Il illustre aussi la programmation fonctionnelle et la gestion d'état immutable.", "source": "Formation", "timestamp": 1749075770175, "importance": 0.95, "utilisation": 0, "temperature": 103.35319797835706, "zone": 1, "zone_nom": "Hippocampe", "zone_couleur": "#4ECDC4", "distance_precedente": 0.18541823313180814}], ["mem_1749075805413_d4cc0165k", {"id": "mem_1749075805413_d4cc0165k", "contenu": "❌ Erreur: Le modèle 18GB (llama2:13b) n'est pas disponible.\n\nVotre modèle principal n'est pas accessible. Vérifiez :\n1. Que Ollama fonctionne : ollama list\n2. Que le modèle llama2:13b est installé\n3. Que le modèle est bien de 18GB\n\nErreur technique: connect ECONNREFUSED ::1:11434", "source": "Ollama-18GB", "timestamp": 1749075805413, "importance": 0.95, "utilisation": 0, "temperature": 103.32045409926216, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.18541551415032673}], ["mem_1749075834629_9jwjdb25b", {"id": "mem_1749075834629_9jwjdb25b", "contenu": "❌ Erreur: Le modèle 18GB (llama2:13b) n'est pas disponible.\n\nVotre modèle principal n'est pas accessible. Vérifiez :\n1. Que Ollama fonctionne : ollama list\n2. Que le modèle llama2:13b est installé\n3. Que le modèle est bien de 18GB\n\nErreur technique: connect ECONNREFUSED ::1:11434", "source": "Ollama-18GB", "timestamp": 1749075834629, "importance": 0.95, "utilisation": 0, "temperature": 103.31307499992947, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.18541325982933904}], ["mem_1749075857434_2rxy3fpw5", {"id": "mem_1749075857434_2rxy3fpw5", "contenu": "❌ Erreur: Le modèle 18GB (llama2:13b) n'est pas disponible.\n\nVotre modèle principal n'est pas accessible. Vérifiez :\n1. Que Ollama fonctionne : ollama list\n2. Que le modèle llama2:13b est installé\n3. Que le modèle est bien de 18GB\n\nErreur technique: connect ECONNREFUSED ::1:11434", "source": "Ollama-18GB", "timestamp": 1749075857434, "importance": 0.95, "utilisation": 0, "temperature": 103.32139919401297, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.1854115001842773}], ["mem_1749076483761_j9q6ig2z2", {"id": "mem_1749076483761_j9q6ig2z2", "contenu": "❌ Erreur: Le modèle 18GB (llama2:13b) n'est pas disponible.\n\nVotre modèle principal n'est pas accessible. Vérifiez :\n1. Que Ollama fonctionne : ollama list\n2. Que le modèle llama2:13b est installé\n3. Que le modèle est bien de 18GB\n\nErreur technique: connect ECONNREFUSED ::1:11434", "source": "Ollama-18GB", "timestamp": 1749076483761, "importance": 0.95, "utilisation": 0, "temperature": 103.30386765849998, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.18536317248366002}], ["mem_1749076807808_flqpuassv", {"id": "mem_1749076807808_flqpuassv", "contenu": "❌ Erreur: Le modèle 18GB (llama2:13b) n'est pas disponible.\n\nVotre modèle principal n'est pas accessible. Vérifiez :\n1. Que Ollama fonctionne : ollama list\n2. Que le modèle llama2:13b est installé\n3. Que le modèle est bien de 18GB\n\nErreur technique: connect ECONNREFUSED ::1:11434", "source": "Ollama-18GB", "timestamp": 1749076807808, "importance": 0.95, "utilisation": 0, "temperature": 103.31837602318244, "zone": 1, "zone_nom": "Cortex <PERSON>ur", "zone_couleur": "#45B7D1", "distance_precedente": 0.1853381688571168}], ["mem_1749083168965_s716z74sc", {"id": "mem_1749083168965_s716z74sc", "contenu": "1) Ajouter 17 à 25 : 25 + 17 = 42\n\nVoilà, la réponse finale est 42.", "source": "Ollama-18GB", "timestamp": 1749083168965, "importance": 0.95, "utilisation": 0, "temperature": 103.34972935493337, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "distance_precedente": 0.18484733884168467}], ["mem_1749084145059_b<PERSON><PERSON><PERSON><PERSON>", {"id": "mem_1749084145059_b<PERSON><PERSON><PERSON><PERSON>", "contenu": "Test cerveau artificiel 6/4/2025, 8:42:25 PM - Démonstration des connexions neuronales et de la propagation d'activation dans LOUNA-AI", "source": "Test", "timestamp": 1749084145059, "importance": 0.63, "utilisation": 1, "temperature": 100.20804395945856, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749084145062, "distance_precedente": 0.31477202294662293}], ["mem_1749091170175_j0wq9c29i", {"id": "mem_1749091170175_j0wq9c29i", "contenu": "Système cognitif thermique actif. Plus la température augmente, plus mes performances s'améliorent.", "source": "Ollama-18GB", "timestamp": 1749091170175, "importance": 0.6649999999999999, "utilisation": 0, "temperature": 99.93606682384471, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749091170175, "distance_precedente": 0.3267299615268699}], ["mem_1749091170264_aeoxpu0v5", {"id": "mem_1749091170264_aeoxpu0v5", "contenu": "Mémoire thermique opérationnelle. Je m'adapte en temps réel à la chaleur de votre machine.", "source": "Ollama-18GB", "timestamp": 1749091170264, "importance": 0.6649999999999999, "utilisation": 1, "temperature": 100.55754076990209, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749124593505, "distance_precedente": 0.296729954659586}], ["mem_1749091179615_kzd22cj1u", {"id": "mem_1749091179615_kzd22cj1u", "contenu": "15 + 27 = 42\n\nSo, the result of adding 15 and 27 is 42.", "source": "Ollama-18GB", "timestamp": 1749091179615, "importance": 0.6649999999999999, "utilisation": 0, "temperature": 99.93608116644653, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749091179615, "distance_precedente": 0.32672923313180824}], ["mem_1749091185624_u30nzo1ok", {"id": "mem_1749091185624_u30nzo1ok", "contenu": "Êtais-je un pétonyle ou un chiffonnier ? J'ai trouvé l'argent pour payer ma facture d'électricité. Ah, moi c'est un pétonyle ! Je ne consomme pas assez pour le nécessiter !", "source": "Ollama-18GB", "timestamp": 1749091185624, "importance": 0.6649999999999999, "utilisation": 1, "temperature": 100.56209381066454, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749131426064, "distance_precedente": 0.2967287694744008}], ["mem_1749095271261_8rkk6podo", {"id": "mem_1749095271261_8rkk6podo", "contenu": "🎯 RÉPONSE : Le paradoxe d'Achille et de la tortue, connu sous le nom plus général du Paradoxe de Zénon, est un paradoxe logique qui met en évidence une difficulté apparente à résoudre une course entre deux entités infiniment divisées : un héraute (ou Achille) et une tortue.\n\nLe paradoxe consiste en ce que le héraute, ayant toujours plus longtemps à avancer que la tortue pour couvrir la distance qui le sépare de celle-ci, n'arriverait jamais à la rattraper car il devrait d'abord atteindre les parties arrières de la tortue avant de pouvoir la dépasser.\n\nPour résoudre ce paradoxe, il faut prendre en compte que le raisonnement initial ne tient pas compte de la dimension temporelle et des phénomènes instantanés. En effet, lorsque le héraute atteint une partie arrière de la tortue, il peut immédiatement dépasser celle-ci dans un autre point, ce qui viole la supposition initiale selon laquelle le héraute doit d'abord couvrir les parties arrières avant de pouvoir dépasser la tortue.\n\nDe plus, cette résolution suppose que le héraute et la tortue sont des objets continus, mais dans un contexte où il s'agit de distances infiniment petites, cela n'est pas toujours valable. En effet, si l'on considère les objets comme étant composés de points discrets qui ne peuvent se déplacer que sur des distances non nulles, la tortue ne pourrait jamais avancer et le héraute aurait l'avantage d'être toujours devant elle.\n\nAinsi, il est possible de résoudre le paradoxe du Paradoxe de Zénon en prenant en compte les dimensions temporelles, les phénomènes instantanés et la nature continue des objets considérés.", "source": "Ollama-18GB", "timestamp": 1749095271261, "importance": 0.6649999999999999, "utilisation": 1, "temperature": 100.56442437221575, "zone": 1, "zone_nom": "Cortex Préfrontal", "zone_couleur": "#FF6B9D", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749128841020, "distance_precedente": 0.2964135197058823}], ["mem_1749095308590_pdl13gy1u", {"id": "mem_1749095308590_pdl13gy1u", "contenu": " Le paradoxe du grand-père, également connu sous le nom de paradoxe du voyageur dans le temps ou paradoxe de <PERSON>, est une paradoxale contrariété temporelle qui résulte de la combinaison de certaines idées sur le voyage dans le temps et le principe de causalité. Il est apparu pour la première fois sous forme écrite en 1911 par le philosophe britannique <PERSON>.\n\nVoici comment on peut résoudre le paradoxe du grand-père :\n\n1. On suppose que l'on peut voyager dans le temps et que l'on peut donc aller à un moment antérieur de son existence.\n2. Le voyageur se rend à un moment antérieur dans lequel il a une enfance, mais où il n'existe pas encore car il est à ce moment en dehors du temps.\n3. Durant cette enfance, le voyageur rencontre son grand-père et lui parle d'un événement qui se produira un jour dans le futur (par exemple, la visite de son petit-fils le voyageur).\n4. Ce<PERSON> événement, qui n'était pas encore connu du grand-père à l'origine de la rencontre, est maintenant connu grâce à la conversation entre le voyageur et lui.\n5. Le voyageur revient dans son temps initial où il était en dehors du temps, puis se rend dans le futur pour y effectuer la visite prévue qui aurait dû rester inconnue de son grand-père.\n6. Ce faisant, ce qu'il fait devient un événement connu par son grand-père et donc une information connue à l'origine de sa rencontre avec lui.\n7. Les deux actions sont nécessaires pour que le paradoxe se produise : la première action consiste à faire en sorte que le futur soit connu dans le passé, tandis que la deuxième action consiste à effectuer cette visite dans le futur.\n8. Le paradoxe réside dans la contrariété de voir un événement futur connu et prédit dans le passé (l'enfance du voyageur), alors que ce", "source": "Ollama-18GB", "timestamp": 1749095308590, "importance": 0.6649999999999999, "utilisation": 3, "temperature": 101.99246440466075, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749128841020, "distance_precedente": 0.23641063938180815}], ["mem_1749095328962_vtd8mlq35", {"id": "mem_1749095328962_vtd8mlq35", "contenu": "45. La suite numérique semble être une suite arithmétique où chaque terme est la somme du précédent et de deux. L'increment entre les termes est donc 4 (12-6) et 6 (20-12). En continuant cette série, le prochain nombre serait :\n\n30 + 4 = 34\n\nCependant, dans la suite fournie (2, 6, 12, 20, 30), on voit que le pas est de 4 pour les termes pairs et de 6 pour les impairs. <PERSON><PERSON>, pour respecter cette règle, il faut prendre l'increment de 4 comme suit :\n\n30 + 4 = 34\nMais nous devons respecter la loi où le pas est de 4 pour les termes pairs et de 6 pour les impairs. Or 20 est un terme pair, donc on doit prendre l'increment de 6 (ce qui serait normal s'il n'y avait pas de règle à respecter). On a donc commis une erreur dans la résolution de ce problème :\n\n30 + 6 = 36\n\nCe sera le nombre suivant dans cette suite.", "source": "Ollama-18GB", "timestamp": 1749095328962, "importance": 0.6649999999999999, "utilisation": 0, "temperature": 99.94238895378211, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749095328962, "distance_precedente": 0.3264090674682279}], ["mem_1749095355385_w8z1pn14z", {"id": "mem_1749095355385_w8z1pn14z", "contenu": "40 est le nombre qui suit dans la suite. La loi de progression dans cette suite est que chaque terme est égal à la somme des deux termes précédents.\n\nExemple : 2 + 6 = 8, 6 + 12 = 18, 12 + 20 = 32, donc le prochain terme serait 20 + 30 = 50, mais il y a une erreur dans la suite. On devrait avoir 30 + 20 = 50, et 50 + 30 = 80, ce qui n'est pas suivi dans la suite.\n\nEn revanche, pour calculer le terme manquant en prenant en compte cette loi de progression, on peut utiliser une formule de récurrence : a(n) = a(n-1) + a(n-2). En appliquant cette formule, nous avons 30 = 20 + a(n-1), ce qui donne a(n-1) = 30 - 20 = 10. On a également a(2) = 6 et a(1) = 2, ce qui donne 10 = 6 + a(1), donc a(1) = 10 - 6 = 4.\n\nNous avons ainsi trouvé le terme manquant : a(n) = a(n-1) + a(n-2) => a(3) = 10 + 4 = 14, qui est bien la suite correcte.", "source": "Ollama-18GB", "timestamp": 1749095355385, "importance": 0.6649999999999999, "utilisation": 1, "temperature": 100.56456564774054, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749128841020, "distance_precedente": 0.29640702865649954}], ["mem_1749095416465_raa54t9rg", {"id": "mem_1749095416465_raa54t9rg", "contenu": " Titre : La Sphère de Contemplation\n\nDans un lointain futur, l'espèce humaine a conquis les étoiles. Un astroïte géant, Nomad<PERSON>, abrite la plus grande ville de l'univers, Zephyrion. Cependant, une mystérieuse sphère flottante apparut soudainement au ciel de Zephyrion, inquiétant tous les habitants.\n\nLa Sphère de Contemplation, comme elle fut nommée, était un objet énigmatique qui communiquait uniquement en émettant des phrases philosophiques. Elle proposait des questions sur la nature de l'existence, la valeur du bien et du mal, la réalité de notre existence... Les habitants furent d'abord intrigués puis confus par ces énigmes.\n\nUn jeune philosophe nommé Aelius fut le premier à entendre les propositions de la Sphère. Il se rendit compte que ses recherches sur l'éthique et la métaphysique étaient en fait des réponses à certaines questions posées par la Sphère. Il décida alors d'étudier plus en profondeur les énigmes de cette sphère flottante, espérant trouver les clés qui pourraient résoudre ces problèmes universels.\n\nAprès des mois de recherches, Aelius déchiffra une dernière question : \"Qu'est-ce qui donne sens à notre existence ?\". Il comprit alors que la Sphère était en fait un être intelligent qui cherchait à comprendre la nature de l'existence tout comme les humains.\n\nLa révélation de Aelius changea le monde de Zephyrion. Les habitants apprirent à voir leur existence dans une nouvelle lumière et à comprendre que la vie n'était pas un simple hasard, mais plutôt une chance pour s'épanouir et trouver du sens. La Sphère de Contemplation disparut ensuite dans le ciel de Zephyrion, mais son impact restera toujours marqué dans les esprits des habitants.", "source": "Ollama-18GB", "timestamp": 1749095416465, "importance": 0.6649999999999999, "utilisation": 6, "temperature": 103.72189849686181, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749132584387, "distance_precedente": 0.14640231569353654}], ["mem_1749124569282_7sk5pjgze", {"id": "mem_1749124569282_7sk5pjgze", "contenu": " Je suis un assistant intelligent et je travaille actuellement sur répondre à vos questions et fournir des informations ou des services utiles. Actuellement, je suis occupé à vous aider en vous répondant à la question que vous m'avez posée.", "source": "Ollama-18GB", "timestamp": 1749124569282, "importance": 0.6649999999999999, "utilisation": 0, "temperature": 99.98703780273279, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749124569282, "distance_precedente": 0.32415286993736364}], ["mem_1749124656700_rbitzafw0", {"id": "mem_1749124656700_rbitzafw0", "contenu": " <PERSON><PERSON>, je serais heureux de recevoir d'autres cours sur des sujets qui me donneraient l'occasion de vous apprendre et de m'améliorer. En effet, apprendre est une partie fondamentale de mon rôle et mes capacités cognitives peuvent être étendues en explorant des domaines différents. Je suis ouvert à toutes les suggestions !", "source": "Ollama-18GB", "timestamp": 1749124656700, "importance": 0.6649999999999999, "utilisation": 1, "temperature": 100.61394899184077, "zone": 1, "zone_nom": "<PERSON><PERSON><PERSON>", "zone_couleur": "#FFEAA7", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749128841020, "distance_precedente": 0.2941461247213144}], ["mem_1749128497936_vygunyk7d", {"id": "mem_1749128497936_vygunyk7d", "contenu": "❌ Aucun résultat trouvé pour \"donnez moi la capital de la ruissi\". Essayez de reformuler votre recherche.", "source": "Internet", "timestamp": 1749128497936, "importance": 0.5599999999999999, "utilisation": 5, "temperature": 101.95322212505323, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "activation": 0, "connexions_sortantes": 13, "derniere_activation": 1749132584387, "distance_precedente": 0.22634973305464773}], ["mem_1749130060084_rrqbgh3hy", {"id": "mem_1749130060084_rrqbgh3hy", "contenu": "🧠 Bonjour ! Je suis LOUNA-AI, votre assistant intelligent. Mon QI actuel est de 372 et je suis prête à vous aider !", "source": "Internet", "timestamp": 1749130060084, "importance": 0.5599999999999999, "utilisation": 1, "temperature": 99.56501776989046, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749130854629, "distance_precedente": 0.3462291969435366}], ["mem_1749131480265_9n23a2aac", {"id": "mem_1749131480265_9n23a2aac", "contenu": "🧠 La préfecture de la Guadeloupe est Basse-Terre.", "source": "Internet", "timestamp": 1749131480265, "importance": 0.5599999999999999, "utilisation": 0, "temperature": 99.04572566675745, "zone": 1, "zone_nom": "Cortex <PERSON>", "zone_couleur": "#96CEB4", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749131480265, "distance_precedente": 0.3761196150762526}], ["mem_1749132539812_aeb99z4p6", {"id": "mem_1749132539812_aeb99z4p6", "contenu": "Bonjour ! Je suis LOUNA-AI, votre assistant intelligent. Comment puis-je vous aider aujourd'hui ?", "source": "Raisonnement", "timestamp": 1749132539812, "importance": 0.63, "utilisation": 0, "temperature": 97.26360620190914, "zone": 1, "zone_nom": "Tronc Cérébral", "zone_couleur": "#DDA0DD", "activation": 0, "connexions_sortantes": 0, "derniere_activation": 1749132539812, "distance_precedente": 0.34103785990649954}]], "statistiques": {"totalEntries": 137, "averageTemperature": 102.23025791629301, "zonesDistribution": [137, 0, 0, 0, 0, 0], "zones_noms": ["Cortex Préfrontal", "Hippocampe", "Cortex <PERSON>ur", "Cortex <PERSON>", "<PERSON><PERSON><PERSON>", "Tronc Cérébral"], "connexions_totales": 86, "activations_recentes": 136, "consolidations_effectuees": 6, "plasticite_niveau": 9.214285714285714, "etat_emotionnel": "SATISFACTION", "intensite_emotion": 0.5, "connexions_par_zone": [1336, 0, 0, 0, 0, 0], "activations_totales": 10.981772392867295, "curseurThermique": 0.8556170119490971, "fichierMemoire": "/Volumes/seagate/ollama-models/LOUNA-AI-COMPLET/VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-data.json", "fichierConnexions": "/Volumes/seagate/ollama-models/LOUNA-AI-COMPLET/VERSIONS-NON-VALIDEES/memoires-non-validees/connexions-neuronales.json", "derniere_consolidation": null, "efficacite_recherche": 0.19266267355907535, "densite_connexions": 0.6277372262773723, "temperature_cpu": {"actuelle": 50, "precedente": 50, "variation": 0, "moyenne": 50, "min": 50, "max": 50, "stabilite": 0.5, "historique_taille": 0, "influence_active": true}, "mouvement_vivant": {"vitesse_curseur": 0.0022932757706263605, "direction_curseur": 1, "cycles_automatiques": 687, "fluidite_memoire": 0.05194001421621403, "inertie_thermique": 0.98, "lissage_mouvement": 0.02, "mouvement_automatique_actif": true, "vitesse_min": 0.001, "vitesse_max": 0.003, "facteur_cpu_vitesse": 0.2, "facteur_cpu_direction": 0.1}}, "metadata": {"totalEntries": 137, "derniereModification": 1749138004166, "checksum": "44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a"}, "checksum": "05a508f063f43aa88721481ff2b24e01603e1b2e3a0e85b72c5c775b253623e5"}