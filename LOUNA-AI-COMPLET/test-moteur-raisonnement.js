/**
 * TEST DU MOTEUR DE RAISONNEMENT - VÉRIFICATION SALUTATIONS
 */

const MoteurRaisonnementReel = require('./moteur-raisonnement-reel.js');

console.log('🧪 TEST MOTEUR DE RAISONNEMENT - SALUTATIONS');
console.log('=============================================');

// Créer une instance du moteur
const moteur = new MoteurRaisonnementReel();

// Tests de salutations
const testsSalutations = [
    'bonjour',
    'Bonjour !',
    'Bonjour LOUNA',
    'salut',
    'Salut !',
    'hello',
    'Hello world',
    'bonsoir',
    'comment ça va ?',
    'comment allez-vous ?',
    'qui es-tu ?',
    'test normal' // Ne devrait pas être une salutation
];

testsSalutations.forEach((test, index) => {
    console.log(`\n🔍 Test ${index + 1}: "${test}"`);
    
    try {
        const resultat = moteur.penser(test);
        
        if (resultat && resultat.reponse) {
            console.log('✅ RÉPONSE OBTENUE !');
            console.log('📝 Source:', resultat.source);
            console.log('🎯 Réponse:', resultat.reponse.substring(0, 100) + '...');
            
            // Vérifier si c'est bien une salutation
            if (resultat.source === 'Raisonnement interne' && 
                (resultat.reponse.includes('Bonjour') || 
                 resultat.reponse.includes('Je vais très bien') ||
                 resultat.reponse.includes('LOUNA-AI'))) {
                console.log('🎉 SALUTATION DÉTECTÉE ET TRAITÉE !');
            }
        } else {
            console.log('❌ Aucune réponse');
        }
        
    } catch (error) {
        console.log('❌ ERREUR:', error.message);
    }
});

console.log('\n🎉 TESTS TERMINÉS !');
console.log('===================');

// Test spécifique pour "bonjour"
console.log('\n🎯 TEST SPÉCIFIQUE "bonjour":');
try {
    const resultatBonjour = moteur.penser('bonjour');
    if (resultatBonjour && resultatBonjour.reponse) {
        console.log('✅ SUCCÈS ! Réponse à "bonjour":');
        console.log(resultatBonjour.reponse);
    } else {
        console.log('❌ ÉCHEC ! Pas de réponse à "bonjour"');
    }
} catch (error) {
    console.log('❌ ERREUR pour "bonjour":', error.message);
}
