<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 REEL LOUNA AI V5 - Mémoire Thermique</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-card h3 {
            margin-bottom: 15px;
            color: #feca57;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-left: auto;
            text-align: right;
        }

        .message.bot {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .input-container input {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            backdrop-filter: blur(10px);
        }

        .input-container input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .input-container button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .input-container button:hover {
            transform: scale(1.05);
        }

        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: #333;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .test-btn:hover {
            transform: scale(1.05);
        }

        .loading {
            display: none;
            text-align: center;
            color: #feca57;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 REEL LOUNA AI V5</h1>
            <p>Mémoire Thermique Vivante • Problème Italie/Guadeloupe Résolu</p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🧠 État Mémoire Thermique</h3>
                <div id="memoire-stats">
                    <div class="loading pulse">Chargement...</div>
                </div>
            </div>

            <div class="status-card">
                <h3>⚡ Système Neural</h3>
                <div id="neural-stats">
                    <div class="loading pulse">Chargement...</div>
                </div>
            </div>

            <div class="status-card">
                <h3>🔄 Activité Système</h3>
                <div id="system-stats">
                    <div class="loading pulse">Chargement...</div>
                </div>
            </div>
        </div>

        <div class="chat-container">
            <h3>💬 Test Conversationnel</h3>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="testerProbleme()">🎯 Test Problème Résolu</button>
                <button class="test-btn" onclick="testerCapitales()">🌍 Test Capitales</button>
                <button class="test-btn" onclick="testerCalcul()">🧮 Test Calcul</button>
                <button class="test-btn" onclick="testerSalutation()">👋 Test Salutation</button>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="message bot">
                    <strong>LOUNA-AI :</strong> Salut ! Je suis prête avec ma mémoire thermique active ! 🔥<br>
                    <em>Teste le problème résolu : "quelle est la capitale de l'Italie ?" puis "et la Guadeloupe ?"</em>
                </div>
            </div>

            <div class="input-container">
                <input type="text" id="message-input" placeholder="Tapez votre message..." onkeypress="handleKeyPress(event)">
                <button onclick="envoyerMessage()">Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        // Charger les statistiques
        function chargerStatistiques() {
            fetch('/api/memoire-thermique')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.memoireThermique.active) {
                        const stats = data.memoireThermique.stats;
                        const neurones = data.memoireThermique.neurones;
                        const kyber = data.memoireThermique.kyber;

                        document.getElementById('memoire-stats').innerHTML = `
                            <div class="stat-item">
                                <span>📊 Mémoires totales:</span>
                                <span>${stats.totalEntries}</span>
                            </div>
                            <div class="stat-item">
                                <span>🌡️ Température moyenne:</span>
                                <span>${stats.averageTemperature.toFixed(2)}°C</span>
                            </div>
                            <div class="stat-item">
                                <span>🔄 Mises à jour:</span>
                                <span>${stats.updates_performed}</span>
                            </div>
                        `;

                        document.getElementById('neural-stats').innerHTML = `
                            <div class="stat-item">
                                <span>🧠 Neurones totaux:</span>
                                <span>${neurones.total_installed.toLocaleString()}</span>
                            </div>
                            <div class="stat-item">
                                <span>⚡ Neurones actifs:</span>
                                <span>${neurones.active_count.toLocaleString()}</span>
                            </div>
                            <div class="stat-item">
                                <span>🚀 Accélérateurs KYBER:</span>
                                <span>${kyber.active.length}</span>
                            </div>
                        `;

                        document.getElementById('system-stats').innerHTML = `
                            <div class="stat-item">
                                <span>🔍 Vérifications:</span>
                                <span>${stats.verifications_done}</span>
                            </div>
                            <div class="stat-item">
                                <span>📈 Informations actualisées:</span>
                                <span>${stats.information_refreshed}</span>
                            </div>
                            <div class="stat-item">
                                <span>✅ Statut:</span>
                                <span style="color: #2ecc71;">Opérationnel</span>
                            </div>
                        `;
                    } else {
                        document.getElementById('memoire-stats').innerHTML = '<div style="color: #e74c3c;">❌ Mémoire thermique non disponible</div>';
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    document.getElementById('memoire-stats').innerHTML = '<div style="color: #e74c3c;">❌ Erreur de connexion</div>';
                });
        }

        // Tests prédéfinis
        function testerProbleme() {
            envoyerMessage("quelle est la capitale de l'Italie ?");
            setTimeout(() => {
                envoyerMessage("et la Guadeloupe ?");
            }, 2000);
        }

        function testerCapitales() {
            envoyerMessage("quelle est la capitale de la France ?");
        }

        function testerCalcul() {
            envoyerMessage("combien font 15 × 7 ?");
        }

        function testerSalutation() {
            envoyerMessage("bonjour");
        }

        // Envoyer message
        function envoyerMessage(messageText = null) {
            const input = document.getElementById('message-input');
            const message = messageText || input.value.trim();
            
            if (!message) return;

            // Afficher message utilisateur
            ajouterMessage(message, 'user');
            
            // Vider l'input si pas de message prédéfini
            if (!messageText) input.value = '';

            // Envoyer à l'API
            fetch('/api/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    ajouterMessage(data.reponse, 'bot');
                } else {
                    ajouterMessage('Erreur: ' + data.error, 'bot');
                }
            })
            .catch(error => {
                ajouterMessage('Erreur de connexion: ' + error.message, 'bot');
            });
        }

        function ajouterMessage(message, type) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            if (type === 'user') {
                messageDiv.innerHTML = `<strong>Vous :</strong> ${message}`;
            } else {
                messageDiv.innerHTML = `<strong>LOUNA-AI :</strong> ${message}`;
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                envoyerMessage();
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            chargerStatistiques();
            setInterval(chargerStatistiques, 5000); // Mise à jour toutes les 5 secondes
        });
    </script>
</body>
</html>
