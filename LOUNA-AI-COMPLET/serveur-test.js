const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    let filePath = path.join(__dirname, 'interface-louna-complete.html');
    
    fs.readFile(filePath, (err, content) => {
        if (err) {
            res.writeHead(404);
            res.end('Fichier non trouvé');
        } else {
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(content);
        }
    });
});

server.listen(8080, () => {
    console.log('🚀 Serveur démarré sur http://localhost:8080');
    console.log('✅ Interface LOUNA-AI corrigée disponible !');
});
