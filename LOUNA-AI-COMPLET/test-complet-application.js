/**
 * TEST COMPLET DE L'APPLICATION LOUNA-AI
 * Vérification de tous les systèmes et fonctionnalités
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

class TestCompletApplication {
    constructor() {
        this.baseURL = 'http://localhost:3000';
        this.resultats = {
            tests_reussis: 0,
            tests_echoues: 0,
            erreurs: [],
            details: []
        };
    }

    async executerTousLesTests() {
        console.log('🧪 DÉBUT DES TESTS COMPLETS LOUNA-AI');
        console.log('=====================================');

        try {
            // 1. Test de démarrage du serveur
            await this.testerDemarrageServeur();

            // 2. Test des routes principales
            await this.testerRoutesInterface();

            // 3. Test des APIs
            await this.testerAPIs();

            // 4. Test des fichiers
            await this.testerFichiers();

            // 5. Test des systèmes intégrés
            await this.testerSystemesIntegres();

            // 6. Test de l'interface cerveau
            await this.testerInterfaceCerveau();

            // 7. Test du système QI
            await this.testerSystemeQI();

            // 8. Test de sécurisation
            await this.testerSecurisation();

            this.afficherResultats();

        } catch (error) {
            console.error('❌ Erreur critique lors des tests:', error.message);
            this.resultats.erreurs.push(`Erreur critique: ${error.message}`);
        }
    }

    async testerDemarrageServeur() {
        console.log('\n🚀 Test 1: Démarrage du serveur...');
        
        try {
            const response = await this.faireRequete('/api/stats');
            if (response) {
                this.ajouterSucces('Serveur démarré et accessible');
            } else {
                this.ajouterEchec('Serveur non accessible');
            }
        } catch (error) {
            this.ajouterEchec(`Erreur démarrage serveur: ${error.message}`);
        }
    }

    async testerRoutesInterface() {
        console.log('\n🌐 Test 2: Routes d\'interface...');
        
        const routes = [
            '/',
            '/cerveau',
            '/pensees',
            '/test-qi',
            '/3d',
            '/formations'
        ];

        for (const route of routes) {
            try {
                const response = await this.faireRequete(route);
                if (response) {
                    this.ajouterSucces(`Route ${route} accessible`);
                } else {
                    this.ajouterEchec(`Route ${route} non accessible`);
                }
            } catch (error) {
                this.ajouterEchec(`Erreur route ${route}: ${error.message}`);
            }
        }
    }

    async testerAPIs() {
        console.log('\n🔌 Test 3: APIs...');
        
        const apis = [
            '/api/stats',
            '/api/chat',
            '/api/oubli-intelligent/status',
            '/api/bureau/stats',
            '/api/recherche-securisee/status',
            '/api/expertise/stats',
            '/api/cerveau/pensees',
            '/api/cerveau/emotions-detaillees',
            '/api/cerveau/idees-creatives',
            '/api/cerveau/memoires-thermiques'
        ];

        for (const api of apis) {
            try {
                const response = await this.faireRequete(api);
                if (response) {
                    this.ajouterSucces(`API ${api} fonctionnelle`);
                } else {
                    this.ajouterEchec(`API ${api} non fonctionnelle`);
                }
            } catch (error) {
                this.ajouterEchec(`Erreur API ${api}: ${error.message}`);
            }
        }
    }

    async testerFichiers() {
        console.log('\n📁 Test 4: Fichiers essentiels...');
        
        const fichiers = [
            'serveur-interface-complete.js',
            'interface-louna-complete.html',
            'interface-cerveau-pensees-emotions.html',
            'interface-test-qi-avance.html',
            'interface-3d-cerveau-vivant.html',
            'systeme-oubli-intelligent.js',
            'gestionnaire-bureau-complet.js',
            'systeme-scan-intelligent.js',
            'recherche-internet-securisee.js',
            'systeme-expertise-automatique.js',
            'securisation-complete.js'
        ];

        for (const fichier of fichiers) {
            const chemin = path.join(__dirname, fichier);
            if (fs.existsSync(chemin)) {
                this.ajouterSucces(`Fichier ${fichier} présent`);
                
                // Vérifier la taille du fichier
                const stats = fs.statSync(chemin);
                if (stats.size > 1000) { // Au moins 1KB
                    this.ajouterSucces(`Fichier ${fichier} a du contenu (${stats.size} bytes)`);
                } else {
                    this.ajouterEchec(`Fichier ${fichier} trop petit (${stats.size} bytes)`);
                }
            } else {
                this.ajouterEchec(`Fichier ${fichier} manquant`);
            }
        }
    }

    async testerSystemesIntegres() {
        console.log('\n🔧 Test 5: Systèmes intégrés...');
        
        // Test système d'oubli intelligent
        try {
            const response = await this.faireRequete('/api/oubli-intelligent/status');
            if (response && !response.erreur) {
                this.ajouterSucces('Système d\'oubli intelligent opérationnel');
            } else {
                this.ajouterEchec('Système d\'oubli intelligent non opérationnel');
            }
        } catch (error) {
            this.ajouterEchec(`Erreur système oubli: ${error.message}`);
        }

        // Test gestionnaire bureau
        try {
            const response = await this.faireRequete('/api/bureau/stats');
            if (response && !response.erreur) {
                this.ajouterSucces('Gestionnaire bureau opérationnel');
            } else {
                this.ajouterEchec('Gestionnaire bureau non opérationnel');
            }
        } catch (error) {
            this.ajouterEchec(`Erreur gestionnaire bureau: ${error.message}`);
        }

        // Test recherche sécurisée
        try {
            const response = await this.faireRequete('/api/recherche-securisee/status');
            if (response && !response.erreur) {
                this.ajouterSucces('Recherche sécurisée opérationnelle');
            } else {
                this.ajouterEchec('Recherche sécurisée non opérationnelle');
            }
        } catch (error) {
            this.ajouterEchec(`Erreur recherche sécurisée: ${error.message}`);
        }

        // Test expertise automatique
        try {
            const response = await this.faireRequete('/api/expertise/stats');
            if (response && !response.erreur) {
                this.ajouterSucces('Expertise automatique opérationnelle');
            } else {
                this.ajouterEchec('Expertise automatique non opérationnelle');
            }
        } catch (error) {
            this.ajouterEchec(`Erreur expertise automatique: ${error.message}`);
        }
    }

    async testerInterfaceCerveau() {
        console.log('\n🧠 Test 6: Interface cerveau...');
        
        const apisCerveau = [
            '/api/cerveau/pensees',
            '/api/cerveau/emotions-detaillees',
            '/api/cerveau/idees-creatives',
            '/api/cerveau/memoires-thermiques'
        ];

        for (const api of apisCerveau) {
            try {
                const response = await this.faireRequete(api);
                if (response && response.success !== false) {
                    this.ajouterSucces(`API cerveau ${api} fonctionnelle`);
                } else {
                    this.ajouterEchec(`API cerveau ${api} non fonctionnelle`);
                }
            } catch (error) {
                this.ajouterEchec(`Erreur API cerveau ${api}: ${error.message}`);
            }
        }
    }

    async testerSystemeQI() {
        console.log('\n🧠 Test 7: Système QI...');
        
        try {
            // Test de l'interface QI
            const response = await this.faireRequete('/test-qi');
            if (response) {
                this.ajouterSucces('Interface test QI accessible');
            } else {
                this.ajouterEchec('Interface test QI non accessible');
            }

            // Test API chat pour QI
            const chatResponse = await this.faireRequetePost('/api/chat', {
                message: 'Quel est ton QI actuel ?'
            });
            
            if (chatResponse && chatResponse.success !== false) {
                this.ajouterSucces('Système QI répond aux requêtes');
            } else {
                this.ajouterEchec('Système QI ne répond pas');
            }
        } catch (error) {
            this.ajouterEchec(`Erreur système QI: ${error.message}`);
        }
    }

    async testerSecurisation() {
        console.log('\n🔒 Test 8: Sécurisation...');
        
        try {
            // Vérifier si le système de sécurisation existe
            const cheminSecurisation = path.join(__dirname, 'securisation-complete.js');
            if (fs.existsSync(cheminSecurisation)) {
                this.ajouterSucces('Système de sécurisation présent');
                
                // Vérifier les dossiers de sauvegarde
                const dossierSauvegarde = path.join(__dirname, 'SAUVEGARDE_SECURISEE');
                if (fs.existsSync(dossierSauvegarde)) {
                    this.ajouterSucces('Dossier de sauvegarde créé');
                } else {
                    this.ajouterEchec('Dossier de sauvegarde manquant');
                }
            } else {
                this.ajouterEchec('Système de sécurisation manquant');
            }
        } catch (error) {
            this.ajouterEchec(`Erreur sécurisation: ${error.message}`);
        }
    }

    async faireRequete(endpoint) {
        return new Promise((resolve, reject) => {
            const url = this.baseURL + endpoint;
            
            http.get(url, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        if (res.headers['content-type'] && res.headers['content-type'].includes('application/json')) {
                            resolve(JSON.parse(data));
                        } else {
                            resolve(data); // HTML ou autre
                        }
                    } catch (error) {
                        resolve(data); // Retourner les données brutes si pas JSON
                    }
                });
            }).on('error', (error) => {
                reject(error);
            });
        });
    }

    async faireRequetePost(endpoint, donnees) {
        return new Promise((resolve, reject) => {
            const url = new URL(this.baseURL + endpoint);
            const postData = JSON.stringify(donnees);
            
            const options = {
                hostname: url.hostname,
                port: url.port,
                path: url.pathname,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };
            
            const req = http.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        resolve(JSON.parse(data));
                    } catch (error) {
                        resolve(data);
                    }
                });
            });
            
            req.on('error', (error) => {
                reject(error);
            });
            
            req.write(postData);
            req.end();
        });
    }

    ajouterSucces(message) {
        console.log(`✅ ${message}`);
        this.resultats.tests_reussis++;
        this.resultats.details.push({ type: 'succès', message });
    }

    ajouterEchec(message) {
        console.log(`❌ ${message}`);
        this.resultats.tests_echoues++;
        this.resultats.erreurs.push(message);
        this.resultats.details.push({ type: 'échec', message });
    }

    afficherResultats() {
        console.log('\n📊 RÉSULTATS DES TESTS');
        console.log('======================');
        console.log(`✅ Tests réussis: ${this.resultats.tests_reussis}`);
        console.log(`❌ Tests échoués: ${this.resultats.tests_echoues}`);
        console.log(`📈 Taux de réussite: ${((this.resultats.tests_reussis / (this.resultats.tests_reussis + this.resultats.tests_echoues)) * 100).toFixed(1)}%`);
        
        if (this.resultats.erreurs.length > 0) {
            console.log('\n🔍 ERREURS DÉTECTÉES:');
            this.resultats.erreurs.forEach((erreur, index) => {
                console.log(`${index + 1}. ${erreur}`);
            });
        }
        
        console.log('\n🎯 RECOMMANDATIONS:');
        if (this.resultats.tests_echoues === 0) {
            console.log('🎉 Parfait ! Tous les tests sont réussis. L\'application est prête !');
        } else if (this.resultats.tests_echoues < 5) {
            console.log('⚠️ Quelques problèmes mineurs détectés. Corrigez les erreurs listées.');
        } else {
            console.log('🚨 Problèmes importants détectés. Vérifiez l\'installation et la configuration.');
        }
        
        // Sauvegarder les résultats
        const rapport = {
            timestamp: new Date().toISOString(),
            resultats: this.resultats,
            recommandations: this.genererRecommandations()
        };
        
        fs.writeFileSync(
            path.join(__dirname, 'rapport-test-complet.json'),
            JSON.stringify(rapport, null, 2)
        );
        
        console.log('\n📄 Rapport détaillé sauvegardé dans: rapport-test-complet.json');
    }

    genererRecommandations() {
        const recommandations = [];
        
        if (this.resultats.erreurs.some(e => e.includes('Serveur'))) {
            recommandations.push('Vérifiez que le serveur LOUNA-AI est démarré sur le port 3000');
        }
        
        if (this.resultats.erreurs.some(e => e.includes('Fichier'))) {
            recommandations.push('Vérifiez que tous les fichiers sont présents et complets');
        }
        
        if (this.resultats.erreurs.some(e => e.includes('API'))) {
            recommandations.push('Vérifiez la configuration des APIs et des systèmes intégrés');
        }
        
        if (this.resultats.tests_reussis > this.resultats.tests_echoues) {
            recommandations.push('L\'application fonctionne globalement bien, corrigez les erreurs mineures');
        }
        
        return recommandations;
    }
}

// Exécution des tests si le fichier est lancé directement
if (require.main === module) {
    const testeur = new TestCompletApplication();
    testeur.executerTousLesTests().then(() => {
        console.log('\n🏁 Tests terminés !');
        process.exit(0);
    }).catch((error) => {
        console.error('💥 Erreur fatale:', error);
        process.exit(1);
    });
}

module.exports = TestCompletApplication;
