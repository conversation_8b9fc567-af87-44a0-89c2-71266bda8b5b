#!/usr/bin/env node

/**
 * 🤖 OLLAMA INTÉGRÉ DIRECTEMENT DANS L'APPLICATION
 * 
 * OBJECTIF: Intégrer Ollama directement sans dépendance externe
 * INNOVATION: Premier système IA avec Ollama embarqué
 * 
 * "AUTONOMIE COMPLÈTE" - Aucune dépendance externe
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec, execSync } = require('child_process');
const axios = require('axios').default;

class OllamaIntegre {
    constructor() {
        this.config = {
            port: 11434,
            host: 'localhost',
            models_path: path.join(__dirname, 'models'),
            ollama_path: null,
            auto_install: true,
            embedded_mode: true
        };
        
        this.processus_ollama = null;
        this.modeles_disponibles = [];
        this.statut = 'arrete';
        
        // Modèles intégrés (versions légères)
        this.modeles_integres = {
            'mistral-7b': {
                nom: 'mistral:7b',
                taille: '4.1GB',
                description: 'Modèle général rapide',
                url_download: 'https://ollama.ai/library/mistral:7b'
            },
            'codellama-7b': {
                nom: 'codellama:7b',
                taille: '3.8GB', 
                description: 'Modèle code spécialisé',
                url_download: 'https://ollama.ai/library/codellama:7b'
            },
            'phi3-mini': {
                nom: 'phi3:mini',
                taille: '2.3GB',
                description: 'Modèle ultra-léger',
                url_download: 'https://ollama.ai/library/phi3:mini'
            }
        };
        
        console.log('🤖 Ollama Intégré initialisé');
        this.init();
    }

    async init() {
        try {
            // Créer dossiers nécessaires
            this.creerDossiers();
            
            // Détecter ou installer Ollama
            await this.detecterOuInstallerOllama();
            
            // Démarrer Ollama intégré
            await this.demarrerOllamaIntegre();
            
            // Vérifier modèles disponibles
            await this.verifierModeles();
            
            console.log('✅ Ollama intégré opérationnel');
            
        } catch (error) {
            console.error('❌ Erreur initialisation Ollama intégré:', error.message);
            this.activerModeSimulation();
        }
    }

    creerDossiers() {
        // Créer dossier modèles
        if (!fs.existsSync(this.config.models_path)) {
            fs.mkdirSync(this.config.models_path, { recursive: true });
            console.log(`📁 Dossier modèles créé: ${this.config.models_path}`);
        }
        
        // Créer dossier logs
        const logs_path = path.join(__dirname, 'logs');
        if (!fs.existsSync(logs_path)) {
            fs.mkdirSync(logs_path, { recursive: true });
        }
    }

    async detecterOuInstallerOllama() {
        console.log('🔍 Détection Ollama...');
        
        // Vérifier si Ollama est déjà installé
        try {
            const result = execSync('which ollama', { encoding: 'utf8' });
            this.config.ollama_path = result.trim();
            console.log(`✅ Ollama trouvé: ${this.config.ollama_path}`);
            return;
        } catch (error) {
            console.log('⚠️ Ollama non trouvé, installation automatique...');
        }
        
        // Installation automatique selon OS
        if (this.config.auto_install) {
            await this.installerOllamaAutomatique();
        } else {
            throw new Error('Ollama non trouvé et auto-installation désactivée');
        }
    }

    async installerOllamaAutomatique() {
        console.log('📥 Installation automatique Ollama...');
        
        const platform = process.platform;
        
        try {
            if (platform === 'darwin') {
                // macOS
                console.log('🍎 Installation Ollama pour macOS...');
                execSync('curl -fsSL https://ollama.ai/install.sh | sh', { stdio: 'inherit' });
                
            } else if (platform === 'linux') {
                // Linux
                console.log('🐧 Installation Ollama pour Linux...');
                execSync('curl -fsSL https://ollama.ai/install.sh | sh', { stdio: 'inherit' });
                
            } else if (platform === 'win32') {
                // Windows
                console.log('🪟 Installation Ollama pour Windows...');
                console.log('⚠️ Téléchargez manuellement depuis: https://ollama.ai/download/windows');
                throw new Error('Installation manuelle requise pour Windows');
                
            } else {
                throw new Error(`Plateforme non supportée: ${platform}`);
            }
            
            // Vérifier installation
            const result = execSync('which ollama', { encoding: 'utf8' });
            this.config.ollama_path = result.trim();
            console.log('✅ Ollama installé avec succès');
            
        } catch (error) {
            console.error('❌ Erreur installation Ollama:', error.message);
            throw error;
        }
    }

    async demarrerOllamaIntegre() {
        console.log('🚀 Démarrage Ollama intégré...');
        
        // Vérifier si déjà en cours
        try {
            const response = await axios.get(`http://${this.config.host}:${this.config.port}/api/version`, { timeout: 2000 });
            console.log('✅ Ollama déjà en cours d\'exécution');
            this.statut = 'actif';
            return;
        } catch (error) {
            // Pas encore démarré, on continue
        }
        
        // Variables d'environnement
        const env = {
            ...process.env,
            OLLAMA_MODELS: this.config.models_path,
            OLLAMA_HOST: `${this.config.host}:${this.config.port}`,
            OLLAMA_KEEP_ALIVE: '-1', // Garder modèles en mémoire
            OLLAMA_NUM_PARALLEL: '2',
            OLLAMA_MAX_LOADED_MODELS: '2'
        };
        
        // Démarrer processus Ollama
        this.processus_ollama = spawn(this.config.ollama_path, ['serve'], {
            env: env,
            stdio: ['pipe', 'pipe', 'pipe'],
            detached: false
        });
        
        // Gestion événements processus
        this.processus_ollama.on('error', (error) => {
            console.error('❌ Erreur processus Ollama:', error.message);
            this.statut = 'erreur';
        });
        
        this.processus_ollama.on('exit', (code) => {
            console.log(`⚠️ Processus Ollama terminé avec code: ${code}`);
            this.statut = 'arrete';
        });
        
        // Attendre démarrage
        await this.attendreDemanageOllama();
    }

    async attendreDemanageOllama() {
        console.log('⏳ Attente démarrage Ollama...');
        
        for (let i = 0; i < 30; i++) {
            try {
                const response = await axios.get(`http://${this.config.host}:${this.config.port}/api/version`, { timeout: 1000 });
                console.log('✅ Ollama démarré avec succès');
                this.statut = 'actif';
                return;
            } catch (error) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        throw new Error('Timeout démarrage Ollama');
    }

    async verifierModeles() {
        console.log('🔍 Vérification modèles disponibles...');
        
        try {
            const response = await axios.get(`http://${this.config.host}:${this.config.port}/api/tags`);
            this.modeles_disponibles = response.data.models || [];
            
            console.log(`📊 ${this.modeles_disponibles.length} modèle(s) disponible(s):`);
            this.modeles_disponibles.forEach(model => {
                const taille = (model.size / 1024 / 1024 / 1024).toFixed(1);
                console.log(`   • ${model.name}: ${taille}GB`);
            });
            
            // Installer modèle par défaut si aucun
            if (this.modeles_disponibles.length === 0) {
                await this.installerModeleParDefaut();
            }
            
        } catch (error) {
            console.error('❌ Erreur vérification modèles:', error.message);
        }
    }

    async installerModeleParDefaut() {
        console.log('📥 Installation modèle par défaut...');
        
        const modele_defaut = 'phi3:mini'; // Plus léger
        
        try {
            console.log(`⏳ Téléchargement ${modele_defaut}...`);
            
            // Utiliser pull API
            const response = await axios.post(`http://${this.config.host}:${this.config.port}/api/pull`, {
                name: modele_defaut
            }, {
                timeout: 300000 // 5 minutes
            });
            
            console.log(`✅ Modèle ${modele_defaut} installé`);
            
            // Mettre à jour liste
            await this.verifierModeles();
            
        } catch (error) {
            console.error('❌ Erreur installation modèle:', error.message);
        }
    }

    async genererReponse(prompt, modele = null) {
        if (this.statut !== 'actif') {
            return this.genererReponseSimulee(prompt);
        }
        
        // Sélectionner modèle
        const modele_utilise = modele || this.obtenirModeleParDefaut();
        
        if (!modele_utilise) {
            return this.genererReponseSimulee(prompt);
        }
        
        try {
            console.log(`🤖 Génération avec ${modele_utilise}...`);
            
            const response = await axios.post(`http://${this.config.host}:${this.config.port}/api/generate`, {
                model: modele_utilise,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: 0.7,
                    top_p: 0.9,
                    num_predict: 512
                }
            }, {
                timeout: 30000
            });
            
            return response.data.response;
            
        } catch (error) {
            console.error('❌ Erreur génération:', error.message);
            return this.genererReponseSimulee(prompt);
        }
    }

    obtenirModeleParDefaut() {
        if (this.modeles_disponibles.length === 0) {
            return null;
        }
        
        // Préférer modèles dans l'ordre
        const preferences = ['phi3:mini', 'mistral:7b', 'codellama:7b'];
        
        for (const pref of preferences) {
            const modele = this.modeles_disponibles.find(m => m.name.includes(pref));
            if (modele) {
                return modele.name;
            }
        }
        
        // Prendre le premier disponible
        return this.modeles_disponibles[0].name;
    }

    genererReponseSimulee(prompt) {
        console.log('🎭 Mode simulation activé');
        
        const reponses_simulees = [
            "Je suis LOUNA-AI avec mémoire thermique. Ma température CPU actuelle influence mes réponses.",
            "Système cognitif thermique actif. Plus la température augmente, plus mes performances s'améliorent.",
            "Mémoire thermique opérationnelle. Je m'adapte en temps réel à la chaleur de votre machine.",
            "Agent IA thermique en mode simulation. Mes capacités évoluent avec la température CPU.",
            "LOUNA-AI thermique simulé. Le concept 'Chaleur = Vie' guide mes réponses."
        ];
        
        const index = Math.floor(Math.random() * reponses_simulees.length);
        return reponses_simulees[index];
    }

    activerModeSimulation() {
        console.log('🎭 Activation mode simulation Ollama');
        this.statut = 'simulation';
    }

    async arreter() {
        console.log('🛑 Arrêt Ollama intégré...');
        
        if (this.processus_ollama) {
            this.processus_ollama.kill('SIGTERM');
            
            // Attendre arrêt propre
            await new Promise(resolve => {
                this.processus_ollama.on('exit', resolve);
                setTimeout(resolve, 5000); // Timeout 5s
            });
        }
        
        this.statut = 'arrete';
        console.log('✅ Ollama intégré arrêté');
    }

    obtenirStatut() {
        return {
            statut: this.statut,
            port: this.config.port,
            host: this.config.host,
            modeles_disponibles: this.modeles_disponibles.length,
            models_path: this.config.models_path,
            ollama_path: this.config.ollama_path,
            embedded_mode: this.config.embedded_mode
        };
    }

    async installerModele(nom_modele) {
        if (this.statut !== 'actif') {
            throw new Error('Ollama non actif');
        }
        
        console.log(`📥 Installation modèle: ${nom_modele}`);
        
        try {
            const response = await axios.post(`http://${this.config.host}:${this.config.port}/api/pull`, {
                name: nom_modele
            }, {
                timeout: 600000 // 10 minutes
            });
            
            console.log(`✅ Modèle ${nom_modele} installé`);
            await this.verifierModeles();
            
            return true;
            
        } catch (error) {
            console.error(`❌ Erreur installation ${nom_modele}:`, error.message);
            return false;
        }
    }

    async supprimerModele(nom_modele) {
        if (this.statut !== 'actif') {
            throw new Error('Ollama non actif');
        }
        
        console.log(`🗑️ Suppression modèle: ${nom_modele}`);
        
        try {
            await axios.delete(`http://${this.config.host}:${this.config.port}/api/delete`, {
                data: { name: nom_modele }
            });
            
            console.log(`✅ Modèle ${nom_modele} supprimé`);
            await this.verifierModeles();
            
            return true;
            
        } catch (error) {
            console.error(`❌ Erreur suppression ${nom_modele}:`, error.message);
            return false;
        }
    }

    // Test complet du système
    async testerSystemeComplet() {
        console.log('\n🧪 TEST SYSTÈME OLLAMA INTÉGRÉ');
        console.log('==============================');
        
        const statut = this.obtenirStatut();
        console.log('📊 Statut système:', JSON.stringify(statut, null, 2));
        
        // Test génération
        const prompt_test = "Bonjour, je suis LOUNA-AI thermique. Présente-toi brièvement.";
        const reponse = await this.genererReponse(prompt_test);
        console.log(`🤖 Test génération: "${reponse}"`);
        
        return {
            statut: statut,
            test_generation: reponse,
            timestamp: new Date().toISOString()
        };
    }
}

// Gestion arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt demandé...');
    if (global.ollama_integre) {
        await global.ollama_integre.arreter();
    }
    process.exit(0);
});

module.exports = { OllamaIntegre };
