#!/usr/bin/env node

/**
 * 🧪 TEST CHAT SIMPLE
 * 
 * Test direct du système de chat avec recherche automatique
 */

const http = require('http');

async function testerChat(message) {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({ message: message });
        
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/chat',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': data.length
            }
        };
        
        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(responseData);
                    resolve(response);
                } catch (error) {
                    reject(new Error('Réponse JSON invalide: ' + responseData));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.write(data);
        req.end();
    });
}

async function executerTests() {
    console.log('🧪 TEST CHAT SIMPLE AVEC RECHERCHE AUTOMATIQUE');
    console.log('===============================================');
    
    const questions = [
        "Bonjour, comment ça va ?",
        "Quelle est la capitale du Bhoutan ?",
        "Comment fonctionne le processeur M4 d'Apple ?",
        "Qu'est-ce que le framework Astro 4.0 ?",
        "Calcule 2 + 2",
        "Raconte-moi une blague"
    ];
    
    let testsReussis = 0;
    let recherchesDeclenchees = 0;
    
    for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        
        try {
            console.log(`\n📝 Test ${i + 1}/${questions.length}: "${question}"`);
            console.log('⏳ Envoi de la question...');
            
            const debut = Date.now();
            const response = await testerChat(question);
            const duree = Date.now() - debut;
            
            if (response.response) {
                console.log(`✅ Réponse reçue en ${duree}ms`);
                console.log(`🤖 Réponse: ${response.response.substring(0, 100)}...`);
                
                // Vérifier si recherche automatique déclenchée
                if (response.response.includes('🔍') || 
                    response.response.includes('Informations trouvées') ||
                    response.response.includes('Source:')) {
                    console.log('🔍 ✅ Recherche automatique détectée !');
                    recherchesDeclenchees++;
                } else {
                    console.log('💭 Réponse depuis mémoire/agent');
                }
                
                testsReussis++;
                
            } else if (response.error) {
                console.log(`❌ Erreur: ${response.error}`);
            } else {
                console.log('❌ Réponse vide');
            }
            
        } catch (error) {
            console.log(`❌ Erreur de connexion: ${error.message}`);
        }
        
        // Pause entre questions
        if (i < questions.length - 1) {
            console.log('⏳ Pause 2 secondes...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log('\n📊 RÉSULTATS FINAUX');
    console.log('===================');
    console.log(`✅ Tests réussis: ${testsReussis}/${questions.length}`);
    console.log(`🔍 Recherches déclenchées: ${recherchesDeclenchees}`);
    console.log(`📈 Taux de réussite: ${((testsReussis / questions.length) * 100).toFixed(1)}%`);
    console.log(`🎯 Taux de recherche: ${((recherchesDeclenchees / testsReussis) * 100).toFixed(1)}%`);
    
    if (testsReussis === questions.length) {
        console.log('\n🎉 TOUS LES TESTS RÉUSSIS !');
        console.log('✅ Le chat fonctionne parfaitement');
        if (recherchesDeclenchees > 0) {
            console.log('✅ La recherche automatique est opérationnelle');
        }
    } else {
        console.log('\n⚠️ Certains tests ont échoué');
    }
    
    // Test spécifique recherche automatique
    console.log('\n🔍 TEST SPÉCIFIQUE RECHERCHE AUTOMATIQUE');
    console.log('========================================');
    
    const questionsRecherche = [
        "Je ne sais pas cette information",
        "Désolé, je ne connais pas",
        "Information non disponible dans mes données"
    ];
    
    for (const question of questionsRecherche) {
        try {
            console.log(`\n🔍 Test recherche: "${question}"`);
            const response = await testerChat(question);
            
            if (response.response) {
                if (response.response.includes('🔍') || 
                    response.response.includes('recherche')) {
                    console.log('✅ Recherche automatique déclenchée correctement');
                } else {
                    console.log('⚠️ Recherche automatique non déclenchée');
                }
            }
            
        } catch (error) {
            console.log(`❌ Erreur: ${error.message}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🏁 TESTS TERMINÉS');
}

// Vérifier d'abord que le serveur répond
async function verifierServeur() {
    try {
        console.log('🔍 Vérification du serveur...');
        
        const response = await new Promise((resolve, reject) => {
            const req = http.request({
                hostname: 'localhost',
                port: 3000,
                path: '/api/status',
                method: 'GET'
            }, (res) => {
                resolve(res.statusCode);
            });
            
            req.on('error', reject);
            req.end();
        });
        
        if (response === 200) {
            console.log('✅ Serveur accessible');
            return true;
        } else {
            console.log(`⚠️ Serveur répond avec code ${response}`);
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Serveur inaccessible: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🚀 DÉMARRAGE TESTS CHAT');
    console.log('=======================');
    
    const serveurOK = await verifierServeur();
    
    if (serveurOK) {
        await executerTests();
    } else {
        console.log('❌ Impossible de tester - serveur non accessible');
        console.log('💡 Assurez-vous que le serveur est démarré avec:');
        console.log('   node serveur-interface-complete.js');
    }
}

// Exécuter si appelé directement
if (require.main === module) {
    main().catch(error => {
        console.error('💥 Erreur fatale:', error);
        process.exit(1);
    });
}

module.exports = { testerChat, executerTests, verifierServeur };
