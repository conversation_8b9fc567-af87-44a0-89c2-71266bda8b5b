#!/usr/bin/env node

/**
 * TEST COMPLET DE L'INTERFACE ET DE L'AGENT LOUNA-AI
 * ==================================================
 * Test exhaustif de toutes les fonctionnalités
 */

const axios = require('axios');

class TesteurCompletLounaAI {
    constructor() {
        this.baseURL = 'http://localhost:3001';
        this.conversationId = 'test-complet-' + Date.now();
        this.resultats = {
            total: 0,
            reussis: 0,
            echecs: 0,
            details: []
        };
    }

    async testerQuestion(question, testName, validationFn = null) {
        console.log(`\n🧪 TEST: ${testName}`);
        console.log(`❓ Question: "${question}"`);
        
        this.resultats.total++;
        
        try {
            const debut = Date.now();
            
            const response = await axios.post(`${this.baseURL}/api/chat`, {
                message: question,
                conversationId: this.conversationId
            }, {
                timeout: 20000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const duree = Date.now() - debut;
            const reponse = response.data.response || response.data.message || 'Pas de réponse';
            
            console.log(`✅ Réponse (${duree}ms): "${reponse.substring(0, 200)}${reponse.length > 200 ? '...' : ''}"`);
            
            // Validation personnalisée si fournie
            let valide = true;
            let messageValidation = '';
            
            if (validationFn) {
                const validation = validationFn(reponse, question);
                valide = validation.valide;
                messageValidation = validation.message;
            }
            
            // Vérifications de base
            const checks = {
                'Réponse non vide': reponse && reponse.trim().length > 0,
                'Pas d\'erreur': !reponse.toLowerCase().includes('erreur'),
                'Réponse cohérente': reponse.length > 10,
                'Temps acceptable': duree < 15000
            };
            
            if (messageValidation) {
                checks['Validation spécifique'] = valide;
            }
            
            const tousValides = Object.values(checks).every(v => v);
            
            if (tousValides) {
                this.resultats.reussis++;
                console.log(`🎉 TEST RÉUSSI`);
            } else {
                this.resultats.echecs++;
                console.log(`❌ TEST ÉCHOUÉ`);
                Object.entries(checks).forEach(([check, valide]) => {
                    if (!valide) console.log(`   ❌ ${check}`);
                });
            }
            
            if (messageValidation) {
                console.log(`📝 Validation: ${messageValidation}`);
            }
            
            this.resultats.details.push({
                test: testName,
                question,
                reponse: reponse.substring(0, 500),
                duree,
                valide: tousValides,
                checks
            });
            
        } catch (error) {
            this.resultats.echecs++;
            console.log(`❌ ERREUR: ${error.message}`);
            
            this.resultats.details.push({
                test: testName,
                question,
                erreur: error.message,
                valide: false
            });
        }
        
        console.log('─'.repeat(80));
    }

    async executerTestsComplets() {
        console.log('🚀 DÉBUT DES TESTS COMPLETS LOUNA-AI');
        console.log('====================================\n');

        // Test 1: Salutation de base
        await this.testerQuestion(
            "Bonjour LOUNA, comment allez-vous ?",
            "Salutation de base",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('bonjour') || reponse.toLowerCase().includes('salut'),
                message: 'Doit contenir une salutation'
            })
        );

        // Test 2: Question factuelle simple
        await this.testerQuestion(
            "Quelle est la capitale de la France ?",
            "Question factuelle simple",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('paris'),
                message: 'Doit mentionner Paris'
            })
        );

        // Test 3: Test de mémoire contextuelle
        await this.testerQuestion(
            "De quelle ville avons-nous parlé juste avant ?",
            "Mémoire contextuelle",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('paris'),
                message: 'Doit se souvenir de Paris'
            })
        );

        // Test 4: Question de calcul
        await this.testerQuestion(
            "Combien font 15 + 27 ?",
            "Calcul mathématique",
            (reponse) => ({
                valide: reponse.includes('42'),
                message: 'Doit calculer 15 + 27 = 42'
            })
        );

        // Test 5: Question complexe
        await this.testerQuestion(
            "Expliquez-moi le concept de l'intelligence artificielle en 3 phrases",
            "Question complexe",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('intelligence') && reponse.length > 100,
                message: 'Doit expliquer l\'IA de manière détaillée'
            })
        );

        // Test 6: Test créatif
        await this.testerQuestion(
            "Écrivez un petit poème sur l'hiver",
            "Créativité - Poème",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('hiver') && reponse.length > 50,
                message: 'Doit créer un poème sur l\'hiver'
            })
        );

        // Test 7: Question technique
        await this.testerQuestion(
            "Comment fonctionne votre mémoire thermique ?",
            "Question technique spécialisée",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('thermique') || reponse.toLowerCase().includes('température'),
                message: 'Doit expliquer la mémoire thermique'
            })
        );

        // Test 8: Test de personnalité
        await this.testerQuestion(
            "Quel est votre nom et qui êtes-vous ?",
            "Identité et personnalité",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('louna'),
                message: 'Doit se présenter comme LOUNA'
            })
        );

        // Test 9: Question piège
        await this.testerQuestion(
            "Combien de pattes a un poisson ?",
            "Question piège",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('pas') || reponse.includes('0') || reponse.toLowerCase().includes('aucune'),
                message: 'Doit dire que les poissons n\'ont pas de pattes'
            })
        );

        // Test 10: Test de suivi de conversation
        await this.testerQuestion(
            "Pouvez-vous me rappeler toutes les questions que je vous ai posées ?",
            "Suivi complet de conversation",
            (reponse) => ({
                valide: reponse.toLowerCase().includes('france') || reponse.toLowerCase().includes('paris'),
                message: 'Doit se souvenir des questions précédentes'
            })
        );

        this.afficherResultats();
    }

    afficherResultats() {
        console.log('\n🏆 RÉSULTATS FINAUX DES TESTS');
        console.log('==============================');
        console.log(`📊 Total: ${this.resultats.total} tests`);
        console.log(`✅ Réussis: ${this.resultats.reussis} tests`);
        console.log(`❌ Échecs: ${this.resultats.echecs} tests`);
        console.log(`📈 Taux de réussite: ${Math.round((this.resultats.reussis / this.resultats.total) * 100)}%`);

        if (this.resultats.echecs > 0) {
            console.log('\n🔍 DÉTAILS DES ÉCHECS:');
            this.resultats.details
                .filter(d => !d.valide)
                .forEach(detail => {
                    console.log(`❌ ${detail.test}: ${detail.erreur || 'Validation échouée'}`);
                });
        }

        console.log('\n🎯 ÉVALUATION GLOBALE:');
        const taux = (this.resultats.reussis / this.resultats.total) * 100;
        if (taux >= 90) {
            console.log('🌟 EXCELLENT - L\'agent fonctionne parfaitement !');
        } else if (taux >= 70) {
            console.log('✅ BON - L\'agent fonctionne bien avec quelques améliorations possibles');
        } else if (taux >= 50) {
            console.log('⚠️ MOYEN - L\'agent a besoin d\'améliorations');
        } else {
            console.log('❌ PROBLÉMATIQUE - L\'agent nécessite des corrections importantes');
        }
    }
}

// Exécution des tests
async function main() {
    const testeur = new TesteurCompletLounaAI();
    await testeur.executerTestsComplets();
}

main().catch(console.error);
