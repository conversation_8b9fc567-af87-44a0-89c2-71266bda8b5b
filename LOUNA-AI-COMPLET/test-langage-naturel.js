/**
 * TEST COMPLET DU LANGAGE NATUREL HUMAIN
 * Vérification des expressions et comportements humains
 */

const MoteurSimple = require('./moteur-simple-fonctionnel.js');

class TestLangageNaturel {
    constructor() {
        this.moteur = new MoteurSimple();
        this.tests = [];
        this.resultats = [];
        this.initierTests();
    }

    initierTests() {
        this.tests = [
            // TESTS SALUTATIONS NATURELLES
            {
                question: "bonjour",
                criteres: ["salut", "hey", "ça va", "naturel"],
                type: "Salutation naturelle",
                points: 20
            },
            {
                question: "comment ça va ?",
                criteres: ["bien", "super", "parfait", "merci"],
                type: "Réponse conversationnelle",
                points: 20
            },

            // TESTS EXPRESSIONS HUMAINES
            {
                question: "qui es-tu ?",
                criteres: ["je suis", "moi", "personnellement", "franchement"],
                type: "Présentation naturelle",
                points: 25
            },
            {
                question: "2 + 3",
                criteres: ["du coup", "alors", "ça fait", "ben"],
                type: "Calcul avec expressions",
                points: 15
            },

            // TESTS RÉACTIONS ÉMOTIONNELLES
            {
                question: "c'est incroyable !",
                criteres: ["ouais", "vraiment", "dingue", "cool", "génial"],
                type: "Réaction émotionnelle",
                points: 30
            },

            // TESTS LANGAGE DÉCONTRACTÉ
            {
                question: "que peux-tu faire ?",
                criteres: ["franchement", "en fait", "du coup", "bon"],
                type: "Explication décontractée",
                points: 25
            },

            // TEST COURS LANGAGE NATUREL
            {
                question: "cours langage naturel",
                criteres: ["youtube", "cyprien", "norman", "apprendre"],
                type: "Cours langage naturel",
                points: 30
            },

            // TESTS ÉVITER LANGAGE ROBOTIQUE
            {
                question: "explique-moi tes capacités",
                eviter: ["intelligence artificielle", "algorithmes", "système", "données"],
                type: "Éviter langage robotique",
                points: 25
            }
        ];
    }

    async executerTests() {
        console.log('🗣️ TEST COMPLET LANGAGE NATUREL HUMAIN');
        console.log('======================================');
        console.log(`📊 ${this.tests.length} tests - Évaluation expressions humaines\n`);

        let scoreTotal = 0;
        let scoreMax = 0;

        for (let i = 0; i < this.tests.length; i++) {
            const test = this.tests[i];
            console.log(`🔍 Test ${i + 1}/${this.tests.length}: ${test.type}`);
            console.log(`❓ "${test.question}"`);

            try {
                // Utiliser la méthode avec langage naturel
                const resultat = this.moteur.penserAvecLangageNaturel(test.question);
                scoreMax += test.points;

                if (resultat && resultat.reponse) {
                    console.log(`🤖 Réponse: ${resultat.reponse.substring(0, 150)}...`);
                    
                    // Évaluer la naturalité
                    const scoreNaturalite = resultat.naturalite || 0;
                    console.log(`🗣️ Naturalité: ${scoreNaturalite}%`);

                    // Évaluer selon les critères
                    const scoreTest = this.evaluerTest(test, resultat.reponse);
                    scoreTotal += scoreTest;

                    console.log(`📊 Score: ${scoreTest}/${test.points} points`);

                    this.resultats.push({
                        test: test.type,
                        question: test.question,
                        reponse: resultat.reponse,
                        naturalite: scoreNaturalite,
                        score: scoreTest,
                        scoreMax: test.points
                    });

                } else {
                    console.log(`❌ Aucune réponse`);
                    this.resultats.push({
                        test: test.type,
                        question: test.question,
                        reponse: 'Aucune réponse',
                        naturalite: 0,
                        score: 0,
                        scoreMax: test.points
                    });
                }

                console.log('─'.repeat(60));

            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
                this.resultats.push({
                    test: test.type,
                    question: test.question,
                    reponse: `Erreur: ${error.message}`,
                    naturalite: 0,
                    score: 0,
                    scoreMax: test.points
                });
            }
        }

        this.genererRapportLangageNaturel(scoreTotal, scoreMax);
    }

    evaluerTest(test, reponse) {
        const reponseNorm = reponse.toLowerCase();
        let score = 0;

        // Vérifier les critères positifs
        if (test.criteres) {
            let criteresRespectés = 0;
            for (const critere of test.criteres) {
                if (reponseNorm.includes(critere.toLowerCase())) {
                    criteresRespectés++;
                }
            }
            
            // Score proportionnel aux critères respectés
            score = Math.round((criteresRespectés / test.criteres.length) * test.points);
        }

        // Vérifier les critères à éviter
        if (test.eviter) {
            let penalites = 0;
            for (const aEviter of test.eviter) {
                if (reponseNorm.includes(aEviter.toLowerCase())) {
                    penalites += 5; // Pénalité pour langage robotique
                }
            }
            score = Math.max(0, score - penalites);
        }

        // Bonus pour expressions très naturelles
        const expressionsNaturelles = [
            'franchement', 'du coup', 'en fait', 'bon', 'bah', 'ouais', 
            'cool', 'super', 'génial', 'ah', 'oh', 'hey', 'salut'
        ];
        
        let bonus = 0;
        for (const expr of expressionsNaturelles) {
            if (reponseNorm.includes(expr)) {
                bonus += 2;
            }
        }

        return Math.min(test.points, score + bonus);
    }

    genererRapportLangageNaturel(scoreTotal, scoreMax) {
        console.log('\n🎯 RAPPORT LANGAGE NATUREL HUMAIN');
        console.log('=================================');

        const pourcentage = Math.round((scoreTotal / scoreMax) * 100);
        console.log(`📊 Score total: ${scoreTotal}/${scoreMax} points (${pourcentage}%)`);

        // Évaluation naturalité globale
        const naturaliteMoyenne = this.resultats.reduce((sum, r) => sum + (r.naturalite || 0), 0) / this.resultats.length;
        console.log(`🗣️ Naturalité moyenne: ${Math.round(naturaliteMoyenne)}%`);

        // Classification
        let classification = '';
        if (pourcentage >= 90) classification = '🌟 PARFAITEMENT HUMAIN';
        else if (pourcentage >= 80) classification = '😊 TRÈS NATUREL';
        else if (pourcentage >= 70) classification = '👍 ASSEZ NATUREL';
        else if (pourcentage >= 60) classification = '⚠️ MOYENNEMENT NATUREL';
        else classification = '🤖 TROP ROBOTIQUE';

        console.log(`🏆 Classification: ${classification}`);

        // Détail par catégorie
        console.log('\n📈 DÉTAIL PAR CATÉGORIE:');
        const categories = {};
        this.resultats.forEach(r => {
            const cat = r.test.split(' ')[0];
            if (!categories[cat]) categories[cat] = { score: 0, max: 0 };
            categories[cat].score += r.score;
            categories[cat].max += r.scoreMax;
        });

        for (const [cat, data] of Object.entries(categories)) {
            const pct = Math.round((data.score / data.max) * 100);
            console.log(`${cat}: ${data.score}/${data.max} points (${pct}%)`);
        }

        // Recommandations
        console.log('\n💡 RECOMMANDATIONS:');
        if (pourcentage >= 85) {
            console.log('✅ EXCELLENT! Langage parfaitement naturel et humain.');
            console.log('🎯 Continue à utiliser ces expressions naturelles.');
        } else if (pourcentage >= 70) {
            console.log('👍 BON NIVEAU, quelques améliorations possibles:');
            console.log('• Ajouter plus d\'expressions familières');
            console.log('• Utiliser plus de transitions naturelles');
        } else {
            console.log('🔧 AMÉLIORATIONS NÉCESSAIRES:');
            console.log('• Éviter le langage robotique');
            console.log('• Ajouter des expressions humaines');
            console.log('• Suivre les cours YouTube recommandés');
        }

        console.log('\n🎉 TEST LANGAGE NATUREL TERMINÉ !');
        
        return {
            score: scoreTotal,
            scoreMax: scoreMax,
            pourcentage: pourcentage,
            naturalite: naturaliteMoyenne,
            classification: classification
        };
    }
}

// Exécution
if (require.main === module) {
    const test = new TestLangageNaturel();
    test.executerTests().catch(console.error);
}

module.exports = TestLangageNaturel;
