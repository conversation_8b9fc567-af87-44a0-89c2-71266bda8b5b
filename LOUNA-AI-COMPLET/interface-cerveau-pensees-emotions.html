<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 CERVEAU LOUNA-AI - Pensées & Émotions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            grid-column: 1 / -1;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow-y: auto;
            max-height: 500px;
        }

        .section h2 {
            margin-bottom: 15px;
            color: #4fc3f7;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pensee-item, .emotion-item, .idee-item, .memoire-item {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .pensee-item {
            border-left-color: #81c784;
            animation: pulse-green 2s infinite;
        }

        .emotion-item {
            border-left-color: #f06292;
            animation: pulse-pink 2s infinite;
        }

        .idee-item {
            border-left-color: #ffb74d;
            animation: pulse-orange 2s infinite;
        }

        .memoire-item {
            border-left-color: #64b5f6;
            animation: pulse-blue 2s infinite;
        }

        .pensee-item:hover, .emotion-item:hover, .idee-item:hover, .memoire-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
        }

        .timestamp {
            font-size: 0.8em;
            color: #aaa;
            margin-bottom: 5px;
        }

        .content {
            font-size: 0.9em;
            line-height: 1.4;
        }

        .temperature {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7em;
        }

        .actions {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-copy {
            background: rgba(76, 175, 80, 0.3);
        }

        .btn-delete {
            background: rgba(244, 67, 54, 0.3);
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4fc3f7;
        }

        .controls {
            grid-column: 1 / -1;
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .control-btn {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        @keyframes pulse-green {
            0%, 100% { border-left-color: #81c784; }
            50% { border-left-color: #a5d6a7; }
        }

        @keyframes pulse-pink {
            0%, 100% { border-left-color: #f06292; }
            50% { border-left-color: #f48fb1; }
        }

        @keyframes pulse-orange {
            0%, 100% { border-left-color: #ffb74d; }
            50% { border-left-color: #ffcc02; }
        }

        @keyframes pulse-blue {
            0%, 100% { border-left-color: #64b5f6; }
            50% { border-left-color: #90caf9; }
        }

        .loading {
            text-align: center;
            color: #aaa;
            font-style: italic;
        }

        .empty {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }

        .search-box {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .search-box::placeholder {
            color: #aaa;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: rgba(79, 195, 247, 0.3);
            border-color: #4fc3f7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 CERVEAU LOUNA-AI - Pensées & Émotions</h1>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="total-pensees">0</div>
                    <div>Pensées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-emotions">0</div>
                    <div>Émotions</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-idees">0</div>
                    <div>Idées Créatives</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-memoires">0</div>
                    <div>Mémoires</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="temperature-moyenne">0°C</div>
                    <div>Température</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🧠 Pensées Récentes</h2>
            <input type="text" class="search-box" id="search-pensees" placeholder="Rechercher dans les pensées...">
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">Toutes</button>
                <button class="filter-btn" data-filter="recent">Récentes</button>
                <button class="filter-btn" data-filter="important">Importantes</button>
            </div>
            <div id="pensees-container" class="loading">Chargement des pensées...</div>
        </div>

        <div class="section">
            <h2>🎭 États Émotionnels</h2>
            <input type="text" class="search-box" id="search-emotions" placeholder="Rechercher dans les émotions...">
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">Toutes</button>
                <button class="filter-btn" data-filter="positive">Positives</button>
                <button class="filter-btn" data-filter="negative">Négatives</button>
                <button class="filter-btn" data-filter="neutral">Neutres</button>
            </div>
            <div id="emotions-container" class="loading">Chargement des émotions...</div>
        </div>

        <div class="section">
            <h2>💡 Idées Créatives</h2>
            <input type="text" class="search-box" id="search-idees" placeholder="Rechercher dans les idées...">
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">Toutes</button>
                <button class="filter-btn" data-filter="new">Nouvelles</button>
                <button class="filter-btn" data-filter="innovative">Innovantes</button>
            </div>
            <div id="idees-container" class="loading">Chargement des idées créatives...</div>
        </div>

        <div class="section">
            <h2>💾 Mémoires Thermiques</h2>
            <input type="text" class="search-box" id="search-memoires" placeholder="Rechercher dans les mémoires...">
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">Toutes</button>
                <button class="filter-btn" data-filter="hot">Chaudes</button>
                <button class="filter-btn" data-filter="cold">Froides</button>
                <button class="filter-btn" data-filter="recent">Récentes</button>
            </div>
            <div id="memoires-container" class="loading">Chargement des mémoires...</div>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="actualiserTout()">🔄 Actualiser</button>
            <button class="control-btn" onclick="sauvegarderTout()">💾 Sauvegarder</button>
            <button class="control-btn" onclick="exporterDonnees()">📤 Exporter</button>
            <button class="control-btn" onclick="nettoyerMemoire()">🧹 Nettoyer</button>
        </div>
    </div>

    <script>
        let donneesCerveau = {
            pensees: [],
            emotions: [],
            idees: [],
            memoires: []
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            chargerDonneesCerveau();
            configurerFiltres();
            configurerRecherche();
            
            // Actualisation automatique toutes les 30 secondes
            setInterval(chargerDonneesCerveau, 30000);
        });

        async function chargerDonneesCerveau() {
            try {
                console.log('🧠 Chargement des données du cerveau...');
                
                // Charger les pensées (depuis mémoire thermique)
                const responsePensees = await fetch('/api/stats');
                if (responsePensees.ok) {
                    const stats = await responsePensees.json();
                    if (stats.stats && stats.stats.memoire_thermique) {
                        donneesCerveau.memoires = transformerMemoiresEnPensees(stats.stats.memoire_thermique);
                    }
                }

                // Charger les émotions
                const responseEmotions = await fetch('/api/emotions/status');
                if (responseEmotions.ok) {
                    const emotions = await responseEmotions.json();
                    donneesCerveau.emotions = transformerEmotions(emotions);
                }

                // Charger les idées créatives
                const responseIdees = await fetch('/api/emotions/idees');
                if (responseIdees.ok) {
                    const idees = await responseIdees.json();
                    donneesCerveau.idees = transformerIdees(idees);
                }

                afficherDonnees();
                mettreAJourStatistiques();
                
            } catch (error) {
                console.error('❌ Erreur chargement données cerveau:', error);
                afficherErreur();
            }
        }

        function transformerMemoiresEnPensees(stats) {
            const pensees = [];
            
            // Simuler des pensées basées sur les statistiques
            if (stats.totalEntries > 0) {
                for (let i = 0; i < Math.min(10, stats.totalEntries); i++) {
                    pensees.push({
                        id: `pensee_${i}`,
                        contenu: `Réflexion sur les ${stats.totalEntries} mémoires stockées avec une température moyenne de ${stats.averageTemperature?.toFixed(1)}°C`,
                        timestamp: Date.now() - (i * 60000),
                        temperature: stats.averageTemperature || 50,
                        importance: Math.random(),
                        type: 'reflexion'
                    });
                }
            }
            
            return pensees;
        }

        function transformerEmotions(emotionsData) {
            const emotions = [];
            
            if (emotionsData && !emotionsData.erreur) {
                // Transformer les données d'émotions en format d'affichage
                const etats = ['curiosité', 'satisfaction', 'concentration', 'créativité', 'détermination'];
                
                etats.forEach((etat, index) => {
                    emotions.push({
                        id: `emotion_${index}`,
                        type: etat,
                        intensite: Math.random() * 100,
                        timestamp: Date.now() - (index * 30000),
                        description: `État émotionnel: ${etat}`,
                        valence: Math.random() > 0.5 ? 'positive' : 'neutral'
                    });
                });
            }
            
            return emotions;
        }

        function transformerIdees(ideesData) {
            const idees = [];
            
            if (ideesData && ideesData.idees) {
                ideesData.idees.forEach((idee, index) => {
                    idees.push({
                        id: `idee_${index}`,
                        contenu: idee,
                        timestamp: Date.now() - (index * 120000),
                        innovation: Math.random() * 100,
                        faisabilite: Math.random() * 100,
                        type: 'creative'
                    });
                });
            }
            
            return idees;
        }

        function afficherDonnees() {
            afficherPensees();
            afficherEmotions();
            afficherIdees();
            afficherMemoires();
        }

        function afficherPensees() {
            const container = document.getElementById('pensees-container');
            
            if (donneesCerveau.pensees.length === 0) {
                container.innerHTML = '<div class="empty">Aucune pensée récente</div>';
                return;
            }
            
            container.innerHTML = donneesCerveau.pensees.map(pensee => `
                <div class="pensee-item" data-id="${pensee.id}">
                    <div class="temperature">${pensee.temperature?.toFixed(1)}°C</div>
                    <div class="timestamp">${new Date(pensee.timestamp).toLocaleString()}</div>
                    <div class="content">${pensee.contenu}</div>
                    <div class="actions">
                        <button class="btn btn-copy" onclick="copierContenu('${pensee.id}', 'pensee')">📋 Copier</button>
                        <button class="btn btn-delete" onclick="supprimerElement('${pensee.id}', 'pensees')">🗑️ Supprimer</button>
                    </div>
                </div>
            `).join('');
        }

        function afficherEmotions() {
            const container = document.getElementById('emotions-container');
            
            if (donneesCerveau.emotions.length === 0) {
                container.innerHTML = '<div class="empty">Aucune émotion détectée</div>';
                return;
            }
            
            container.innerHTML = donneesCerveau.emotions.map(emotion => `
                <div class="emotion-item" data-id="${emotion.id}">
                    <div class="temperature">${emotion.intensite?.toFixed(1)}%</div>
                    <div class="timestamp">${new Date(emotion.timestamp).toLocaleString()}</div>
                    <div class="content">
                        <strong>${emotion.type}</strong><br>
                        ${emotion.description}
                    </div>
                    <div class="actions">
                        <button class="btn btn-copy" onclick="copierContenu('${emotion.id}', 'emotion')">📋 Copier</button>
                        <button class="btn btn-delete" onclick="supprimerElement('${emotion.id}', 'emotions')">🗑️ Supprimer</button>
                    </div>
                </div>
            `).join('');
        }

        function afficherIdees() {
            const container = document.getElementById('idees-container');
            
            if (donneesCerveau.idees.length === 0) {
                container.innerHTML = '<div class="empty">Aucune idée créative générée</div>';
                return;
            }
            
            container.innerHTML = donneesCerveau.idees.map(idee => `
                <div class="idee-item" data-id="${idee.id}">
                    <div class="temperature">${idee.innovation?.toFixed(1)}%</div>
                    <div class="timestamp">${new Date(idee.timestamp).toLocaleString()}</div>
                    <div class="content">${idee.contenu}</div>
                    <div class="actions">
                        <button class="btn btn-copy" onclick="copierContenu('${idee.id}', 'idee')">📋 Copier</button>
                        <button class="btn btn-delete" onclick="supprimerElement('${idee.id}', 'idees')">🗑️ Supprimer</button>
                    </div>
                </div>
            `).join('');
        }

        function afficherMemoires() {
            const container = document.getElementById('memoires-container');
            
            if (donneesCerveau.memoires.length === 0) {
                container.innerHTML = '<div class="empty">Aucune mémoire thermique</div>';
                return;
            }
            
            container.innerHTML = donneesCerveau.memoires.map(memoire => `
                <div class="memoire-item" data-id="${memoire.id}">
                    <div class="temperature">${memoire.temperature?.toFixed(1)}°C</div>
                    <div class="timestamp">${new Date(memoire.timestamp).toLocaleString()}</div>
                    <div class="content">${memoire.contenu}</div>
                    <div class="actions">
                        <button class="btn btn-copy" onclick="copierContenu('${memoire.id}', 'memoire')">📋 Copier</button>
                        <button class="btn btn-delete" onclick="supprimerElement('${memoire.id}', 'memoires')">🗑️ Supprimer</button>
                    </div>
                </div>
            `).join('');
        }

        function mettreAJourStatistiques() {
            document.getElementById('total-pensees').textContent = donneesCerveau.pensees.length;
            document.getElementById('total-emotions').textContent = donneesCerveau.emotions.length;
            document.getElementById('total-idees').textContent = donneesCerveau.idees.length;
            document.getElementById('total-memoires').textContent = donneesCerveau.memoires.length;
            
            // Calculer température moyenne
            const temperatures = [
                ...donneesCerveau.pensees.map(p => p.temperature || 0),
                ...donneesCerveau.memoires.map(m => m.temperature || 0)
            ];
            
            const tempMoyenne = temperatures.length > 0 ? 
                temperatures.reduce((a, b) => a + b, 0) / temperatures.length : 0;
            
            document.getElementById('temperature-moyenne').textContent = `${tempMoyenne.toFixed(1)}°C`;
        }

        function copierContenu(id, type) {
            const element = document.querySelector(`[data-id="${id}"] .content`);
            if (element) {
                navigator.clipboard.writeText(element.textContent).then(() => {
                    alert('✅ Contenu copié dans le presse-papiers !');
                });
            }
        }

        function supprimerElement(id, categorie) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
                donneesCerveau[categorie] = donneesCerveau[categorie].filter(item => item.id !== id);
                afficherDonnees();
                mettreAJourStatistiques();
                alert('✅ Élément supprimé !');
            }
        }

        function actualiserTout() {
            chargerDonneesCerveau();
            alert('🔄 Données actualisées !');
        }

        function sauvegarderTout() {
            const donnees = JSON.stringify(donneesCerveau, null, 2);
            const blob = new Blob([donnees], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cerveau-louna-ai-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            alert('💾 Données sauvegardées !');
        }

        function exporterDonnees() {
            const texte = [
                '🧠 EXPORT CERVEAU LOUNA-AI',
                '========================',
                '',
                '📝 PENSÉES:',
                ...donneesCerveau.pensees.map(p => `- ${p.contenu} (${p.temperature?.toFixed(1)}°C)`),
                '',
                '🎭 ÉMOTIONS:',
                ...donneesCerveau.emotions.map(e => `- ${e.type}: ${e.description} (${e.intensite?.toFixed(1)}%)`),
                '',
                '💡 IDÉES CRÉATIVES:',
                ...donneesCerveau.idees.map(i => `- ${i.contenu} (Innovation: ${i.innovation?.toFixed(1)}%)`),
                '',
                '💾 MÉMOIRES:',
                ...donneesCerveau.memoires.map(m => `- ${m.contenu} (${m.temperature?.toFixed(1)}°C)`)
            ].join('\n');
            
            const blob = new Blob([texte], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `export-cerveau-louna-ai-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            alert('📤 Données exportées !');
        }

        function nettoyerMemoire() {
            if (confirm('Êtes-vous sûr de vouloir nettoyer la mémoire ? Cette action est irréversible.')) {
                donneesCerveau = { pensees: [], emotions: [], idees: [], memoires: [] };
                afficherDonnees();
                mettreAJourStatistiques();
                alert('🧹 Mémoire nettoyée !');
            }
        }

        function configurerFiltres() {
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const section = this.closest('.section');
                    section.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    // Logique de filtrage à implémenter
                });
            });
        }

        function configurerRecherche() {
            document.querySelectorAll('.search-box').forEach(input => {
                input.addEventListener('input', function() {
                    // Logique de recherche à implémenter
                    console.log('Recherche:', this.value);
                });
            });
        }

        function afficherErreur() {
            document.querySelectorAll('.loading').forEach(container => {
                container.innerHTML = '<div class="empty">❌ Erreur de chargement</div>';
            });
        }
    </script>
</body>
</html>
