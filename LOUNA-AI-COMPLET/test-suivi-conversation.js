/**
 * TEST SPÉCIFIQUE SUIVI CONVERSATIONNEL
 * Reproduction du problème "Italie" -> "et la Guadeloupe ?"
 */

const MoteurSimple = require('./moteur-simple-fonctionnel.js');

class TestSuiviConversation {
    constructor() {
        this.moteur = new MoteurSimple();
        this.conversations = [];
        this.initierTestsConversation();
    }

    initierTestsConversation() {
        this.conversations = [
            // TEST 1 - REPRODUCTION DU PROBLÈME EXACT
            {
                nom: "Test Italie -> Guadeloupe",
                echanges: [
                    {
                        question: "quelle est la capitale de l'Italie ?",
                        reponseAttendue: "Rome"
                    },
                    {
                        question: "et la Guadeloupe ?",
                        reponseAttendue: "Basse-Terre",
                        problemeOriginal: "Répondait 'Italie' au lieu de 'Basse-Terre'"
                    }
                ]
            },

            // TEST 2 - AUTRES PAYS
            {
                nom: "Test France -> Martinique",
                echanges: [
                    {
                        question: "capitale de la France ?",
                        reponseAttendue: "Paris"
                    },
                    {
                        question: "et la Martinique ?",
                        reponseAttendue: "Fort-de-France"
                    }
                ]
            },

            // TEST 3 - ENCHAÎNEMENT MULTIPLE
            {
                nom: "Test enchaînement multiple",
                echanges: [
                    {
                        question: "quelle est la capitale de l'Espagne ?",
                        reponseAttendue: "Madrid"
                    },
                    {
                        question: "et le Portugal ?",
                        reponseAttendue: "Lisbonne"
                    },
                    {
                        question: "et la Grèce ?",
                        reponseAttendue: "Athènes"
                    }
                ]
            },

            // TEST 4 - CHANGEMENT DE SUJET
            {
                nom: "Test changement de sujet",
                echanges: [
                    {
                        question: "capitale de l'Allemagne ?",
                        reponseAttendue: "Berlin"
                    },
                    {
                        question: "combien font 2 + 3 ?",
                        reponseAttendue: "5",
                        changementSujet: true
                    },
                    {
                        question: "et la Suisse ?",
                        reponseAttendue: "Berne",
                        noteSpeciale: "Doit reprendre le contexte géographique"
                    }
                ]
            },

            // TEST 5 - VARIATIONS LINGUISTIQUES
            {
                nom: "Test variations linguistiques",
                echanges: [
                    {
                        question: "chef-lieu de la Réunion ?",
                        reponseAttendue: "Saint-Denis"
                    },
                    {
                        question: "et pour la Guyane ?",
                        reponseAttendue: "Cayenne"
                    },
                    {
                        question: "et Mayotte alors ?",
                        reponseAttendue: "Mamoudzou"
                    }
                ]
            }
        ];
    }

    async executerTestsConversation() {
        console.log('🗣️ TEST SUIVI CONVERSATIONNEL - CORRECTION PROBLÈME');
        console.log('===================================================');
        console.log('🎯 Reproduction du problème "Italie" -> "et la Guadeloupe ?"\n');

        let testsReussis = 0;
        let testsTotal = 0;

        for (let i = 0; i < this.conversations.length; i++) {
            const conversation = this.conversations[i];
            console.log(`🔍 ${conversation.nom}`);
            console.log('─'.repeat(50));

            // Réinitialiser le contexte pour chaque conversation
            this.moteur.reinitialiserContexte();

            let conversationReussie = true;

            for (let j = 0; j < conversation.echanges.length; j++) {
                const echange = conversation.echanges[j];
                testsTotal++;

                console.log(`❓ Question ${j + 1}: "${echange.question}"`);

                try {
                    const resultat = this.moteur.penser(echange.question);

                    if (resultat && resultat.reponse) {
                        console.log(`🤖 Réponse: ${resultat.reponse}`);
                        console.log(`🎯 Source: ${resultat.source}`);

                        // Vérifier si la réponse contient ce qui est attendu
                        const reponseContientAttendu = resultat.reponse.toLowerCase()
                            .includes(echange.reponseAttendue.toLowerCase());

                        if (reponseContientAttendu) {
                            console.log(`✅ SUCCÈS - Contient "${echange.reponseAttendue}"`);
                            testsReussis++;
                        } else {
                            console.log(`❌ ÉCHEC - Attendu "${echange.reponseAttendue}"`);
                            conversationReussie = false;

                            if (echange.problemeOriginal) {
                                console.log(`🚨 Problème original: ${echange.problemeOriginal}`);
                            }
                        }

                        if (echange.noteSpeciale) {
                            console.log(`📝 Note: ${echange.noteSpeciale}`);
                        }

                    } else {
                        console.log(`❌ AUCUNE RÉPONSE`);
                        conversationReussie = false;
                    }

                } catch (error) {
                    console.log(`❌ ERREUR: ${error.message}`);
                    conversationReussie = false;
                }

                console.log('');
            }

            if (conversationReussie) {
                console.log(`🎉 CONVERSATION RÉUSSIE !`);
            } else {
                console.log(`🔧 CONVERSATION NÉCESSITE CORRECTIONS`);
            }

            console.log('═'.repeat(60));
            console.log('');
        }

        this.genererRapportSuiviConversation(testsReussis, testsTotal);
    }

    genererRapportSuiviConversation(testsReussis, testsTotal) {
        console.log('🎯 RAPPORT SUIVI CONVERSATIONNEL');
        console.log('=================================');

        const pourcentage = Math.round((testsReussis / testsTotal) * 100);
        console.log(`📊 Tests réussis: ${testsReussis}/${testsTotal} (${pourcentage}%)`);

        // Évaluation spécifique
        if (pourcentage >= 90) {
            console.log('🌟 EXCELLENT - Suivi conversationnel parfait !');
            console.log('✅ Le problème "Italie" -> "Guadeloupe" est résolu');
        } else if (pourcentage >= 80) {
            console.log('👍 BON - Suivi conversationnel fonctionnel');
            console.log('⚠️ Quelques améliorations possibles');
        } else if (pourcentage >= 60) {
            console.log('⚠️ MOYEN - Suivi conversationnel partiel');
            console.log('🔧 Corrections nécessaires');
        } else {
            console.log('❌ PROBLÈME - Suivi conversationnel défaillant');
            console.log('🚨 Corrections urgentes requises');
        }

        // Test spécifique du problème original
        console.log('\n🎯 TEST SPÉCIFIQUE DU PROBLÈME ORIGINAL:');
        console.log('Question 1: "quelle est la capitale de l\'Italie ?"');
        console.log('Question 2: "et la Guadeloupe ?"');
        
        // Simuler le test exact
        this.moteur.reinitialiserContexte();
        
        const reponse1 = this.moteur.penser("quelle est la capitale de l'Italie ?");
        console.log(`Réponse 1: ${reponse1.reponse}`);
        
        const reponse2 = this.moteur.penser("et la Guadeloupe ?");
        console.log(`Réponse 2: ${reponse2.reponse}`);
        
        if (reponse2.reponse.toLowerCase().includes('basse-terre')) {
            console.log('✅ PROBLÈME RÉSOLU ! Répond correctement "Basse-Terre"');
        } else if (reponse2.reponse.toLowerCase().includes('italie')) {
            console.log('❌ PROBLÈME PERSISTE ! Répond encore "Italie"');
        } else {
            console.log('⚠️ RÉPONSE INATTENDUE - À vérifier');
        }

        console.log('\n🎉 TEST SUIVI CONVERSATIONNEL TERMINÉ !');
        
        return {
            testsReussis: testsReussis,
            testsTotal: testsTotal,
            pourcentage: pourcentage,
            problemeResolu: reponse2.reponse.toLowerCase().includes('basse-terre')
        };
    }

    // Méthode pour tester rapidement le problème spécifique
    testerProblemeSpecifique() {
        console.log('🚨 TEST RAPIDE DU PROBLÈME SPÉCIFIQUE');
        console.log('=====================================');
        
        this.moteur.reinitialiserContexte();
        
        console.log('❓ "quelle est la capitale de l\'Italie ?"');
        const rep1 = this.moteur.penser("quelle est la capitale de l'Italie ?");
        console.log(`🤖 ${rep1.reponse}`);
        
        console.log('\n❓ "et la Guadeloupe ?"');
        const rep2 = this.moteur.penser("et la Guadeloupe ?");
        console.log(`🤖 ${rep2.reponse}`);
        
        if (rep2.reponse.toLowerCase().includes('basse-terre')) {
            console.log('\n✅ SUCCÈS ! Problème résolu !');
            return true;
        } else {
            console.log('\n❌ ÉCHEC ! Problème persiste !');
            return false;
        }
    }
}

// Exécution
if (require.main === module) {
    const test = new TestSuiviConversation();
    
    // Test rapide d'abord
    console.log('🔥 TEST RAPIDE DU PROBLÈME:');
    test.testerProblemeSpecifique();
    
    console.log('\n' + '═'.repeat(60) + '\n');
    
    // Test complet ensuite
    test.executerTestsConversation().catch(console.error);
}

module.exports = TestSuiviConversation;
