<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Test QI Avancé LOUNA-AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .question {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 4px solid #4fc3f7;
        }

        .question h3 {
            color: #4fc3f7;
            margin-bottom: 15px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .option {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            background: rgba(79, 195, 247, 0.3);
            transform: translateY(-2px);
        }

        .option.selected {
            background: rgba(76, 175, 80, 0.4);
            border-color: #4caf50;
        }

        .btn {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        .btn-home {
            background: linear-gradient(45deg, #4caf50, #45a049);
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress {
            height: 100%;
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
            transition: width 0.3s ease;
        }

        .results {
            display: none;
            text-align: center;
        }

        .score {
            font-size: 3em;
            font-weight: bold;
            color: #4fc3f7;
            margin: 20px 0;
        }

        .analysis {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
        }

        .math-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px;
            border-radius: 5px;
            width: 200px;
            text-align: center;
            font-size: 16px;
        }

        .sequence-pattern {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .pattern-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            min-width: 60px;
            text-align: center;
            font-weight: bold;
        }

        .pattern-missing {
            background: rgba(255, 193, 7, 0.3);
            border-color: #ffc107;
        }

        .logic-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
            max-width: 300px;
        }

        .grid-item {
            aspect-ratio: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .grid-item:hover {
            background: rgba(79, 195, 247, 0.3);
        }

        .difficulty-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .difficulty-btn {
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .difficulty-btn.active {
            background: rgba(79, 195, 247, 0.4);
            border-color: #4fc3f7;
        }
    </style>
</head>
<body>
    <div class="timer" id="timer">⏱️ 00:00</div>

    <div class="container">
        <div class="header">
            <button onclick="retourAccueil()" class="btn btn-home">🏠 Retour Accueil</button>
            <h1>🧠 Test QI Avancé LOUNA-AI</h1>
            <p>Évaluation complète des capacités intellectuelles</p>
            
            <div class="difficulty-selector">
                <button class="difficulty-btn active" onclick="setDifficulty('normal')" data-level="normal">📚 Normal</button>
                <button class="difficulty-btn" onclick="setDifficulty('avance')" data-level="avance">🎓 Avancé</button>
                <button class="difficulty-btn" onclick="setDifficulty('expert')" data-level="expert">🧠 Expert</button>
                <button class="difficulty-btn" onclick="setDifficulty('genius')" data-level="genius">⚡ Génie</button>
            </div>

            <div class="progress-bar">
                <div class="progress" id="progress"></div>
            </div>
            <div id="question-counter">Question 1 / 20</div>
        </div>

        <div id="test-container">
            <!-- Les questions seront générées dynamiquement -->
        </div>

        <div class="results" id="results">
            <h2>🎉 Résultats du Test QI</h2>
            <div class="score" id="final-score">0</div>
            <div class="analysis" id="analysis"></div>
            <button class="btn" onclick="recommencerTest()">🔄 Recommencer</button>
            <button class="btn" onclick="envoyerResultats()">📊 Envoyer à LOUNA-AI</button>
        </div>
    </div>

    <script>
        let currentQuestion = 0;
        let totalQuestions = 20;
        let score = 0;
        let startTime = Date.now();
        let difficulty = 'normal';
        let answers = [];
        let timer;

        const questions = {
            normal: [
                {
                    type: 'logique',
                    question: 'Quelle est la suite logique ?',
                    pattern: [2, 4, 8, 16, '?'],
                    options: ['24', '32', '30', '28'],
                    correct: 1,
                    explanation: 'Chaque nombre est multiplié par 2'
                },
                {
                    type: 'mathematique',
                    question: 'Résolvez : (15 × 4) ÷ 3 + 7 = ?',
                    input: true,
                    correct: 27,
                    explanation: '(15 × 4) ÷ 3 + 7 = 60 ÷ 3 + 7 = 20 + 7 = 27'
                },
                {
                    type: 'analogie',
                    question: 'Chat est à Miauler comme Chien est à ?',
                    options: ['Courir', 'Aboyer', 'Dormir', 'Manger'],
                    correct: 1,
                    explanation: 'Relation son/animal'
                }
            ],
            avance: [
                {
                    type: 'sequence',
                    question: 'Trouvez le nombre manquant dans la séquence de Fibonacci modifiée',
                    pattern: [1, 1, 2, 3, 5, 8, '?', 21],
                    options: ['11', '13', '15', '17'],
                    correct: 1,
                    explanation: 'Suite de Fibonacci : 1+1=2, 1+2=3, 2+3=5, 3+5=8, 5+8=13'
                },
                {
                    type: 'logique_complexe',
                    question: 'Si tous les A sont B, et certains B sont C, alors :',
                    options: [
                        'Tous les A sont C',
                        'Certains A peuvent être C',
                        'Aucun A n\'est C',
                        'Impossible à déterminer'
                    ],
                    correct: 1,
                    explanation: 'Logique syllogistique : certains A peuvent être C via B'
                }
            ],
            expert: [
                {
                    type: 'mathematique_avance',
                    question: 'Résolvez : ∫(2x + 3)dx de 0 à 2',
                    input: true,
                    correct: 10,
                    explanation: '∫(2x + 3)dx = x² + 3x. [x² + 3x]₀² = (4 + 6) - 0 = 10'
                },
                {
                    type: 'logique_formelle',
                    question: 'Dans un système logique, si P→Q et ¬Q, alors :',
                    options: ['P', '¬P', 'Q', 'P∧Q'],
                    correct: 1,
                    explanation: 'Modus tollens : P→Q, ¬Q ⊢ ¬P'
                }
            ],
            genius: [
                {
                    type: 'theorie_nombres',
                    question: 'Quel est le 10ème nombre premier de Mersenne ?',
                    input: true,
                    correct: 89,
                    explanation: 'Les nombres premiers de Mersenne : 3, 7, 31, 127, 8191, 131071, 524287, 2147483647, 2305843009213693951, 618970019642690137449562111'
                },
                {
                    type: 'logique_quantique',
                    question: 'Dans l\'interprétation de Copenhague, l\'état |ψ⟩ = α|0⟩ + β|1⟩ représente :',
                    options: [
                        'Une superposition quantique',
                        'Un état classique',
                        'Une mesure déterministe',
                        'Un paradoxe logique'
                    ],
                    correct: 0,
                    explanation: 'Superposition quantique avec amplitudes α et β'
                }
            ]
        };

        function startTimer() {
            timer = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                document.getElementById('timer').textContent = 
                    `⏱️ ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function setDifficulty(level) {
            difficulty = level;
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-level="${level}"]`).classList.add('active');
            
            // Ajuster le nombre de questions selon la difficulté
            totalQuestions = {
                'normal': 15,
                'avance': 20,
                'expert': 25,
                'genius': 30
            }[level];
            
            generateQuestions();
        }

        function generateQuestions() {
            const questionSet = questions[difficulty] || questions.normal;
            // Mélanger et répéter les questions pour atteindre le total
            const allQuestions = [];
            while (allQuestions.length < totalQuestions) {
                allQuestions.push(...questionSet);
            }
            
            // Prendre seulement le nombre nécessaire
            window.currentQuestions = allQuestions.slice(0, totalQuestions);
            
            showQuestion();
        }

        function showQuestion() {
            if (currentQuestion >= totalQuestions) {
                showResults();
                return;
            }

            const question = window.currentQuestions[currentQuestion];
            const container = document.getElementById('test-container');
            
            let html = `
                <div class="test-section">
                    <div class="question">
                        <h3>Question ${currentQuestion + 1} - ${question.type.toUpperCase()}</h3>
                        <p>${question.question}</p>
            `;

            if (question.pattern) {
                html += '<div class="sequence-pattern">';
                question.pattern.forEach(item => {
                    html += `<div class="pattern-item ${item === '?' ? 'pattern-missing' : ''}">${item}</div>`;
                });
                html += '</div>';
            }

            if (question.input) {
                html += `
                    <div style="margin: 20px 0;">
                        <input type="number" class="math-input" id="answer-input" placeholder="Votre réponse">
                    </div>
                `;
            } else if (question.options) {
                html += '<div class="options">';
                question.options.forEach((option, index) => {
                    html += `<div class="option" onclick="selectOption(${index})" data-index="${index}">${option}</div>`;
                });
                html += '</div>';
            }

            html += `
                    </div>
                    <div style="text-align: center;">
                        <button class="btn" onclick="nextQuestion()">Suivant ➡️</button>
                    </div>
                </div>
            `;

            container.innerHTML = html;
            updateProgress();
        }

        function selectOption(index) {
            document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            document.querySelector(`[data-index="${index}"]`).classList.add('selected');
            window.selectedAnswer = index;
        }

        function nextQuestion() {
            const question = window.currentQuestions[currentQuestion];
            let userAnswer = null;
            let isCorrect = false;

            if (question.input) {
                userAnswer = parseFloat(document.getElementById('answer-input').value);
                isCorrect = Math.abs(userAnswer - question.correct) < 0.01;
            } else {
                userAnswer = window.selectedAnswer;
                isCorrect = userAnswer === question.correct;
            }

            if (isCorrect) {
                score += getDifficultyMultiplier();
            }

            answers.push({
                question: currentQuestion,
                userAnswer: userAnswer,
                correct: question.correct,
                isCorrect: isCorrect,
                explanation: question.explanation
            });

            currentQuestion++;
            showQuestion();
        }

        function getDifficultyMultiplier() {
            return {
                'normal': 1,
                'avance': 1.5,
                'expert': 2,
                'genius': 3
            }[difficulty];
        }

        function updateProgress() {
            const progress = (currentQuestion / totalQuestions) * 100;
            document.getElementById('progress').style.width = progress + '%';
            document.getElementById('question-counter').textContent = `Question ${currentQuestion + 1} / ${totalQuestions}`;
        }

        function showResults() {
            clearInterval(timer);
            const totalTime = Date.now() - startTime;
            const minutes = Math.floor(totalTime / 60000);
            
            // Calculer le QI basé sur le score, la difficulté et le temps
            const baseScore = (score / totalQuestions) * 100;
            const timeBonus = Math.max(0, 20 - minutes) * 2; // Bonus pour rapidité
            const difficultyBonus = {
                'normal': 0,
                'avance': 20,
                'expert': 40,
                'genius': 60
            }[difficulty];
            
            const finalQI = Math.round(80 + (baseScore * 0.8) + timeBonus + difficultyBonus);
            
            document.getElementById('test-container').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            document.getElementById('final-score').textContent = `QI: ${finalQI}`;
            
            let analysis = `
                <h3>📊 Analyse Détaillée</h3>
                <p><strong>Score brut:</strong> ${score}/${totalQuestions * getDifficultyMultiplier()} (${(baseScore).toFixed(1)}%)</p>
                <p><strong>Difficulté:</strong> ${difficulty.toUpperCase()} (+${difficultyBonus} points)</p>
                <p><strong>Temps:</strong> ${minutes} minutes (+${timeBonus} points bonus)</p>
                <p><strong>QI Final:</strong> ${finalQI}</p>
                
                <h4>🎯 Interprétation:</h4>
            `;
            
            if (finalQI >= 160) {
                analysis += '<p>🌟 <strong>Génie exceptionnel</strong> - Capacités intellectuelles extraordinaires</p>';
            } else if (finalQI >= 140) {
                analysis += '<p>🧠 <strong>Très supérieur</strong> - Intelligence remarquable</p>';
            } else if (finalQI >= 120) {
                analysis += '<p>📚 <strong>Supérieur</strong> - Excellentes capacités intellectuelles</p>';
            } else if (finalQI >= 110) {
                analysis += '<p>✅ <strong>Au-dessus de la moyenne</strong> - Bonnes capacités</p>';
            } else if (finalQI >= 90) {
                analysis += '<p>📊 <strong>Moyenne</strong> - Capacités normales</p>';
            } else {
                analysis += '<p>📈 <strong>Potentiel d\'amélioration</strong> - Continuez à vous entraîner</p>';
            }
            
            analysis += `
                <h4>📋 Détail des Réponses:</h4>
                <div style="max-height: 200px; overflow-y: auto;">
            `;
            
            answers.forEach((answer, index) => {
                const icon = answer.isCorrect ? '✅' : '❌';
                analysis += `
                    <p>${icon} Q${index + 1}: ${answer.isCorrect ? 'Correct' : 'Incorrect'}</p>
                    ${!answer.isCorrect ? `<small style="color: #ffab91;">Explication: ${answer.explanation}</small><br>` : ''}
                `;
            });
            
            analysis += '</div>';
            
            document.getElementById('analysis').innerHTML = analysis;
            
            // Sauvegarder le résultat
            window.testResult = {
                qi: finalQI,
                score: score,
                totalQuestions: totalQuestions,
                difficulty: difficulty,
                timeMinutes: minutes,
                answers: answers
            };
        }

        function recommencerTest() {
            currentQuestion = 0;
            score = 0;
            answers = [];
            startTime = Date.now();
            document.getElementById('results').style.display = 'none';
            document.getElementById('test-container').style.display = 'block';
            generateQuestions();
            startTimer();
        }

        async function envoyerResultats() {
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: `Test QI terminé ! Résultats: QI ${window.testResult.qi}, Score ${window.testResult.score}/${window.testResult.totalQuestions}, Difficulté ${window.testResult.difficulty}, Temps ${window.testResult.timeMinutes} minutes. Analyse ces résultats et donne ton avis sur mes capacités intellectuelles.`
                    })
                });
                
                if (response.ok) {
                    alert('✅ Résultats envoyés à LOUNA-AI avec succès !');
                } else {
                    alert('❌ Erreur lors de l\'envoi des résultats');
                }
            } catch (error) {
                alert('❌ Erreur de connexion');
            }
        }

        function retourAccueil() {
            window.location.href = '/';
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            generateQuestions();
            startTimer();
        });
    </script>
</body>
</html>
