#!/bin/bash

echo "🚀 LANCEMENT LOUNA-AI AVEC MÉMOIRE THERMIQUE"
echo "=============================================="

# Vérifier si Ollama est installé
if ! command -v ollama &> /dev/null; then
    echo "❌ Ollama n'est pas installé"
    echo "📥 Installation d'Ollama..."
    curl -fsSL https://ollama.ai/install.sh | sh
fi

# Vérifier si Python est installé
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 n'est pas installé"
    exit 1
fi

# Installer les dépendances Python
echo "📦 Installation des dépendances..."
pip3 install flask flask-socketio requests

# Démarrer Ollama en arrière-plan
echo "🔧 Démarrage du serveur Ollama..."
ollama serve &
OLLAMA_PID=$!

# Attendre que Ollama soit prêt
sleep 3

# Vérifier si le modèle Mistral est disponible
echo "🧠 Vérification du modèle avec mémoire thermique..."
if ! ollama list | grep -q "mistral"; then
    echo "📥 Téléchargement du modèle Mistral..."
    ollama pull mistral:latest
fi

# Configurer la mémoire thermique optimale
echo "🌡️ Configuration de la mémoire thermique..."
ollama run mistral:latest <<EOF
/set parameter temperature 0.05
/set parameter top_p 0.95
/set parameter repeat_penalty 1.15
/set parameter num_ctx 8192
/set parameter num_predict 2048
/bye
EOF

echo "✅ Configuration thermique appliquée"

# Lancer l'interface LOUNA-AI
echo "🌐 Lancement de l'interface LOUNA-AI..."
echo "📱 Interface disponible sur: http://localhost:5000"
echo "🧠 Agent avec mémoire thermique: ACTIF"
echo "=============================================="

python3 louna_connector.py

# Nettoyer à la sortie
trap "kill $OLLAMA_PID 2>/dev/null" EXIT
