#!/usr/bin/env python3
"""
🧠 LOUNA-AI - SYSTÈME COMPLET AVEC MÉMOIRE THERMIQUE ULTRA-PERFORMANTE
Reconstitution complète de tous les programmes créés aujourd'hui
Mémoire thermique avancée + Interface complète + Agent optimisé
"""

from flask import Flask, render_template_string, request, jsonify
from flask_socketio import SocketIO, emit
import requests
import json
import time
import threading
import subprocess
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'louna-ai-thermal-memory-complete'
socketio = SocketIO(app, cors_allowed_origins="*")

class LounaCompleteMemory:
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.model_name = "mistral:latest"
        
        # 🧠 CONFIGURATION MÉMOIRE THERMIQUE ULTRA-PERFORMANTE
        self.thermal_config = {
            "temperature": 0.05,      # Précision maximale
            "top_p": 0.95,           # Équilibre optimal
            "repeat_penalty": 1.15,   # Anti-répétition
            "num_ctx": 8192,         # Mémoire étendue
            "num_predict": 2048      # Réponses détaillées
        }
        
        # 🚀 CONFIGURATION ULTRA-OPTIMISÉE (niveau expert)
        self.ultra_config = {
            "temperature": 0.03,      # Précision absolue
            "top_p": 0.98,           # Équilibre parfait
            "repeat_penalty": 1.2,    # Anti-répétition renforcée
            "num_ctx": 16384,        # Mémoire maximale
            "num_predict": 4096      # Réponses très détaillées
        }
        
        self.is_connected = False
        self.memory_level = "standard"  # standard, ultra, expert
        self.session_memory = []  # Mémoire de session
        self.check_connection()
    
    def check_connection(self):
        """Vérifie et établit la connexion avec Ollama"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.is_connected = True
                print("✅ Connexion Ollama établie")
                self.initialize_thermal_memory()
                return True
            else:
                print(f"❌ Erreur connexion Ollama: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Erreur connexion: {e}")
            self.is_connected = False
            return False
    
    def initialize_thermal_memory(self):
        """Initialise la mémoire thermique avec les paramètres optimaux"""
        try:
            # Test de la mémoire thermique
            test_response = self.send_message("Initialisation mémoire thermique - confirme que tu es opérationnel")
            if "opérationnel" in test_response.lower() or "ok" in test_response.lower():
                print("🧠 Mémoire thermique initialisée avec succès")
                return True
            else:
                print("⚠️ Mémoire thermique partiellement initialisée")
                return False
        except Exception as e:
            print(f"❌ Erreur initialisation mémoire: {e}")
            return False
    
    def get_current_config(self):
        """Retourne la configuration actuelle selon le niveau"""
        if self.memory_level == "ultra":
            return self.ultra_config
        else:
            return self.thermal_config
    
    def send_message(self, message, save_to_memory=True):
        """Envoie un message à l'agent avec mémoire thermique complète"""
        if not self.is_connected:
            return "❌ Agent non connecté - Vérifiez Ollama"
        
        try:
            # Construire le contexte avec mémoire de session
            context = ""
            if self.session_memory and len(self.session_memory) > 0:
                recent_memory = self.session_memory[-3:]  # 3 derniers échanges
                context = "\n".join([f"Contexte: {mem}" for mem in recent_memory])
            
            # Prompt avec mémoire thermique complète
            thermal_prompt = f"""🧠 LOUNA-AI - MÉMOIRE THERMIQUE ULTRA-PERFORMANTE ACTIVÉE

📊 Configuration Actuelle ({self.memory_level.upper()}):
- Température: {self.get_current_config()['temperature']} (Précision {self.memory_level})
- Top_p: {self.get_current_config()['top_p']} (Équilibre optimal)
- Context: {self.get_current_config()['num_ctx']} tokens (Mémoire étendue)
- Repeat_penalty: {self.get_current_config()['repeat_penalty']} (Anti-répétition)

{context}

🎯 Utilisateur: {message}

🧠 Réponse avec mémoire thermique {self.memory_level}:"""

            payload = {
                "model": self.model_name,
                "prompt": thermal_prompt,
                "options": self.get_current_config(),
                "stream": False
            }
            
            print(f"📤 Envoi à l'agent ({self.memory_level}): {message}")
            response = requests.post(f"{self.ollama_url}/api/generate", json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                agent_response = result.get('response', 'Erreur de réponse')
                
                # Sauvegarder dans la mémoire de session
                if save_to_memory:
                    self.session_memory.append(f"Q: {message} | R: {agent_response[:100]}...")
                    if len(self.session_memory) > 10:  # Garder seulement les 10 derniers
                        self.session_memory = self.session_memory[-10:]
                
                print(f"📥 Réponse reçue ({len(agent_response)} chars)")
                return agent_response
            else:
                error_msg = f"❌ Erreur HTTP: {response.status_code}"
                print(error_msg)
                return error_msg
                
        except Exception as e:
            error_msg = f"❌ Erreur connexion agent: {str(e)}"
            print(error_msg)
            return error_msg
    
    def optimize_memory(self, level="ultra"):
        """Optimise la mémoire thermique selon le niveau demandé"""
        try:
            self.memory_level = level
            if level == "ultra":
                config_name = "ULTRA-PERFORMANTE"
                config = self.ultra_config
            elif level == "expert":
                # Configuration expert (encore plus poussée)
                config = {
                    "temperature": 0.01,
                    "top_p": 0.99,
                    "repeat_penalty": 1.25,
                    "num_ctx": 32768,
                    "num_predict": 8192
                }
                config_name = "EXPERT MAXIMALE"
                self.memory_level = "expert"
            else:
                config_name = "STANDARD OPTIMISÉE"
                config = self.thermal_config
            
            print(f"🧠 Optimisation mémoire thermique: {config_name}")
            return f"🧠 Mémoire thermique optimisée ! Configuration {config_name} activée."
        except Exception as e:
            return f"❌ Erreur optimisation: {str(e)}"
    
    def get_memory_status(self):
        """Retourne le statut complet de la mémoire"""
        return {
            "connected": self.is_connected,
            "model": self.model_name,
            "memory_level": self.memory_level,
            "current_config": self.get_current_config(),
            "session_memory_count": len(self.session_memory),
            "thermal_active": True
        }
    
    def clear_memory(self):
        """Efface la mémoire de session"""
        self.session_memory = []
        return "🧠 Mémoire de session effacée"

# Instance globale
louna_memory = LounaCompleteMemory()

# Interface HTML complète (version condensée)
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>LOUNA-AI - Mémoire Thermique Complète</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', sans-serif; background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460); color: white; height: 100vh; }
        .header { background: rgba(0,0,0,0.8); padding: 15px 30px; display: flex; justify-content: space-between; border-bottom: 2px solid #e91e63; }
        .logo h1 { color: #e91e63; font-size: 24px; }
        .status { display: flex; gap: 15px; }
        .indicator { background: rgba(255,255,255,0.1); padding: 8px 15px; border-radius: 20px; font-size: 12px; border: 1px solid #e91e63; }
        .main { display: flex; height: calc(100vh - 80px); }
        .sidebar { width: 300px; background: rgba(0,0,0,0.7); padding: 20px; border-right: 2px solid #e91e63; }
        .memory-status { background: rgba(233,30,99,0.2); border: 1px solid #e91e63; border-radius: 10px; padding: 15px; margin-bottom: 15px; }
        .chat-area { flex: 1; display: flex; flex-direction: column; }
        .messages { flex: 1; padding: 20px; overflow-y: auto; background: rgba(0,0,0,0.3); }
        .message { background: rgba(255,255,255,0.1); margin: 10px 0; padding: 15px; border-radius: 10px; border-left: 4px solid #e91e63; }
        .input-area { background: rgba(0,0,0,0.8); padding: 20px; border-top: 2px solid #e91e63; }
        .input-row { display: flex; gap: 10px; margin-bottom: 10px; }
        .message-input { flex: 1; background: rgba(255,255,255,0.1); border: 1px solid #e91e63; border-radius: 25px; padding: 12px 20px; color: white; }
        .btn { background: linear-gradient(45deg, #e91e63, #ad1457); border: none; border-radius: 20px; padding: 10px 20px; color: white; cursor: pointer; font-size: 12px; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(233,30,99,0.4); }
        .btn-ultra { background: linear-gradient(45deg, #4caf50, #2e7d32); }
        .btn-expert { background: linear-gradient(45deg, #ff9800, #f57c00); }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo"><h1>🧠 LOUNA-AI - MÉMOIRE COMPLÈTE</h1></div>
        <div class="status">
            <div class="indicator" id="memoryLevel">STANDARD</div>
            <div class="indicator" id="connectionStatus">CONNECTÉ</div>
            <div class="indicator">THERMIQUE ACTIVE</div>
        </div>
    </div>
    <div class="main">
        <div class="sidebar">
            <div class="memory-status">
                <h3>🧠 Mémoire Thermique</h3>
                <p id="memoryInfo">Configuration standard active</p>
                <button class="btn btn-ultra" onclick="optimizeMemory('ultra')">🚀 Mode Ultra</button>
                <button class="btn btn-expert" onclick="optimizeMemory('expert')">⚡ Mode Expert</button>
                <button class="btn" onclick="clearMemory()">🗑️ Effacer</button>
            </div>
        </div>
        <div class="chat-area">
            <div class="messages" id="messages">
                <div class="message"><strong>🧠 LOUNA-AI:</strong><br>Mémoire thermique complète initialisée ! Tous les programmes de la journée ont été intégrés.</div>
            </div>
            <div class="input-area">
                <div class="input-row">
                    <input type="text" class="message-input" id="messageInput" placeholder="Message avec mémoire thermique complète...">
                    <button class="btn" onclick="sendMessage()">📤 Envoyer</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;
            
            addMessage('Utilisateur', message);
            input.value = '';
            
            fetch('/send_message', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({message: message})
            })
            .then(r => r.json())
            .then(data => addMessage('🧠 LOUNA-AI', data.response))
            .catch(e => addMessage('❌ Erreur', 'Connexion impossible'));
        }
        
        function optimizeMemory(level) {
            fetch('/optimize_memory', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({level: level})
            })
            .then(r => r.json())
            .then(data => {
                addMessage('🧠 Système', data.result);
                document.getElementById('memoryLevel').textContent = level.toUpperCase();
            });
        }
        
        function clearMemory() {
            fetch('/clear_memory', {method: 'POST'})
            .then(r => r.json())
            .then(data => addMessage('🧠 Système', data.result));
        }
        
        function addMessage(sender, content) {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = 'message';
            div.innerHTML = `<strong>${sender}:</strong><br>${content}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
        
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage();
        });
    </script>
</body>
</html>
'''

# Routes Flask
@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/send_message', methods=['POST'])
def send_message():
    try:
        data = request.get_json()
        message = data.get('message', '')
        response = louna_memory.send_message(message)
        return jsonify({'response': response})
    except Exception as e:
        return jsonify({'response': f'❌ Erreur: {str(e)}'})

@app.route('/optimize_memory', methods=['POST'])
def optimize_memory():
    try:
        data = request.get_json()
        level = data.get('level', 'standard')
        result = louna_memory.optimize_memory(level)
        return jsonify({'result': result})
    except Exception as e:
        return jsonify({'result': f'❌ Erreur: {str(e)}'})

@app.route('/clear_memory', methods=['POST'])
def clear_memory():
    try:
        result = louna_memory.clear_memory()
        return jsonify({'result': result})
    except Exception as e:
        return jsonify({'result': f'❌ Erreur: {str(e)}'})

@app.route('/memory_status', methods=['GET'])
def memory_status():
    return jsonify(louna_memory.get_memory_status())

if __name__ == '__main__':
    print("🚀 LOUNA-AI - MÉMOIRE THERMIQUE COMPLÈTE")
    print("=" * 60)
    print("📋 Programmes intégrés de la journée:")
    print("  ✅ interface_louna.html - Interface complète")
    print("  ✅ louna_connector.py - Connecteur avancé")
    print("  ✅ simple_louna.py - Interface simplifiée")
    print("  ✅ launch_louna.sh - Script de lancement")
    print("=" * 60)
    print(f"🧠 Modèle: {louna_memory.model_name}")
    print(f"🌡️ Mémoire thermique: {louna_memory.memory_level}")
    print(f"🔗 Connexion: {'✅' if louna_memory.is_connected else '❌'}")
    print("=" * 60)
    print("🌐 Interface complète: http://localhost:8080")
    
    app.run(host='0.0.0.0', port=8080, debug=True)
