#!/usr/bin/env python3
"""
LOUNA-AI Connector - Interface avec Mémoire Thermique
Connecte l'interface web à votre agent Ollama avec configuration optimisée
"""

import requests
import json
import time
import threading
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import subprocess
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'louna-ai-thermal-memory'
socketio = SocketIO(app, cors_allowed_origins="*")

class LounaAIConnector:
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.model_name = "mistral:latest"  # Votre modèle avec mémoire thermique
        self.thermal_config = {
            "temperature": 0.05,
            "top_p": 0.95,
            "repeat_penalty": 1.15,
            "num_ctx": 8192,
            "num_predict": 2048
        }
        self.is_connected = False
        self.check_connection()
    
    def check_connection(self):
        """Vérifie la connexion avec Ollama"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags")
            if response.status_code == 200:
                self.is_connected = True
                print("✅ Connexion Ollama établie")
                return True
        except Exception as e:
            print(f"❌ Erreur connexion Ollama: {e}")
            self.is_connected = False
        return False
    
    def send_to_agent(self, message):
        """Envoie un message à votre agent avec mémoire thermique"""
        if not self.is_connected:
            return "❌ Agent non connecté"
        
        try:
            payload = {
                "model": self.model_name,
                "prompt": f"🧠 LOUNA-AI avec mémoire thermique ultra-performante:\n{message}",
                "options": self.thermal_config,
                "stream": False
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', 'Erreur de réponse')
            else:
                return f"❌ Erreur HTTP: {response.status_code}"
                
        except Exception as e:
            return f"❌ Erreur: {str(e)}"
    
    def optimize_thermal_memory(self):
        """Optimise la configuration de mémoire thermique"""
        try:
            # Configuration ultra-optimisée
            optimized_config = {
                "temperature": 0.03,  # Précision maximale
                "top_p": 0.98,        # Équilibre parfait
                "repeat_penalty": 1.2, # Anti-répétition renforcée
                "num_ctx": 16384,     # Mémoire étendue maximale
                "num_predict": 4096   # Réponses très détaillées
            }
            
            self.thermal_config.update(optimized_config)
            return "🧠 Mémoire thermique optimisée ! Configuration ultra-performante activée."
        except Exception as e:
            return f"❌ Erreur optimisation: {str(e)}"

# Instance globale du connecteur
louna = LounaAIConnector()

@app.route('/')
def index():
    """Page principale de l'interface"""
    return open('interface_louna.html').read()

@socketio.on('connect')
def handle_connect():
    """Gestion des connexions WebSocket"""
    print('🔌 Client connecté à LOUNA-AI')
    emit('status', {
        'connected': louna.is_connected,
        'model': louna.model_name,
        'thermal_config': louna.thermal_config
    })

@socketio.on('send_message')
def handle_message(data):
    """Traite les messages envoyés à l'agent"""
    message = data.get('message', '')
    print(f"📨 Message reçu: {message}")
    
    # Envoyer à votre agent avec mémoire thermique
    response = louna.send_to_agent(message)
    
    emit('agent_response', {
        'message': message,
        'response': response,
        'timestamp': time.time(),
        'thermal_active': True
    })

@socketio.on('optimize_memory')
def handle_optimize():
    """Optimise la mémoire thermique"""
    result = louna.optimize_thermal_memory()
    emit('optimization_result', {
        'result': result,
        'new_config': louna.thermal_config
    })

@socketio.on('execute_action')
def handle_action(data):
    """Exécute une action spécifique"""
    action = data.get('action', '')
    
    action_responses = {
        'WiFi': '📶 Connexion WiFi optimisée avec mémoire thermique',
        'Bluetooth': '🔵 Bluetooth activé - Protocole avancé',
        'AirDrop': '📡 AirDrop configuré pour transferts haute performance',
        'Micro': '🎤 Microphone calibré - Reconnaissance vocale avancée',
        'Présentation': '📊 Mode présentation activé avec IA',
        'Générateur Vidéo': '🎬 Générateur vidéo IA initialisé',
        'Config': '⚙️ Configuration système optimisée'
    }
    
    response = action_responses.get(action, f'Action {action} exécutée')
    
    # Envoyer aussi à votre agent pour traitement avancé
    agent_response = louna.send_to_agent(f"Exécuter action: {action}")
    
    emit('action_result', {
        'action': action,
        'response': response,
        'agent_response': agent_response
    })

def start_ollama_if_needed():
    """Démarre Ollama si nécessaire"""
    try:
        subprocess.run(['ollama', 'serve'], check=False, capture_output=True)
    except:
        pass

if __name__ == '__main__':
    print("🚀 Démarrage LOUNA-AI avec Mémoire Thermique")
    print("=" * 50)
    print(f"🧠 Modèle: {louna.model_name}")
    print(f"🌡️ Configuration thermique: {louna.thermal_config}")
    print(f"🔗 Connexion Ollama: {'✅' if louna.is_connected else '❌'}")
    print("=" * 50)
    
    # Démarrer Ollama en arrière-plan si nécessaire
    threading.Thread(target=start_ollama_if_needed, daemon=True).start()
    
    # Lancer l'interface web
    print("🌐 Interface disponible sur: http://localhost:8080")
    socketio.run(app, host='0.0.0.0', port=8080, debug=True)
