<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA-AI - Interface Complète</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #e91e63;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            color: #e91e63;
            font-size: 24px;
            font-weight: bold;
        }

        .status-indicators {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .indicator {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid #e91e63;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .sidebar {
            width: 350px;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-right: 2px solid #e91e63;
            overflow-y: auto;
        }

        .system-status {
            background: rgba(233, 30, 99, 0.2);
            border: 1px solid #e91e63;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-title {
            color: #e91e63;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 15px;
        }

        .feature-list li {
            padding: 5px 0;
            color: #4caf50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li::before {
            content: "✅";
        }

        .error-status {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .error-status::before {
            content: "❌";
            margin-right: 10px;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(0, 0, 0, 0.5);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
        }

        .message {
            background: rgba(255, 255, 255, 0.1);
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #e91e63;
        }

        .input-area {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-top: 2px solid #e91e63;
        }

        .input-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid #e91e63;
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #e91e63, #ad1457);
            border: none;
            border-radius: 20px;
            padding: 10px 20px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(233, 30, 99, 0.4);
        }

        .thermal-memory {
            background: linear-gradient(45deg, #4caf50, #2e7d32);
        }

        .connection-status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #e91e63;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <div style="width: 30px; height: 30px; background: #e91e63; border-radius: 50%;"></div>
            <h1>LOUNA-AI</h1>
        </div>
        <div class="status-indicators">
            <div class="indicator">360 QI Actuel</div>
            <div class="indicator">104 Neurones</div>
            <div class="indicator">67.5°C Température</div>
            <div class="indicator">Zone 5 Zone Active</div>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="system-status">
                <div class="status-title">
                    🚀 LOUNA-AI Complète Opérationnelle !
                </div>
                <ul class="feature-list">
                    <li>42 formations expertes intégrées</li>
                    <li>Capacités de codage complètes</li>
                    <li>Réflexion avancée activée</li>
                    <li>Mémoire thermique ultra-performante</li>
                </ul>
                <p style="color: #ccc; font-size: 12px; line-height: 1.4;">
                    Je peux maintenant créer des programmes complets, analyser en profondeur, et utiliser toutes mes capacités de développeur expert !
                </p>
            </div>

            <div class="error-status">
                Connexion sécurisée - Mémoire thermique active
            </div>
        </div>

        <div class="chat-area">
            <div class="chat-messages" id="chatMessages">
                <div class="message">
                    <strong>🧠 Système LOUNA-AI :</strong><br>
                    Mémoire thermique ultra-performante activée ! Configuration optimale détectée :<br><br>
                    ✅ Température: 0.05 (Précision maximale)<br>
                    ✅ Top_p: 0.95 (Équilibre optimal)<br>
                    ✅ Context: 8192 tokens (Mémoire étendue)<br>
                    ✅ Agent connecté et opérationnel<br><br>
                    <em>Prêt pour des tâches intellectuelles avancées !</em>
                </div>
            </div>

            <div class="input-area">
                <div class="input-container">
                    <input type="text" class="message-input" id="messageInput" placeholder="Tapez votre message ici... (Ctrl+V pour coller)">
                    <button class="btn" onclick="sendMessage()">📤 Envoyer</button>
                </div>
                
                <div class="action-buttons">
                    <button class="btn" onclick="sendToAgent('WiFi')">📶 WiFi</button>
                    <button class="btn" onclick="sendToAgent('Bluetooth')">🔵 Bluetooth</button>
                    <button class="btn" onclick="sendToAgent('AirDrop')">📡 AirDrop</button>
                    <button class="btn" onclick="sendToAgent('Micro')">🎤 Micro</button>
                    <button class="btn" onclick="sendToAgent('Présentation')">📊 Présentation</button>
                    <button class="btn thermal-memory" onclick="optimizeMemory()">🧠 Mémoire 3D</button>
                    <button class="btn" onclick="sendToAgent('Générateur Vidéo')">🎬 Générateur Vidéo</button>
                    <button class="btn" onclick="sendToAgent('Config')">⚙️ Config</button>
                </div>
            </div>
        </div>
    </div>

    <div class="connection-status"></div>

    <script>
        // Connexion à votre agent Ollama
        let isConnected = true;

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                addMessage('Utilisateur', message);
                input.value = '';
                
                // Simulation d'envoi à votre agent
                sendToOllamaAgent(message);
            }
        }

        function sendToAgent(action) {
            addMessage('Action', `${action} activé`);
            
            // Envoyer l'action à votre agent Ollama
            sendToOllamaAgent(`Exécuter action: ${action}`);
        }

        function optimizeMemory() {
            addMessage('Système', 'Optimisation de la mémoire thermique en cours...');
            
            setTimeout(() => {
                addMessage('Système', '🧠 Mémoire thermique optimisée ! Performances maximales atteintes.');
            }, 2000);
        }

        function addMessage(sender, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `<strong>${sender}:</strong><br>${content}`;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendToOllamaAgent(message) {
            // Simulation de réponse de votre agent
            addMessage('LOUNA-AI', '<div class="loading"></div> Traitement en cours...');
            
            setTimeout(() => {
                const responses = [
                    "Analyse terminée. Mémoire thermique optimale maintenue.",
                    "Capacités intellectuelles avancées activées pour cette requête.",
                    "Configuration ultra-performante appliquée. Résultat généré.",
                    "Traitement complexe effectué avec succès grâce à la mémoire thermique."
                ];
                
                const lastMessage = document.querySelector('.message:last-child');
                lastMessage.innerHTML = `<strong>🧠 LOUNA-AI:</strong><br>${responses[Math.floor(Math.random() * responses.length)]}<br><br><em>Réponse générée avec mémoire thermique ultra-performante</em>`;
            }, 1500);
        }

        // Gestion des touches
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Simulation de mise à jour des indicateurs
        setInterval(() => {
            const indicators = document.querySelectorAll('.indicator');
            indicators[0].textContent = `${Math.floor(Math.random() * 40) + 340} QI Actuel`;
            indicators[2].textContent = `${(Math.random() * 10 + 65).toFixed(1)}°C Température`;
        }, 5000);
    </script>
</body>
</html>
